<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/CpclCmd.h</key>
		<data>
		CzA6X4KR5jxMw1p00Y0OpEQwHh0=
		</data>
		<key>Headers/EscCmd.h</key>
		<data>
		jRprcyj15TBKXYXjpj6NdwifYsM=
		</data>
		<key>Headers/KBlue.h</key>
		<data>
		ffHZjc7cxNrYT6m0Aj/nB7IgoJo=
		</data>
		<key>Headers/KEthernet.h</key>
		<data>
		DJz0rxhF/mfVHdUB79/Es0sc3NY=
		</data>
		<key>Headers/KPrinter.h</key>
		<data>
		bvJk5GKfAXjKOq4RgBsoOmslBcI=
		</data>
		<key>Headers/TscCmd.h</key>
		<data>
		HChPEJaJKuOUqMWY+Y2MncxGW3U=
		</data>
		<key>Info.plist</key>
		<data>
		TKBASRRwc3DY2Z24YZQthCbOqhE=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		OgYm8Nk7X7WNyH7WwBDOjuTe4ZI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/CpclCmd.h</key>
		<dict>
			<key>hash</key>
			<data>
			CzA6X4KR5jxMw1p00Y0OpEQwHh0=
			</data>
			<key>hash2</key>
			<data>
			um71UIPjqLwYKx0Zh2FJ0fHXM8mDuiNnfc+tLRZDwA8=
			</data>
		</dict>
		<key>Headers/EscCmd.h</key>
		<dict>
			<key>hash</key>
			<data>
			jRprcyj15TBKXYXjpj6NdwifYsM=
			</data>
			<key>hash2</key>
			<data>
			3xWfll6LynQSPxXdnQ9xfurcANOBUZ+nNMU+j4hjRi4=
			</data>
		</dict>
		<key>Headers/KBlue.h</key>
		<dict>
			<key>hash</key>
			<data>
			ffHZjc7cxNrYT6m0Aj/nB7IgoJo=
			</data>
			<key>hash2</key>
			<data>
			Eu3zpso88v8AOfJ4J36ND2e39mPoPrus+7KEy0y07SE=
			</data>
		</dict>
		<key>Headers/KEthernet.h</key>
		<dict>
			<key>hash</key>
			<data>
			DJz0rxhF/mfVHdUB79/Es0sc3NY=
			</data>
			<key>hash2</key>
			<data>
			/s82qgVYe0XeBca9shlTbathGPcgqvyXkCvflLduxdk=
			</data>
		</dict>
		<key>Headers/KPrinter.h</key>
		<dict>
			<key>hash</key>
			<data>
			bvJk5GKfAXjKOq4RgBsoOmslBcI=
			</data>
			<key>hash2</key>
			<data>
			PyRdAeAVkiPcxF08bzxGi9kIzuMEPVBBqOLTWdOtDts=
			</data>
		</dict>
		<key>Headers/TscCmd.h</key>
		<dict>
			<key>hash</key>
			<data>
			HChPEJaJKuOUqMWY+Y2MncxGW3U=
			</data>
			<key>hash2</key>
			<data>
			uSnVhPeM19aSjLbU7sInBfI9uVpsoez1q79vKn/0bYY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			OgYm8Nk7X7WNyH7WwBDOjuTe4ZI=
			</data>
			<key>hash2</key>
			<data>
			mSoOqijvyp1qFH37WmNKSWAsZbRXtGdhrdRoT57V4b8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
