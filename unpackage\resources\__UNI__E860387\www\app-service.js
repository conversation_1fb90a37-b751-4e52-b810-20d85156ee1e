if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const r=this.constructor;return this.then((t=>r.resolve(e()).then((()=>t))),(t=>r.resolve(e()).then((()=>{throw t}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64Array,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";const r=(e,r)=>{const t=e.__vccOpts||e;for(const[a,n]of r)t[a]=n;return t};const t=r({data:()=>({username:"user01",password:"123456",users:[]}),onLoad(){const e=uni.getStorageSync("users");Array.isArray(e)&&(this.users=e)},methods:{onLogin(){if(this.username&&this.password)return"admin"===this.username&&"admin"===this.password?(uni.showToast({title:"上传学校信息",icon:"success"}),void uni.navigateTo({url:"/pages/school_updata/school_updata"})):"updata_food"===this.username&&"123456"===this.password?(uni.showToast({title:"上传食谱信息",icon:"success"}),void uni.navigateTo({url:"/pages/food_updata/food_updata"})):void("user01"===this.username&&"123456"===this.password?(uni.showToast({title:"用户登录成功",icon:"success"}),uni.navigateTo({url:"/pages/Module/Module"})):uni.showToast({title:"账号或密码错误",icon:"none"}));uni.showToast({title:"请输入账号和密码",icon:"none"})}}},[["render",function(r,t,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"login-card"},[e.createElementVNode("text",{class:"login-title"},"SoHe留样软件平台"),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("image",{src:"/static/user-icon.png",class:"input-icon"}),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":t[0]||(t[0]=e=>s.username=e),placeholder:"请输入账号","placeholder-class":"placeholder","confirm-type":"done"},null,512),[[e.vModelText,s.username,void 0,{trim:!0}]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("image",{src:"/static/lock-icon.png",class:"input-icon"}),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":t[1]||(t[1]=e=>s.password=e),type:"password",placeholder:"请输入密码","placeholder-class":"placeholder","confirm-type":"done"},null,512),[[e.vModelText,s.password,void 0,{trim:!0}]])]),e.createElementVNode("button",{class:"login-button",onClick:t[2]||(t[2]=(...e)=>i.onLogin&&i.onLogin(...e))},"登录")])])}],["__scopeId","data-v-5bf28d7d"]]);function a(e){return weex.requireModule(e)}function n(e,r,...t){uni.__log__?uni.__log__(e,r,...t):console[e].apply(console,[...t,r])}const s=uni.requireUTSPlugin("uni_modules/mushan-uvccamera"),i=r({__name:"index",setup(r){function t(){s.getUvcCamera((e=>{n("log","at pages/index/index.vue:50",JSON.parse(e))}))}async function a(e){return new Promise(((r,t)=>{let i={textData:[{content:"文本1",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:50},{content:"文本2",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:100}],imageData:[{url:"static/logo.png",width:50,height:50,topMargin:50,leftMargin:50},{url:"static/logo.png",width:50,height:50,topMargin:100,leftMargin:200}],vendorId:e,widthView:.5,heightView:.5,topMargin:.5,leftMargin:0,quirkFixBandwidth:!1};s.openUvcCamera(i,(t=>{let i=JSON.parse(t);n("log","at pages/index/index.vue:138",i),"ok"==i.msg?(s.clickUvcCameraImage({vendorId:e,imageNum:0},(e=>{n("log","at pages/index/index.vue:146","点击了第1张图片")})),s.clickUvcCameraImage({vendorId:e,imageNum:1},(e=>{n("log","at pages/index/index.vue:154","点击了第2张图片")})),r()):"用户拒绝了部分权限"==i.msg&&a()}))}))}async function i(){return new Promise(((e,r)=>{uni.getSystemInfo({success:function(r){let t=r.screenWidth,i=r.screenHeight;uni.createSelectorQuery().select(".sssAD").boundingClientRect().exec((r=>{let c={textData:[],imageData:[],vendorId:3141,widthView:1*(r[0].width/t).toFixed(2),heightView:1*(r[0].height/i).toFixed(2),topMargin:1*(r[0].top/i).toFixed(2),leftMargin:1*(r[0].left/t).toFixed(2),quirkFixBandwidth:!1};s.openUvcCamera(c,(r=>{let t=JSON.parse(r);n("log","at pages/index/index.vue:195",t),"ok"==t.msg?e():"用户拒绝了部分权限"==t.msg&&a(),e()}))}))}})}))}e.ref(null);let c=e.ref("");async function o(){var e;await(e=3141,new Promise(((r,t)=>{s.closedUvcCamera(e,(e=>{"ok"==JSON.parse(e).msg&&r()}))})))}return(r,a)=>(e.openBlock(),e.createElementBlock("view",null,[e.createElementVNode("button",{onClick:t},"获取当前UVC设备列表"),e.createElementVNode("button",{onClick:i},"打开UVC视频设备"),e.createElementVNode("button",{onClick:a[0]||(a[0]=e=>async function(e){return new Promise(((r,t)=>{let a={vendorId:e,someQuality:40,isBase64:!0};s.getUvcCameraImg(a,(e=>{let r=JSON.parse(e);"ok"==r.msg&&(n("log","at pages/index/index.vue:231",r),n("log","at pages/index/index.vue:232",r.file),c.value=r.base64.replace(/[\r\n]/g,""))}))}))}(3141))},"UVC设备拍照"),e.createElementVNode("button",{onClick:o},"关闭UVC设备拍照"),e.createElementVNode("image",{src:e.unref(c),mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"sssAD"})]))}},[["__scopeId","data-v-58d91a0a"]]);var c=1252,o=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],l={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},f=function(e){-1!=o.indexOf(e)&&(c=l[0]=e)};var h=function(e){f(e)};function u(){h(1200),f(1252)}function d(e){for(var r=[],t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function p(e){for(var r=[],t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var m,g=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);return 255==r&&254==t?function(e){for(var r=[],t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t)+(e.charCodeAt(2*t+1)<<8));return r.join("")}(e.slice(2)):254==r&&255==t?p(e.slice(2)):65279==r?e.slice(1):e},v=function(e){return String.fromCharCode(e)},b=function(e){return String.fromCharCode(e)},T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function E(e){for(var r="",t=0,a=0,n=0,s=0,i=0,c=0,o=0,l=0;l<e.length;)s=(t=e.charCodeAt(l++))>>2,i=(3&t)<<4|(a=e.charCodeAt(l++))>>4,c=(15&a)<<2|(n=e.charCodeAt(l++))>>6,o=63&n,isNaN(a)?c=o=64:isNaN(n)&&(o=64),r+=T.charAt(s)+T.charAt(i)+T.charAt(c)+T.charAt(o);return r}function w(e){var r="",t=0,a=0,n=0,s=0,i=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)t=T.indexOf(e.charAt(o++))<<2|(s=T.indexOf(e.charAt(o++)))>>4,r+=String.fromCharCode(t),a=(15&s)<<4|(i=T.indexOf(e.charAt(o++)))>>2,64!==i&&(r+=String.fromCharCode(a)),n=(3&i)<<6|(c=T.indexOf(e.charAt(o++))),64!==c&&(r+=String.fromCharCode(n));return r}var A=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),S=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(r){e=!0}return e?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function k(e){return A?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function y(e){return A?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var _=function(e){return A?S(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function C(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var r=[],t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function x(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return x(new Uint8Array(e));for(var r=new Array(e.length),t=0;t<e.length;++t)r[t]=e[t];return r}var O=A?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:S(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var r=0,t=0;for(r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t),n=0;for(r=0,t=0;r<e.length;t+=n,++r)if(n=e[r].length,e[r]instanceof Uint8Array)a.set(e[r],t);else{if("string"==typeof e[r])throw"wtf";a.set(new Uint8Array(e[r]),t)}return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var R=/\u0000/g,I=/[\u0001-\u0006]/g;function N(e){for(var r="",t=e.length-1;t>=0;)r+=e.charAt(t--);return r}function D(e,r){var t=""+e;return t.length>=r?t:We("0",r-t.length)+t}function F(e,r){var t=""+e;return t.length>=r?t:We(" ",r-t.length)+t}function P(e,r){var t=""+e;return t.length>=r?t:t+We(" ",r-t.length)}var M=Math.pow(2,32);function L(e,r){return e>M||e<-M?function(e,r){var t=""+Math.round(e);return t.length>=r?t:We("0",r-t.length)+t}(e,r):function(e,r){var t=""+e;return t.length>=r?t:We("0",r-t.length)+t}(Math.round(e),r)}function U(e,r){return r=r||0,e.length>=7+r&&103==(32|e.charCodeAt(r))&&101==(32|e.charCodeAt(r+1))&&110==(32|e.charCodeAt(r+2))&&101==(32|e.charCodeAt(r+3))&&114==(32|e.charCodeAt(r+4))&&97==(32|e.charCodeAt(r+5))&&108==(32|e.charCodeAt(r+6))}var B=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var H={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},W={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},z={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function G(e,r,t){for(var a=e<0?-1:1,n=e*a,s=0,i=1,c=0,o=1,l=0,f=0,h=Math.floor(n);l<r&&(c=(h=Math.floor(n))*i+s,f=h*l+o,!(n-h<5e-8));)n=1/(n-h),s=i,i=c,o=l,l=f;if(f>r&&(l>r?(f=o,c=s):(f=l,c=i)),!t)return[0,a*c,f];var u=Math.floor(a*c/f);return[u,a*c-u*f,f]}function j(e,r,t){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],c={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),r&&r.date1904&&(a+=1462),c.u>.9999&&(c.u=0,86400==++n&&(c.T=n=0,++a,++c.D)),60===a)i=t?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=t?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var o=new Date(1900,0,1);o.setDate(o.getDate()+a-1),i=[o.getFullYear(),o.getMonth()+1,o.getDate()],s=o.getDay(),a<60&&(s=(s+6)%7),t&&(s=function(e,r){r[0]-=581;var t=e.getDay();e<60&&(t=(t+6)%7);return t}(o,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=n%60,n=Math.floor(n/60),c.M=n%60,n=Math.floor(n/60),c.H=n,c.q=s,c}var X=new Date(1899,11,31,0,0,0),Y=X.getTime(),K=new Date(1900,2,1,0,0,0);function J(e,r){var t=e.getTime();return r?t-=1262304e5:e>=K&&(t+=864e5),(t-(Y+6e4*(e.getTimezoneOffset()-X.getTimezoneOffset())))/864e5}function q(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Z(e){var r,t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return r=t>=-4&&t<=-1?e.toPrecision(10+t):Math.abs(t)<=9?function(e){var r=e<0?12:11,t=q(e.toFixed(12));return t.length<=r||(t=e.toPrecision(10)).length<=r?t:e.toExponential(5)}(e):10===t?e.toFixed(10).substr(0,12):function(e){var r=q(e.toFixed(11));return r.length>(e<0?12:11)||"0"===r||"-0"===r?e.toPrecision(6):r}(e),q(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(r.toUpperCase()))}function Q(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):Z(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Ee(14,J(e,r&&r.date1904),r)}throw new Error("unsupported value in General format: "+e)}function ee(e,r,t,a){var n,s="",i=0,c=0,o=t.y,l=0;switch(e){case 98:o=t.y+543;case 121:switch(r.length){case 1:case 2:n=o%100,l=2;break;default:n=o%1e4,l=4}break;case 109:switch(r.length){case 1:case 2:n=t.m,l=r.length;break;case 3:return V[t.m-1][1];case 5:return V[t.m-1][0];default:return V[t.m-1][2]}break;case 100:switch(r.length){case 1:case 2:n=t.d,l=r.length;break;case 3:return B[t.q][0];default:return B[t.q][1]}break;case 104:switch(r.length){case 1:case 2:n=1+(t.H+11)%12,l=r.length;break;default:throw"bad hour format: "+r}break;case 72:switch(r.length){case 1:case 2:n=t.H,l=r.length;break;default:throw"bad hour format: "+r}break;case 77:switch(r.length){case 1:case 2:n=t.M,l=r.length;break;default:throw"bad minute format: "+r}break;case 115:if("s"!=r&&"ss"!=r&&".0"!=r&&".00"!=r&&".000"!=r)throw"bad second format: "+r;return 0!==t.u||"s"!=r&&"ss"!=r?(c=a>=2?3===a?1e3:100:1===a?10:1,(i=Math.round(c*(t.S+t.u)))>=60*c&&(i=0),"s"===r?0===i?"0":""+i/c:(s=D(i,2+a),"ss"===r?s.substr(0,2):"."+s.substr(2,r.length-1))):D(t.S,r.length);case 90:switch(r){case"[h]":case"[hh]":n=24*t.D+t.H;break;case"[m]":case"[mm]":n=60*(24*t.D+t.H)+t.M;break;case"[s]":case"[ss]":n=60*(60*(24*t.D+t.H)+t.M)+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r}l=3===r.length?1:2;break;case 101:n=o,l=1}return l>0?D(n,l):""}function re(e){if(e.length<=3)return e;for(var r=e.length%3,t=e.substr(0,r);r!=e.length;r+=3)t+=(t.length>0?",":"")+e.substr(r,3);return t}var te=/%/g;function ae(e,r){var t,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+ae(e,-r);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(t=(r/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).indexOf("e")){var i=Math.floor(Math.log(r)*Math.LOG10E);for(-1===t.indexOf(".")?t=t.charAt(0)+"."+t.substr(1)+"E+"+(i-t.length+s):t+="E+"+(i-s);"0."===t.substr(0,2);)t=(t=t.charAt(0)+t.substr(2,n)+"."+t.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,r,t,a){return r+t+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else t=r.toExponential(a);return e.match(/E\+00$/)&&t.match(/e[+-]\d$/)&&(t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1)),e.match(/E\-/)&&t.match(/e\+/)&&(t=t.replace(/e\+/,"e")),t.replace("e","E")}var ne=/# (\?+)( ?)\/( ?)(\d+)/;var se=/^#*0*\.([0#]+)/,ie=/\).*[0#]/,ce=/\(###\) ###\\?-####/;function oe(e){for(var r,t="",a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function le(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function fe(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);return r<(""+Math.round(t*a)).length?0:Math.round(t*a)}function he(e,r,t){if(40===e.charCodeAt(0)&&!r.match(ie)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return t>=0?he("n",a,t):"("+he("n",a,-t)+")"}if(44===r.charCodeAt(r.length-1))return function(e,r,t){for(var a=r.length-1;44===r.charCodeAt(a-1);)--a;return pe(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}(e,r,t);if(-1!==r.indexOf("%"))return function(e,r,t){var a=r.replace(te,""),n=r.length-a.length;return pe(e,a,t*Math.pow(10,2*n))+We("%",n)}(e,r,t);if(-1!==r.indexOf("E"))return ae(r,t);if(36===r.charCodeAt(0))return"$"+he(e,r.substr(" "==r.charAt(1)?2:1),t);var n,s,i,c,o=Math.abs(t),l=t<0?"-":"";if(r.match(/^00+$/))return l+L(o,r.length);if(r.match(/^[#?]+$/))return"0"===(n=L(t,0))&&(n=""),n.length>r.length?n:oe(r.substr(0,r.length-n.length))+n;if(s=r.match(ne))return function(e,r,t){var a=parseInt(e[4],10),n=Math.round(r*a),s=Math.floor(n/a),i=n-s*a,c=a;return t+(0===s?"":""+s)+" "+(0===i?We(" ",e[1].length+1+e[4].length):F(i,e[1].length)+e[2]+"/"+e[3]+D(c,e[4].length))}(s,o,l);if(r.match(/^#+0+$/))return l+L(o,r.length-r.indexOf("0"));if(s=r.match(se))return n=le(t,s[1].length).replace(/^([^\.]+)$/,"$1."+oe(s[1])).replace(/\.$/,"."+oe(s[1])).replace(/\.(\d*)$/,(function(e,r){return"."+r+We("0",oe(s[1]).length-r.length)})),-1!==r.indexOf("0.")?n:n.replace(/^0\./,".");if(r=r.replace(/^#+([0.])/,"$1"),s=r.match(/^(0*)\.(#*)$/))return l+le(o,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=r.match(/^#{1,3},##0(\.?)$/))return l+re(L(o,0));if(s=r.match(/^#,##0\.([#0]*0)$/))return t<0?"-"+he(e,r,-t):re(""+(Math.floor(t)+function(e,r){return r<(""+Math.round((e-Math.floor(e))*Math.pow(10,r))).length?1:0}(t,s[1].length)))+"."+D(fe(t,s[1].length),s[1].length);if(s=r.match(/^#,#*,#0/))return he(e,r.replace(/^#,#*,/,""),t);if(s=r.match(/^([0#]+)(\\?-([0#]+))+$/))return n=N(he(e,r.replace(/[\\-]/g,""),t)),i=0,N(N(r.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(r.match(ce))return"("+(n=he(e,"##########",t)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=G(o,Math.pow(10,i)-1,!1),n=""+l," "==(f=pe("n",s[1],c[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=P(c[2],i)).length<s[4].length&&(f=oe(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((c=G(o,Math.pow(10,i)-1,!0))[0]||(c[1]?"":"0"))+" "+(c[1]?F(c[1],i)+s[2]+"/"+s[3]+P(c[2],i):We(" ",2*i+1+s[2].length+s[3].length));if(s=r.match(/^[#0?]+$/))return n=L(t,0),r.length<=n.length?n:oe(r.substr(0,r.length-n.length))+n;if(s=r.match(/^([#0?]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=r.indexOf(".")-i,u=r.length-n.length-h;return oe(r.substr(0,h)+n+r.substr(r.length-u))}if(s=r.match(/^00,000\.([#0]*0)$/))return i=fe(t,s[1].length),t<0?"-"+he(e,r,-t):re(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(t)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?D(0,3-e.length):"")+e}))+"."+D(i,s[1].length);switch(r){case"###,##0.00":return he(e,"#,##0.00",t);case"###,###":case"##,###":case"#,###":var d=re(L(o,0));return"0"!==d?l+d:"";case"###,###.00":return he(e,"###,##0.00",t).replace(/^0\./,".");case"#,###.00":return he(e,"#,##0.00",t).replace(/^0\./,".")}throw new Error("unsupported format |"+r+"|")}function ue(e,r){var t,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+ue(e,-r);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%n;if(s<0&&(s+=n),!(t=(r/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).match(/[Ee]/)){var i=Math.floor(Math.log(r)*Math.LOG10E);-1===t.indexOf(".")?t=t.charAt(0)+"."+t.substr(1)+"E+"+(i-t.length+s):t+="E+"+(i-s),t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,r,t,a){return r+t+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else t=r.toExponential(a);return e.match(/E\+00$/)&&t.match(/e[+-]\d$/)&&(t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1)),e.match(/E\-/)&&t.match(/e\+/)&&(t=t.replace(/e\+/,"e")),t.replace("e","E")}function de(e,r,t){if(40===e.charCodeAt(0)&&!r.match(ie)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return t>=0?de("n",a,t):"("+de("n",a,-t)+")"}if(44===r.charCodeAt(r.length-1))return function(e,r,t){for(var a=r.length-1;44===r.charCodeAt(a-1);)--a;return pe(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}(e,r,t);if(-1!==r.indexOf("%"))return function(e,r,t){var a=r.replace(te,""),n=r.length-a.length;return pe(e,a,t*Math.pow(10,2*n))+We("%",n)}(e,r,t);if(-1!==r.indexOf("E"))return ue(r,t);if(36===r.charCodeAt(0))return"$"+de(e,r.substr(" "==r.charAt(1)?2:1),t);var n,s,i,c,o=Math.abs(t),l=t<0?"-":"";if(r.match(/^00+$/))return l+D(o,r.length);if(r.match(/^[#?]+$/))return n=""+t,0===t&&(n=""),n.length>r.length?n:oe(r.substr(0,r.length-n.length))+n;if(s=r.match(ne))return function(e,r,t){return t+(0===r?"":""+r)+We(" ",e[1].length+2+e[4].length)}(s,o,l);if(r.match(/^#+0+$/))return l+D(o,r.length-r.indexOf("0"));if(s=r.match(se))return n=(n=(""+t).replace(/^([^\.]+)$/,"$1."+oe(s[1])).replace(/\.$/,"."+oe(s[1]))).replace(/\.(\d*)$/,(function(e,r){return"."+r+We("0",oe(s[1]).length-r.length)})),-1!==r.indexOf("0.")?n:n.replace(/^0\./,".");if(r=r.replace(/^#+([0.])/,"$1"),s=r.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=r.match(/^#{1,3},##0(\.?)$/))return l+re(""+o);if(s=r.match(/^#,##0\.([#0]*0)$/))return t<0?"-"+de(e,r,-t):re(""+t)+"."+We("0",s[1].length);if(s=r.match(/^#,#*,#0/))return de(e,r.replace(/^#,#*,/,""),t);if(s=r.match(/^([0#]+)(\\?-([0#]+))+$/))return n=N(de(e,r.replace(/[\\-]/g,""),t)),i=0,N(N(r.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(r.match(ce))return"("+(n=de(e,"##########",t)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=G(o,Math.pow(10,i)-1,!1),n=""+l," "==(f=pe("n",s[1],c[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=P(c[2],i)).length<s[4].length&&(f=oe(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((c=G(o,Math.pow(10,i)-1,!0))[0]||(c[1]?"":"0"))+" "+(c[1]?F(c[1],i)+s[2]+"/"+s[3]+P(c[2],i):We(" ",2*i+1+s[2].length+s[3].length));if(s=r.match(/^[#0?]+$/))return n=""+t,r.length<=n.length?n:oe(r.substr(0,r.length-n.length))+n;if(s=r.match(/^([#0]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=r.indexOf(".")-i,u=r.length-n.length-h;return oe(r.substr(0,h)+n+r.substr(r.length-u))}if(s=r.match(/^00,000\.([#0]*0)$/))return t<0?"-"+de(e,r,-t):re(""+t).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?D(0,3-e.length):"")+e}))+"."+D(0,s[1].length);switch(r){case"###,###":case"##,###":case"#,###":var d=re(""+o);return"0"!==d?l+d:"";default:if(r.match(/\.[0#?]*$/))return de(e,r.slice(0,r.lastIndexOf(".")),t)+oe(r.slice(r.lastIndexOf(".")))}throw new Error("unsupported format |"+r+"|")}function pe(e,r,t){return(0|t)===t?de(e,r,t):he(e,r,t)}var me=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ge(e){for(var r=0,t="",a="";r<e.length;)switch(t=e.charAt(r)){case"G":U(e,r)&&(r+=6),r++;break;case'"':for(;34!==e.charCodeAt(++r)&&r<e.length;);++r;break;case"\\":case"_":r+=2;break;case"@":++r;break;case"B":case"b":if("1"===e.charAt(r+1)||"2"===e.charAt(r+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(r,3).toUpperCase())return!0;if("AM/PM"===e.substr(r,5).toUpperCase())return!0;if("上午/下午"===e.substr(r,5).toUpperCase())return!0;++r;break;case"[":for(a=t;"]"!==e.charAt(r++)&&r<e.length;)a+=e.charAt(r);if(a.match(me))return!0;break;case".":case"0":case"#":for(;r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||"\\"==t&&"-"==e.charAt(r+1)&&"0#".indexOf(e.charAt(r+2))>-1););break;case"?":for(;e.charAt(++r)===t;);break;case"*":++r," "!=e.charAt(r)&&"*"!=e.charAt(r)||++r;break;case"(":case")":++r;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1;);break;default:++r}return!1}var ve=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function be(e,r){if(null==r)return!1;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return!0;break;case">":if(e>t)return!0;break;case"<":if(e<t)return!0;break;case"<>":if(e!=t)return!0;break;case">=":if(e>=t)return!0;break;case"<=":if(e<=t)return!0}return!1}function Te(e,r){var t=function(e){for(var r=[],t=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:case 42:case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n),n=a+1}if(r[r.length]=e.substr(n),!0===t)throw new Error("Format |"+e+"| unterminated string ");return r}(e),a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1&&--a,t.length>4)throw new Error("cannot find right format for |"+t.join("|")+"|");if("number"!=typeof r)return[4,4===t.length||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"]}var s=r>0?t[0]:r<0?t[1]:t[2];if(-1===t[0].indexOf("[")&&-1===t[1].indexOf("["))return[a,s];if(null!=t[0].match(/\[[=<>]/)||null!=t[1].match(/\[[=<>]/)){var i=t[0].match(ve),c=t[1].match(ve);return be(r,i)?[a,t[0]]:be(r,c)?[a,t[1]]:[a,t[null!=i&&null!=c?2:1]]}return[a,s]}function Ee(e,r,t){null==t&&(t={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&t.dateNF?t.dateNF:e;break;case"number":null==(a=14==e&&t.dateNF?t.dateNF:(null!=t.table?t.table:H)[e])&&(a=t.table&&t.table[W[e]]||H[W[e]]),null==a&&(a=z[e]||"General")}if(U(a,0))return Q(r,t);r instanceof Date&&(r=J(r,t.date1904));var n=Te(a,r);if(U(n[1]))return Q(r,t);if(!0===r)r="TRUE";else if(!1===r)r="FALSE";else if(""===r||null==r)return"";return function(e,r,t,a){for(var n,s,i,c=[],o="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!U(e,l))throw new Error("unrecognized character "+f+" in "+e);c[c.length]={t:"G",v:"General"},l+=7;break;case'"':for(o="";34!==(i=e.charCodeAt(++l))&&l<e.length;)o+=String.fromCharCode(i);c[c.length]={t:"t",v:o},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";c[c.length]={t:p,v:d},++l;break;case"_":c[c.length]={t:"t",v:" "},l+=2;break;case"@":c[c.length]={t:"T",v:r},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=j(r,t,"2"===e.charAt(l+1))))return"";c[c.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(r<0)return"";if(null==n&&null==(n=j(r,t)))return"";for(o=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)o+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),c[c.length]={t:f,v:o},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=j(r,t)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";c[c.length]=m,h=f;break;case"[":for(o=f;"]"!==e.charAt(l++)&&l<e.length;)o+=e.charAt(l);if("]"!==o.slice(-1))throw'unterminated "[" block: |'+o+"|";if(o.match(me)){if(null==n&&null==(n=j(r,t)))return"";c[c.length]={t:"Z",v:o.toLowerCase()},h=o.charAt(1)}else o.indexOf("$")>-1&&(o=(o.match(/\$([^-\[\]]*)/)||[])[1]||"$",ge(e)||(c[c.length]={t:"t",v:o}));break;case".":if(null!=n){for(o=f;++l<e.length&&"0"===(f=e.charAt(l));)o+=f;c[c.length]={t:"s",v:o};break}case"0":case"#":for(o=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)o+=f;c[c.length]={t:"n",v:o};break;case"?":for(o=f;e.charAt(++l)===f;)o+=f;c[c.length]={t:f,v:o},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":c[c.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(o=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)o+=e.charAt(l);c[c.length]={t:"D",v:o};break;case" ":c[c.length]={t:f,v:f},++l;break;case"$":c[c.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);c[c.length]={t:"t",v:f},++l}var g,v=0,b=0;for(l=c.length-1,h="t";l>=0;--l)switch(c[l].t){case"h":case"H":c[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=c[l].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=c[l].t;break;case"m":"s"===h&&(c[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&c[l].v.match(/[Hh]/)&&(v=1),v<2&&c[l].v.match(/[Mm]/)&&(v=2),v<3&&c[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var T,E="";for(l=0;l<c.length;++l)switch(c[l].t){case"t":case"T":case" ":case"D":break;case"X":c[l].v="",c[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":c[l].v=ee(c[l].t.charCodeAt(0),c[l].v,n,b),c[l].t="t";break;case"n":case"?":for(T=l+1;null!=c[T]&&("?"===(f=c[T].t)||"D"===f||(" "===f||"t"===f)&&null!=c[T+1]&&("?"===c[T+1].t||"t"===c[T+1].t&&"/"===c[T+1].v)||"("===c[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===c[T].v||" "===c[T].v&&null!=c[T+1]&&"?"==c[T+1].t));)c[l].v+=c[T].v,c[T]={v:"",t:";"},++T;E+=c[l].v,l=T-1;break;case"G":c[l].t="t",c[l].v=Q(r,t)}var w,A,S="";if(E.length>0){40==E.charCodeAt(0)?(w=r<0&&45===E.charCodeAt(0)?-r:r,A=pe("n",E,w)):(A=pe("n",E,w=r<0&&a>1?-r:r),w<0&&c[0]&&"t"==c[0].t&&(A=A.substr(1),c[0].v="-"+c[0].v)),T=A.length-1;var k=c.length;for(l=0;l<c.length;++l)if(null!=c[l]&&"t"!=c[l].t&&c[l].v.indexOf(".")>-1){k=l;break}var y=c.length;if(k===c.length&&-1===A.indexOf("E")){for(l=c.length-1;l>=0;--l)null!=c[l]&&-1!=="n?".indexOf(c[l].t)&&(T>=c[l].v.length-1?(T-=c[l].v.length,c[l].v=A.substr(T+1,c[l].v.length)):T<0?c[l].v="":(c[l].v=A.substr(0,T+1),T=-1),c[l].t="t",y=l);T>=0&&y<c.length&&(c[y].v=A.substr(0,T+1)+c[y].v)}else if(k!==c.length&&-1===A.indexOf("E")){for(T=A.indexOf(".")-1,l=k;l>=0;--l)if(null!=c[l]&&-1!=="n?".indexOf(c[l].t)){for(s=c[l].v.indexOf(".")>-1&&l===k?c[l].v.indexOf(".")-1:c[l].v.length-1,S=c[l].v.substr(s+1);s>=0;--s)T>=0&&("0"===c[l].v.charAt(s)||"#"===c[l].v.charAt(s))&&(S=A.charAt(T--)+S);c[l].v=S,c[l].t="t",y=l}for(T>=0&&y<c.length&&(c[y].v=A.substr(0,T+1)+c[y].v),T=A.indexOf(".")+1,l=k;l<c.length;++l)if(null!=c[l]&&(-1!=="n?(".indexOf(c[l].t)||l===k)){for(s=c[l].v.indexOf(".")>-1&&l===k?c[l].v.indexOf(".")+1:0,S=c[l].v.substr(0,s);s<c[l].v.length;++s)T<A.length&&(S+=A.charAt(T++));c[l].v=S,c[l].t="t",y=l}}}for(l=0;l<c.length;++l)null!=c[l]&&"n?".indexOf(c[l].t)>-1&&(w=a>1&&r<0&&l>0&&"-"===c[l-1].v?-r:r,c[l].v=pe(c[l].t,c[l].v,w),c[l].t="t");var _="";for(l=0;l!==c.length;++l)null!=c[l]&&(_+=c[l].v);return _}(n[1],r,t,n[0])}function we(e,r){if("number"!=typeof r){r=+r||-1;for(var t=0;t<392;++t)if(null!=H[t]){if(H[t]==e){r=t;break}}else r<0&&(r=t);r<0&&(r=391)}return H[r]=e,r}function Ae(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',H=e}var Se={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},ke=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var ye=function(){var e={};e.version="1.2.0";var r=function(){for(var e=0,r=new Array(256),t=0;256!=t;++t)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=t)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,r[t]=e;return"undefined"!=typeof Int32Array?new Int32Array(r):r}();var t=function(e){var r=0,t=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(t=e[a],r=256+a;r<4096;r+=256)t=n[r]=t>>>8^e[255&t];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(r),a=t[0],n=t[1],s=t[2],i=t[3],c=t[4],o=t[5],l=t[6],f=t[7],h=t[8],u=t[9],d=t[10],p=t[11],m=t[12],g=t[13],v=t[14];return e.table=r,e.bstr=function(e,t){for(var a=-1^t,n=0,s=e.length;n<s;)a=a>>>8^r[255&(a^e.charCodeAt(n++))];return~a},e.buf=function(e,t){for(var b=-1^t,T=e.length-15,E=0;E<T;)b=v[e[E++]^255&b]^g[e[E++]^b>>8&255]^m[e[E++]^b>>16&255]^p[e[E++]^b>>>24]^d[e[E++]]^u[e[E++]]^h[e[E++]]^f[e[E++]]^l[e[E++]]^o[e[E++]]^c[e[E++]]^i[e[E++]]^s[e[E++]]^n[e[E++]]^a[e[E++]]^r[e[E++]];for(T+=15;E<T;)b=b>>>8^r[255&(b^e[E++])];return~b},e.str=function(e,t){for(var a=-1^t,n=0,s=e.length,i=0,c=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^r[255&(a^i)]:i<2048?a=(a=a>>>8^r[255&(a^(192|i>>6&31))])>>>8^r[255&(a^(128|63&i))]:i>=55296&&i<57344?(i=64+(1023&i),c=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^r[255&(a^(240|i>>8&7))])>>>8^r[255&(a^(128|i>>2&63))])>>>8^r[255&(a^(128|c>>6&15|(3&i)<<4))])>>>8^r[255&(a^(128|63&c))]):a=(a=(a=a>>>8^r[255&(a^(224|i>>12&15))])>>>8^r[255&(a^(128|i>>6&63))])>>>8^r[255&(a^(128|63&i))];return~a},e}(),_e=function(){var e,r={};function t(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:t(e.slice(0,-1));var r=e.lastIndexOf("/");return-1===r?e:e.slice(0,r+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var r=e.lastIndexOf("/");return-1===r?e:e.slice(r+1)}function s(e,r){"string"==typeof r&&(r=new Date(r));var t=r.getHours();t=(t=t<<6|r.getMinutes())<<5|r.getSeconds()>>>1,e.write_shift(2,t);var a=r.getFullYear()-1980;a=(a=a<<4|r.getMonth()+1)<<5|r.getDate(),e.write_shift(2,a)}function i(e){mt(e,0);for(var r={},t=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};if(21589===a)1&(t=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&t&&(i.atime=e.read_shift(4)),4&t&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));e.l=s,r[a]=i}return r}function c(){return e||(e={})}function o(e,r){if(80==e[0]&&75==e[1])return me(e,r);if(109==(32|e[0])&&105==(32|e[1]))return function(e,r){if("mime-version:"!=D(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var t=r&&r.root||"",a=(A&&Buffer.isBuffer(e)?e.toString("binary"):D(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),t||(t=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,t.length)!=t))for(;t.length>0&&(t=(t=t.slice(0,t.length-1)).slice(0,t.lastIndexOf("/")+1),s.slice(0,t.length)!=t););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var c="--"+(i[1]||""),o={FileIndex:[],FullPaths:[]};d(o);var l,f=0;for(n=0;n<a.length;++n){var h=a[n];h!==c&&h!==c+"--"||(f++&&we(o,a.slice(l,n),t),l=n)}return o}(e,r);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t,a,n,s,i,c,o=512,p=[],m=e.slice(0,512);mt(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(T,"Header Signature: "),e.l+=16;var r=e.read_shift(2,"u");return[e.read_shift(2,"u"),r]}(m);switch(t=g[0]){case 3:o=512;break;case 4:o=4096;break;case 0:if(0==g[1])return me(e,r);default:throw new Error("Major Version: Expected 3 or 4 saw "+t)}512!==o&&mt(m=e.slice(0,o),28);var v=e.slice(0,o);!function(e,r){var t=9;switch(e.l+=2,t=e.read_shift(2)){case 9:if(3!=r)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(4!=r)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,t);var E=m.read_shift(4,"i");if(3===t&&0!==E)throw new Error("# Directory Sectors: Expected 0 saw "+E);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),i=m.read_shift(4,"i"),a=m.read_shift(4,"i"),c=m.read_shift(4,"i"),n=m.read_shift(4,"i");for(var w=-1,S=0;S<109&&!((w=m.read_shift(4,"i"))<0);++S)p[S]=w;var k=function(e,r){for(var t=Math.ceil(e.length/r)-1,a=[],n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);return a[t-1]=e.slice(t*r),a}(e,o);f(c,n,k,o,p);var y=function(e,r,t,a){var n=e.length,s=[],i=[],c=[],o=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(c=[],(u=f+r)>=n&&(u-=n),!i[u]){o=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,c[c.length]=h,o.push(e[h]);var m=t[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(p[h=lt(e[m],d)])break}s[u]={nodes:c,data:Br([o])}}return s}(k,s,p,o);y[s].name="!Directory",a>0&&i!==b&&(y[i].name="!MiniFAT"),y[p[0]].name="!FAT",y.fat_addrs=p,y.ssz=o;var _=[],C=[],x=[];!function(e,r,t,a,n,s,i,c){for(var o,f=0,d=a.length?2:0,p=r[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);mt(v,64),g=v.read_shift(2),o=Hr(v,0,g-d),a.push(o);var T={name:o,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=u(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=u(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=b,T.name=""),5===T.type?(f=T.start,n>0&&f!==b&&(r[f].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===r[T.start]&&(r[T.start]=h(t,T.start,r.fat_addrs,r.ssz)),r[T.start].name=T.name,T.content=r[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:f!==b&&T.start!==b&&r[f]&&(T.content=l(T,r[f].data,(r[c]||{}).data))),T.content&&mt(T.content,0),s[o]=T,i.push(T)}}(s,y,k,_,a,{},C,i),function(e,r,t){for(var a=0,n=0,s=0,i=0,c=0,o=t.length,l=[],f=[];a<o;++a)l[a]=f[a]=a,r[a]=t[a];for(;c<f.length;++c)n=e[a=f[c]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<c&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<c&&f.push(s));for(a=1;a<o;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<o;++a)if(0!==e[a].type){if((c=a)!=l[c])do{c=l[c],r[a]=r[c]+"/"+r[a]}while(0!==c&&-1!==l[c]&&c!=l[c]);l[a]=-1}for(r[0]+="/",a=1;a<o;++a)2!==e[a].type&&(r[a]+="/")}(C,x,_),_.shift();var O={FileIndex:C,FullPaths:x};return r&&r.raw&&(O.raw={header:v,sectors:k}),O}function l(e,r,t){for(var a=e.start,n=e.size,s=[],i=a;t&&n>0&&i>=0;)s.push(r.slice(i*v,i*v+v)),n-=v,i=lt(t,4*i);return 0===s.length?vt(0):O(s).slice(0,e.size)}function f(e,r,t,a,n){var s=b;if(e===b){if(0!==r)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=t[e],c=(a>>>2)-1;if(!i)return;for(var o=0;o<c&&(s=lt(i,4*o))!==b;++o)n.push(s);f(lt(i,a-4),r-1,t,a,n)}}function h(e,r,t,a,n){var s=[],i=[];n||(n=[]);var c=a-1,o=0,l=0;for(o=r;o>=0;){n[o]=!0,s[s.length]=o,i.push(e[o]);var f=t[Math.floor(4*o/a)];if(a<4+(l=4*o&c))throw new Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=lt(e[f],l)}return{nodes:s,data:Br([i])}}function u(e,r){return new Date(1e3*(ot(e,r+4)/1e7*Math.pow(2,32)+ot(e,r)/1e7-11644473600))}function d(e,r){var t=r||{},a=t.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),t.CLSID&&(e.FileIndex[0].clsid=t.CLSID),function(e){var r="Sh33tJ5";if(_e.find(e,"/"+r))return;var t=vt(4);t[0]=55,t[1]=t[3]=50,t[2]=54,e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+r),p(e)}(e)}function p(e,r){d(e);for(var n=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var c=e.FileIndex[i];switch(c.type){case 0:s?n=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(c.R*c.L*c.C)&&(n=!0),c.R>-1&&c.L>-1&&c.R==c.L&&(n=!0);break;default:n=!0}}if(n||r){var o=new Date(1987,1,19),l=0,f=Object.create?Object.create(null):{},h=[];for(i=0;i<e.FullPaths.length;++i)f[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&h.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<h.length;++i){var u=t(h[i][0]);(s=f[u])||(h.push([u,{name:a(u).replace("/",""),type:1,clsid:x,ct:o,mt:o,content:null}]),f[u]=!0)}for(h.sort((function(e,r){return function(e,r){for(var t=e.split("/"),a=r.split("/"),n=0,s=0,i=Math.min(t.length,a.length);n<i;++n){if(s=t[n].length-a[n].length)return s;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}(e[0],r[0])})),e.FullPaths=[],e.FileIndex=[],i=0;i<h.length;++i)e.FullPaths[i]=h[i][0],e.FileIndex[i]=h[i][1];for(i=0;i<h.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||x,0===i)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<h.length&&t(e.FullPaths[l])!=m;++l);for(p.C=l>=h.length?-1:l,l=i+1;l<h.length&&t(e.FullPaths[l])!=t(m);++l);p.R=l>=h.length?-1:l,p.type=1}else t(e.FullPaths[i+1]||"")==t(m)&&(p.R=i+1),p.type=2}}}function m(e,r){var t=r||{};if("mad"==t.fileType)return function(e,r){for(var t=r||{},a=t.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,c=e.FileIndex[0],o=1;o<e.FullPaths.length;++o)if(i=e.FullPaths[o].slice(s.length),(c=e.FileIndex[o]).size&&c.content&&"Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=c.content,f=A&&Buffer.isBuffer(l)?l.toString("binary"):D(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(t.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+be(c,i)),n.push(""),n.push(m?Ee(f):Te(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,t);if(p(e),"zip"===t.fileType)return function(e,r){var t=r||{},a=[],n=[],i=vt(1),c=t.compression?8:0,o=0,l=0,f=0,h=0,u=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],g=[],v=0;for(l=1;l<e.FullPaths.length;++l)if(p=e.FullPaths[l].slice(d.length),(m=e.FileIndex[l]).size&&m.content&&"Sh33tJ5"!=p){var b=h,T=vt(p.length);for(f=0;f<p.length;++f)T.write_shift(1,127&p.charCodeAt(f));T=T.slice(0,T.l),g[u]=ye.buf(m.content,0);var E=m.content;8==c&&(E=F(E)),(i=vt(30)).write_shift(4,67324752),i.write_shift(2,20),i.write_shift(2,o),i.write_shift(2,c),m.mt?s(i,m.mt):i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,E.length),i.write_shift(4,m.content.length),i.write_shift(2,T.length),i.write_shift(2,0),h+=i.length,a.push(i),h+=T.length,a.push(T),h+=E.length,a.push(E),(i=vt(46)).write_shift(4,33639248),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,o),i.write_shift(2,c),i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,E.length),i.write_shift(4,m.content.length),i.write_shift(2,T.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,b),v+=i.l,n.push(i),v+=T.length,n.push(T),++u}return(i=vt(22)).write_shift(4,101010256),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,u),i.write_shift(2,u),i.write_shift(4,v),i.write_shift(4,h),i.write_shift(2,0),O([O(a),O(n),i])}(e,t);var a=function(e){for(var r=0,t=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?r+=s+63>>6:t+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,c=r+127>>7,o=(r+7>>3)+t+i+c,l=o+127>>7,f=l<=109?0:Math.ceil((l-109)/127);o+l+f+127>>7>l;)f=++l<=109?0:Math.ceil((l-109)/127);var h=[1,f,l,c,i,t,r,0];return e.FileIndex[0].size=r<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),n=vt(a[7]<<9),i=0,c=0;for(i=0;i<8;++i)n.write_shift(1,C[i]);for(i=0;i<8;++i)n.write_shift(2,0);for(n.write_shift(2,62),n.write_shift(2,3),n.write_shift(2,65534),n.write_shift(2,9),n.write_shift(2,6),i=0;i<3;++i)n.write_shift(2,0);for(n.write_shift(4,0),n.write_shift(4,a[2]),n.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),n.write_shift(4,0),n.write_shift(4,4096),n.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:b),n.write_shift(4,a[3]),n.write_shift(-4,a[1]?a[0]-1:b),n.write_shift(4,a[1]),i=0;i<109;++i)n.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(c=0;c<a[1];++c){for(;i<236+127*c;++i)n.write_shift(-4,i<a[2]?a[1]+i:-1);n.write_shift(-4,c===a[1]-1?b:c+1)}var o=function(e){for(c+=e;i<c-1;++i)n.write_shift(-4,i+1);e&&(++i,n.write_shift(-4,b))};for(c=i=0,c+=a[1];i<c;++i)n.write_shift(-4,N.DIFSECT);for(c+=a[2];i<c;++i)n.write_shift(-4,N.FATSECT);o(a[3]),o(a[4]);for(var l=0,f=0,h=e.FileIndex[0];l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&((f=h.content.length)<4096||(h.start=c,o(f+511>>9)));for(o(a[6]+7>>3);511&n.l;)n.write_shift(-4,N.ENDOFCHAIN);for(c=i=0,l=0;l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&(!(f=h.content.length)||f>=4096||(h.start=c,o(f+63>>6)));for(;511&n.l;)n.write_shift(-4,N.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(u&&0!==u.length){h=e.FileIndex[i],0===i&&(h.start=h.size?h.start-1:b);var d=0===i&&t.root||h.name;if(f=2*(d.length+1),n.write_shift(64,d,"utf16le"),n.write_shift(2,f),n.write_shift(1,h.type),n.write_shift(1,h.color),n.write_shift(-4,h.L),n.write_shift(-4,h.R),n.write_shift(-4,h.C),h.clsid)n.write_shift(16,h.clsid,"hex");else for(l=0;l<4;++l)n.write_shift(4,0);n.write_shift(4,h.state||0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,h.start),n.write_shift(4,h.size),n.write_shift(4,0)}else{for(l=0;l<17;++l)n.write_shift(4,0);for(l=0;l<3;++l)n.write_shift(4,-1);for(l=0;l<12;++l)n.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>=4096)if(n.l=h.start+1<<9,A&&Buffer.isBuffer(h.content))h.content.copy(n,n.l,0,h.size),n.l+=h.size+511&-512;else{for(l=0;l<h.size;++l)n.write_shift(1,h.content[l]);for(;511&l;++l)n.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>0&&h.size<4096)if(A&&Buffer.isBuffer(h.content))h.content.copy(n,n.l,0,h.size),n.l+=h.size+63&-64;else{for(l=0;l<h.size;++l)n.write_shift(1,h.content[l]);for(;63&l;++l)n.write_shift(1,0)}if(A)n.l=n.length;else for(;n.l<n.length;)n.write_shift(1,0);return n}r.version="1.2.1";var g,v=64,b=-2,T="d0cf11e0a1b11ae1",C=[208,207,17,224,161,177,26,225],x="00000000000000000000000000000000",N={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:b,FREESECT:-1,HEADER_SIGNATURE:T,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:x,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function D(e){for(var r=new Array(e.length),t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function F(e){return g?g.deflateRawSync(e):ie(e)}var P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],M=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],L=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var U,B,V="undefined"!=typeof Uint8Array,H=V?new Uint8Array(256):[],W=0;W<256;++W)H[W]=(B=void 0,255&((B=139536&((U=W)<<1|U<<11)|558144&(U<<5|U<<15))>>16|B>>8|B));function z(e,r){var t=H[255&e];return r<=8?t>>>8-r:(t=t<<8|H[e>>8&255],r<=16?t>>>16-r:(t=t<<8|H[e>>16&255])>>>24-r)}function G(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}function $(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function j(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function X(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function Y(e,r,t){var a=7&r,n=r>>>3,s=(1<<t)-1,i=e[n]>>>a;return t<8-a?i&s:(i|=e[n+1]<<8-a,t<16-a?i&s:(i|=e[n+2]<<16-a,t<24-a?i&s:(i|=e[n+3]<<24-a)&s))}function K(e,r,t){var a=7&r,n=r>>>3;return a<=5?e[n]|=(7&t)<<a:(e[n]|=t<<a&255,e[n+1]=(7&t)>>8-a),r+3}function J(e,r,t){return t=(1&t)<<(7&r),e[r>>>3]|=t,r+1}function q(e,r,t){var a=r>>>3;return t<<=7&r,e[a]|=255&t,t>>>=8,e[a+1]=t,r+8}function Z(e,r,t){var a=r>>>3;return t<<=7&r,e[a]|=255&t,t>>>=8,e[a+1]=255&t,e[a+2]=t>>>8,r+16}function Q(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(A){var s=y(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(V){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<t;++n)i[n]=e[n];return i}return e.length=a,e}function ee(e){for(var r=new Array(e),t=0;t<e;++t)r[t]=0;return r}function re(e,r,t){var a=1,n=0,s=0,i=0,c=0,o=e.length,l=V?new Uint16Array(32):ee(32);for(s=0;s<32;++s)l[s]=0;for(s=o;s<t;++s)e[s]=0;o=e.length;var f=V?new Uint16Array(o):ee(o);for(s=0;s<o;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=c=c+l[s-1]<<1;for(s=0;s<o;++s)0!=(c=e[s])&&(f[s]=l[c+16]++);var h=0;for(s=0;s<o;++s)if(0!=(h=e[s]))for(c=z(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)r[c|i<<h]=15&h|s<<4;return a}var te=V?new Uint16Array(512):ee(512),ae=V?new Uint16Array(32):ee(32);if(!V){for(var ne=0;ne<512;++ne)te[ne]=0;for(ne=0;ne<32;++ne)ae[ne]=0}!function(){for(var e=[],r=0;r<32;r++)e.push(5);re(e,ae,32);var t=[];for(r=0;r<=143;r++)t.push(8);for(;r<=255;r++)t.push(9);for(;r<=279;r++)t.push(7);for(;r<=287;r++)t.push(8);re(t,te,288)}();var se=function(){for(var e=V?new Uint8Array(32768):[],r=0,t=0;r<L.length-1;++r)for(;t<L[r+1];++t)e[t]=r;for(;t<32768;++t)e[t]=29;var a=V?new Uint8Array(259):[];for(r=0,t=0;r<M.length-1;++r)for(;t<M[r+1];++t)a[t]=r;return function(r,t){return r.length<8?function(e,r){for(var t=0;t<e.length;){var a=Math.min(65535,e.length-t),n=t+a==e.length;for(r.write_shift(1,+n),r.write_shift(2,a),r.write_shift(2,65535&~a);a-- >0;)r[r.l++]=e[t++]}return r.l}(r,t):function(r,t){for(var n=0,s=0,i=V?new Uint16Array(32768):[];s<r.length;){var c=Math.min(65535,r.length-s);if(c<10){for(7&(n=K(t,n,+!(s+c!=r.length)))&&(n+=8-(7&n)),t.l=n/8|0,t.write_shift(2,c),t.write_shift(2,65535&~c);c-- >0;)t[t.l++]=r[s++];n=8*t.l}else{n=K(t,n,+!(s+c!=r.length)+2);for(var o=0;c-- >0;){var l=r[s],f=-1,h=0;if((f=i[o=32767&(o<<5^l)])&&((f|=-32768&s)>s&&(f-=32768),f<s))for(;r[f+h]==r[s+h]&&h<250;)++h;if(h>2){(l=a[h])<=22?n=q(t,n,H[l+1]>>1)-1:(q(t,n,3),q(t,n+=5,H[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(Z(t,n,h-M[l]),n+=u),l=e[s-f],n=q(t,n,H[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(Z(t,n,s-f-L[l]),n+=d);for(var p=0;p<h;++p)i[o]=32767&s,o=32767&(o<<5^r[s]),++s;c-=h-1}else l<=143?l+=48:n=J(t,n,1),n=q(t,n,H[l]),i[o]=32767&s,++s}n=q(t,n,0)-1}}return t.l=(n+7)/8|0,t.l}(r,t)}}();function ie(e){var r=vt(50+Math.floor(1.1*e.length)),t=se(e,r);return r.slice(0,t)}var ce=V?new Uint16Array(32768):ee(32768),oe=V?new Uint16Array(32768):ee(32768),le=V?new Uint16Array(128):ee(128),fe=1,he=1;function ue(e,r){var t=j(e,r)+257,a=j(e,r+=5)+1,n=function(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=4?0:e[a+1]<<8))>>>t&15}(e,r+=5)+4;r+=4;for(var s=0,i=V?new Uint8Array(19):ee(19),c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=1,l=V?new Uint8Array(8):ee(8),f=V?new Uint8Array(8):ee(8),h=i.length,u=0;u<n;++u)i[P[u]]=s=$(e,r),o<s&&(o=s),l[s]++,r+=3;var d=0;for(l[0]=0,u=1;u<=o;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(c[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(0!=(p=i[u])){d=H[c[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)le[d|m<<p]=7&p|u<<3}var g=[];for(o=1;g.length<t+a;)switch(r+=7&(d=le[X(e,r)]),d>>>=3){case 16:for(s=3+G(e,r),r+=2,d=g[g.length-1];s-- >0;)g.push(d);break;case 17:for(s=3+$(e,r),r+=3;s-- >0;)g.push(0);break;case 18:for(s=11+X(e,r),r+=7;s-- >0;)g.push(0);break;default:g.push(d),o<d&&(o=d)}var v=g.slice(0,t),b=g.slice(t);for(u=t;u<286;++u)v[u]=0;for(u=a;u<30;++u)b[u]=0;return fe=re(v,ce,286),he=re(b,oe,30),r}function de(e,r){var t=function(e,r){if(3==e[0]&&!(3&e[1]))return[k(r),2];for(var t=0,a=0,n=y(r||1<<18),s=0,i=n.length>>>0,c=0,o=0;0==(1&a);)if(a=$(e,t),t+=3,a>>>1!=0)for(a>>1==1?(c=9,o=5):(t=ue(e,t),c=fe,o=he);;){!r&&i<s+32767&&(i=(n=Q(n,s+32767)).length);var l=Y(e,t,c),f=a>>>1==1?te[l]:ce[l];if(t+=15&f,0==((f>>>=4)>>>8&255))n[s++]=f;else{if(256==f)break;var h=(f-=257)<8?0:f-4>>2;h>5&&(h=0);var u=s+M[f];h>0&&(u+=Y(e,t,h),t+=h),l=Y(e,t,o),t+=15&(f=a>>>1==1?ae[l]:oe[l]);var d=(f>>>=4)<4?0:f-2>>1,p=L[f];for(d>0&&(p+=Y(e,t,d),t+=d),!r&&i<u&&(i=(n=Q(n,u+100)).length);s<u;)n[s]=n[s-p],++s}}else{7&t&&(t+=8-(7&t));var m=e[t>>>3]|e[1+(t>>>3)]<<8;if(t+=32,m>0)for(!r&&i<s+m&&(i=(n=Q(n,s+m)).length);m-- >0;)n[s++]=e[t>>>3],t+=8}return r?[n,t+7>>>3]:[n.slice(0,s),t+7>>>3]}(e.slice(e.l||0),r);return e.l+=t[1],t[0]}function pe(e,r){if(!e)throw new Error(r);"undefined"!=typeof console&&n("error","at node_modules/xlsx/xlsx.mjs:2670",r)}function me(e,r){var t=e;mt(t,0);var a={FileIndex:[],FullPaths:[]};d(a,{root:r.root});for(var n=t.length-4;(80!=t[n]||75!=t[n+1]||5!=t[n+2]||6!=t[n+3])&&n>=0;)--n;t.l=n+4,t.l+=4;var s=t.read_shift(2);t.l+=6;var c=t.read_shift(4);for(t.l=c,n=0;n<s;++n){t.l+=20;var o=t.read_shift(4),l=t.read_shift(4),f=t.read_shift(2),h=t.read_shift(2),u=t.read_shift(2);t.l+=8;var p=t.read_shift(4),m=i(t.slice(t.l+f,t.l+f+h));t.l+=f+h+u;var g=t.l;t.l=p+4,ge(t,o,l,a,m),t.l=g}return a}function ge(e,r,t,a,n){e.l+=2;var s=e.read_shift(2),c=e.read_shift(2),o=function(e){var r=65535&e.read_shift(2),t=65535&e.read_shift(2),a=new Date,n=31&t,s=15&(t>>>=5);t>>>=4,a.setMilliseconds(0),a.setFullYear(t+1980),a.setMonth(s-1),a.setDate(n);var i=31&r,c=63&(r>>>=5);return r>>>=6,a.setHours(r),a.setMinutes(c),a.setSeconds(i<<1),a}(e);if(8257&s)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);if(u){var m=i(e.slice(e.l,e.l+u));(m[21589]||{}).mt&&(o=m[21589].mt),((n||{})[21589]||{}).mt&&(o=n[21589].mt)}e.l+=u;var v=e.slice(e.l,e.l+l);switch(c){case 8:v=function(e,r){if(!g)return de(e,r);var t=new(0,g.InflateRaw),a=t._processChunk(e.slice(e.l),t._finishFlushFlag);return e.l+=t.bytesRead,a}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+c)}var b=!1;8&s&&(134695760==e.read_shift(4)&&(e.read_shift(4),b=!0),l=e.read_shift(4),f=e.read_shift(4)),l!=r&&pe(b,"Bad compressed size: "+r+" != "+l),f!=t&&pe(b,"Bad uncompressed size: "+t+" != "+f),Ae(a,d,v,{unsafe:!0,mt:o})}var ve={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function be(e,r){if(e.ctype)return e.ctype;var t=e.name||"",a=t.match(/\.([^\.]+)$/);return a&&ve[a[1]]||r&&(a=(t=r).match(/[\.\\]([^\.\\])+$/))&&ve[a[1]]?ve[a[1]]:"application/octet-stream"}function Te(e){for(var r=E(e),t=[],a=0;a<r.length;a+=76)t.push(r.slice(a,a+76));return t.join("\r\n")+"\r\n"}function Ee(e){var r=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var r=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==r.length?"0"+r:r)}));"\n"==(r=r.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(r="=0D"+r.slice(1));for(var t=[],a=(r=r.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var c=76,o=s.slice(i,i+c);"="==o.charAt(c-1)?c--:"="==o.charAt(c-2)?c-=2:"="==o.charAt(c-3)&&(c-=3),o=s.slice(i,i+c),(i+=c)<s.length&&(o+="="),t.push(o)}else t.push("")}return t.join("\r\n")}function we(e,r,t){for(var a,n="",s="",i="",c=0;c<10;++c){var o=r[c];if(!o||o.match(/^\s*$/))break;var l=o.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++c,s.toLowerCase()){case"base64":a=_(w(r.slice(c).join("")));break;case"quoted-printable":a=function(e){for(var r=[],t=0;t<e.length;++t){for(var a=e[t];t<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++t];r.push(a)}for(var n=0;n<r.length;++n)r[n]=r[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return _(r.join("\r\n"))}(r.slice(c));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=Ae(e,n.slice(t.length),a,{unsafe:!0});i&&(f.ctype=i)}function Ae(e,r,t,n){var s=n&&n.unsafe;s||d(e);var i=!s&&_e.find(e,r);if(!i){var c=e.FullPaths[0];r.slice(0,c.length)==c?c=r:("/"!=c.slice(-1)&&(c+="/"),c=(c+r).replace("//","/")),i={name:a(r),type:2},e.FileIndex.push(i),e.FullPaths.push(c),s||_e.utils.cfb_gc(e)}return i.content=t,i.size=t?t.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return r.find=function(e,r){var t=e.FullPaths.map((function(e){return e.toUpperCase()})),a=t.map((function(e){var r=e.split("/");return r[r.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===r.charCodeAt(0)?(n=!0,r=t[0].slice(0,-1)+r):n=-1!==r.indexOf("/");var s=r.toUpperCase(),i=!0===n?t.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var c=!s.match(I);for(s=s.replace(R,""),c&&(s=s.replace(I,"!")),i=0;i<t.length;++i){if((c?t[i].replace(I,"!"):t[i]).replace(R,"")==s)return e.FileIndex[i];if((c?a[i].replace(I,"!"):a[i]).replace(R,"")==s)return e.FileIndex[i]}return null},r.read=function(r,t){var a=t&&t.type;switch(a||A&&Buffer.isBuffer(r)&&(a="buffer"),a||"base64"){case"file":return function(r,t){return c(),o(e.readFileSync(r),t)}(r,t);case"base64":return o(_(w(r)),t);case"binary":return o(_(r),t)}return o(r,t)},r.parse=o,r.write=function(r,t){var a=m(r,t);switch(t&&t.type||"buffer"){case"file":return c(),e.writeFileSync(t.filename,a),a;case"binary":return"string"==typeof a?a:D(a);case"base64":return E("string"==typeof a?a:D(a));case"buffer":if(A)return Buffer.isBuffer(a)?a:S(a);case"array":return"string"==typeof a?_(a):a}return a},r.writeFile=function(r,t,a){c();var n=m(r,a);e.writeFileSync(t,n)},r.utils={cfb_new:function(e){var r={};return d(r,e),r},cfb_add:Ae,cfb_del:function(e,r){d(e);var t=_e.find(e,r);if(t)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1},cfb_mov:function(e,r,t){d(e);var n=_e.find(e,r);if(n)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(t),e.FullPaths[s]=t,!0;return!1},cfb_gc:function(e){p(e,!0)},ReadShift:ht,CheckField:pt,prep_blob:mt,bconcat:O,use_zlib:function(e){try{var r=new(0,e.InflateRaw);if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),!r.bytesRead)throw new Error("zlib does not expose bytesRead");g=e}catch(t){n("error","at node_modules/xlsx/xlsx.mjs:2211","cannot use native zlib: "+(t.message||t))}},_deflateRaw:ie,_inflateRaw:de,consts:N},r}();function Ce(e){for(var r=Object.keys(e),t=[],a=0;a<r.length;++a)Object.prototype.hasOwnProperty.call(e,r[a])&&t.push(r[a]);return t}function xe(e){for(var r=[],t=Ce(e),a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}var Oe=new Date(1899,11,30,0,0,0);function Re(e,r){var t=e.getTime();return r&&(t-=1263168e5),(t-(Oe.getTime()+6e4*(e.getTimezoneOffset()-Oe.getTimezoneOffset())))/864e5}var Ie=new Date,Ne=Oe.getTime()+6e4*(Ie.getTimezoneOffset()-Oe.getTimezoneOffset()),De=Ie.getTimezoneOffset();function Fe(e){var r=new Date;return r.setTime(24*e*60*60*1e3+Ne),r.getTimezoneOffset()!==De&&r.setTime(r.getTime()+6e4*(r.getTimezoneOffset()-De)),r}function Pe(e){var r=0,t=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(t=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":t*=24;case"H":t*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");t*=60}r+=t*parseInt(n[s],10)}return r}var Me=new Date("2017-02-19T19:06:09.000Z"),Le=isNaN(Me.getFullYear())?new Date("2/19/17"):Me,Ue=2017==Le.getFullYear();function Be(e,r){var t=new Date(e);if(Ue)return r>0?t.setTime(t.getTime()+60*t.getTimezoneOffset()*1e3):r<0&&t.setTime(t.getTime()-60*t.getTimezoneOffset()*1e3),t;if(e instanceof Date)return e;if(1917==Le.getFullYear()&&!isNaN(t.getFullYear())){var a=t.getFullYear();return e.indexOf(""+a)>-1||t.setFullYear(t.getFullYear()+100),t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function Ve(e,r){if(A&&Buffer.isBuffer(e)){if(r){if(255==e[0]&&254==e[1])return yr(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return yr(p(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(r){if(255==e[0]&&254==e[1])return yr(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return yr(new TextDecoder("utf-16be").decode(e.slice(2)))}var t={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return t[e]||e}))}catch(s){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function He(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=He(e[t]));return r}function We(e,r){for(var t="";t.length<r;)t+=e;return t}function ze(e){var r=Number(e);if(!isNaN(r))return isFinite(r)?r:NaN;if(!/\d/.test(e))return r;var t=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return t*=100,""}));return isNaN(r=Number(a))?(a=a.replace(/[(](.*)[)]/,(function(e,r){return t=-t,r})),isNaN(r=Number(a))?r:r/t):r/t}var Ge=["january","february","march","april","may","june","july","august","september","october","november","december"];function $e(e){var r=new Date(e),t=new Date(NaN),a=r.getYear(),n=r.getMonth(),s=r.getDate();if(isNaN(s))return t;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==Ge.indexOf(i))return t}else if(i.match(/[a-z]/))return t;return a<0||a>8099?t:(n>0||s>1)&&101!=a?r:e.match(/[^-0-9:,\/\\]/)?t:r}var je=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(r,t,a){if(e||"string"==typeof t)return r.split(t);for(var n=r.split(t),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function Xe(e){return e?e.content&&e.type?Ve(e.content,!0):e.data?g(e.data):e.asNodeBuffer&&A?g(e.asNodeBuffer().toString("binary")):e.asBinary?g(e.asBinary()):e._data&&e._data.getContent?g(Ve(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Ye(e){if(!e)return null;if(e.data)return d(e.data);if(e.asNodeBuffer&&A)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();return"string"==typeof r?d(r):Array.prototype.slice.call(r)}return e.content&&e.type?e.content:null}function Ke(e,r){for(var t=e.FullPaths||Ce(e.files),a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<t.length;++s){var i=t[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[t[s]]:e.FileIndex[s]}return null}function Je(e,r){var t=Ke(e,r);if(null==t)throw new Error("Cannot find file "+r+" in zip");return t}function qe(e,r,t){if(!t)return(a=Je(e,r))&&".bin"===a.name.slice(-4)?Ye(a):Xe(a);var a;if(!r)return null;try{return qe(e,r)}catch(n){return null}}function Ze(e,r,t){if(!t)return Xe(Je(e,r));if(!r)return null;try{return Ze(e,r)}catch(a){return null}}function Qe(e,r,t){if(!t)return Ye(Je(e,r));if(!r)return null;try{return Qe(e,r)}catch(a){return null}}function er(e){for(var r=e.FullPaths||Ce(e.files),t=[],a=0;a<r.length;++a)"/"!=r[a].slice(-1)&&t.push(r[a].replace(/^Root Entry[\/]/,""));return t.sort()}function rr(e,r,t){if(e.FullPaths){var a;if("string"==typeof t)return a=A?S(t):function(e){for(var r=[],t=0,a=e.length+250,n=k(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[t++]=i;else if(i<2048)n[t++]=192|i>>6&31,n[t++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var c=1023&e.charCodeAt(++s);n[t++]=240|i>>8&7,n[t++]=128|i>>2&63,n[t++]=128|c>>6&15|(3&i)<<4,n[t++]=128|63&c}else n[t++]=224|i>>12&15,n[t++]=128|i>>6&63,n[t++]=128|63&i;t>a&&(r.push(n.slice(0,t)),t=0,n=k(65535),a=65530)}return r.push(n.slice(0,t)),O(r)}(t),_e.utils.cfb_add(e,r,a);_e.utils.cfb_add(e,r,t)}else e.file(r,t)}function tr(e,r){switch(r.type){case"base64":return _e.read(e,{type:"base64"});case"binary":return _e.read(e,{type:"binary"});case"buffer":case"array":return _e.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+r.type)}function ar(e,r){if("/"==e.charAt(0))return e.slice(1);var t=r.split("/");"/"!=r.slice(-1)&&t.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?t.pop():"."!==n&&t.push(n)}return t.join("/")}var nr='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',sr=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,ir=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,cr=nr.match(ir)?ir:/<[^>]*>/g,or=/<\w*:/,lr=/<(\/?)\w+:/;function fr(e,r,t){for(var a={},n=0,s=0;n!==e.length&&(32!==(s=e.charCodeAt(n))&&10!==s&&13!==s);++n);if(r||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(sr),c=0,o="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l],s=0;s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,o=h.slice(s+1+u,h.length-u),c=0;c!=f.length&&58!==f.charCodeAt(c);++c);if(c===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=o,t||(a[f.toLowerCase()]=o);else{var d=(5===c&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(c+1);if(a[d]&&"ext"==f.slice(c-3,c))continue;a[d]=o,t||(a[d.toLowerCase()]=o)}}return a}function hr(e){return e.replace(lr,"<$1")}var ur={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},dr=xe(ur),pr=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,r=/_x([\da-fA-F]{4})_/gi;return function t(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,r){return ur[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e})).replace(r,(function(e,r){return String.fromCharCode(parseInt(r,16))}));var i=n.indexOf("]]>");return t(n.slice(0,s))+n.slice(s+9,i)+t(n.slice(i+3))}}(),mr=/[&<>'"]/g,gr=/[\u0000-\u001f]/g;function vr(e){return(e+"").replace(mr,(function(e){return dr[e]})).replace(/\n/g,"<br/>").replace(gr,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}var br=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function(t){return t.replace(e,r)}}();function Tr(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Er(e){for(var r="",t=0,a=0,n=0,s=0,i=0,c=0;t<e.length;)(a=e.charCodeAt(t++))<128?r+=String.fromCharCode(a):(n=e.charCodeAt(t++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,r+=String.fromCharCode(i)):(s=e.charCodeAt(t++),a<240?r+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(c=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&(i=e.charCodeAt(t++)))-65536,r+=String.fromCharCode(55296+(c>>>10&1023)),r+=String.fromCharCode(56320+(1023&c)))));return r}function wr(e){var r,t,a,n=k(2*e.length),s=1,i=0,c=0;for(t=0;t<e.length;t+=s)s=1,(a=e.charCodeAt(t))<128?r=a:a<224?(r=64*(31&a)+(63&e.charCodeAt(t+1)),s=2):a<240?(r=4096*(15&a)+64*(63&e.charCodeAt(t+1))+(63&e.charCodeAt(t+2)),s=3):(s=4,r=262144*(7&a)+4096*(63&e.charCodeAt(t+1))+64*(63&e.charCodeAt(t+2))+(63&e.charCodeAt(t+3)),c=55296+((r-=65536)>>>10&1023),r=56320+(1023&r)),0!==c&&(n[i++]=255&c,n[i++]=c>>>8,c=0),n[i++]=r%256,n[i++]=r>>>8;return n.slice(0,i).toString("ucs2")}function Ar(e){return S(e,"binary").toString("utf8")}var Sr="foo bar bazâð£",kr=A&&(Ar(Sr)==Er(Sr)&&Ar||wr(Sr)==Er(Sr)&&wr)||Er,yr=A?function(e){return S(e,"utf8").toString("binary")}:function(e){for(var r=[],t=0,a=0,n=0;t<e.length;)switch(a=e.charCodeAt(t++),!0){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6))),r.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(t++)-56320+(a<<10),r.push(String.fromCharCode(240+(n>>18&7))),r.push(String.fromCharCode(144+(n>>12&63))),r.push(String.fromCharCode(128+(n>>6&63))),r.push(String.fromCharCode(128+(63&n)));break;default:r.push(String.fromCharCode(224+(a>>12))),r.push(String.fromCharCode(128+(a>>6&63))),r.push(String.fromCharCode(128+(63&a)))}return r.join("")},_r=function(){var e={};return function(r,t){var a=r+"|"+(t||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",t||"")}}(),Cr=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(r){for(var t=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)t=t.replace(e[a][0],e[a][1]);return t}}(),xr=function(){var e={};return function(r){return void 0!==e[r]?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),Or=/<\/?(?:vt:)?variant>/g,Rr=/<(?:vt:)([^>]*)>([\s\S]*)</;function Ir(e,r){var t=fr(e),a=e.match(xr(t.baseType))||[],n=[];if(a.length!=t.size){if(r.WTF)throw new Error("unexpected vector length "+a.length+" != "+t.size);return n}return a.forEach((function(e){var r=e.replace(Or,"").match(Rr);r&&n.push({v:kr(r[2]),t:r[1]})})),n}var Nr=/(^\s|\s$|\n)/;function Dr(e,r,t){return"<"+e+(null!=t?function(e){return Ce(e).map((function(r){return" "+r+'="'+e[r]+'"'})).join("")}(t):"")+(null!=r?(r.match(Nr)?' xml:space="preserve"':"")+">"+r+"</"+e:"/")+">"}function Fr(e){if(A&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return kr(C(x(e)));throw new Error("Bad input format: expected Buffer or string")}var Pr=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm,Mr="http://schemas.openxmlformats.org/package/2006/content-types",Lr=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];var Ur=function(e){for(var r=[],t=0;t<e[0].length;++t)if(e[0][t])for(var a=0,n=e[0][t].length;a<n;a+=10240)r.push.apply(r,e[0][t].slice(a,a+10240));return r},Br=A?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:S(e)}))):Ur(e)}:Ur,Vr=function(e,r,t){for(var a=[],n=r;n<t;n+=2)a.push(String.fromCharCode(it(e,n)));return a.join("").replace(R,"")},Hr=A?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf16le",r,t).replace(R,""):Vr(e,r,t)}:Vr,Wr=function(e,r,t){for(var a=[],n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},zr=A?function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):Wr(e,r,t)}:Wr,Gr=function(e,r,t){for(var a=[],n=r;n<t;n++)a.push(String.fromCharCode(st(e,n)));return a.join("")},$r=A?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):Gr(e,r,t)}:Gr,jr=function(e,r){var t=ot(e,r);return t>0?$r(e,r+4,r+4+t-1):""},Xr=jr,Yr=function(e,r){var t=ot(e,r);return t>0?$r(e,r+4,r+4+t-1):""},Kr=Yr,Jr=function(e,r){var t=2*ot(e,r);return t>0?$r(e,r+4,r+4+t-1):""},qr=Jr,Zr=function(e,r){var t=ot(e,r);return t>0?Hr(e,r+4,r+4+t):""},Qr=Zr,et=function(e,r){var t=ot(e,r);return t>0?$r(e,r+4,r+4+t):""},rt=et,tt=function(e,r){return function(e,r){for(var t=1-2*(e[r+7]>>>7),a=((127&e[r+7])<<4)+(e[r+6]>>>4&15),n=15&e[r+6],s=5;s>=0;--s)n=256*n+e[r+s];return 2047==a?0==n?t*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),t*Math.pow(2,a-52)*n)}(e,r)},at=tt,nt=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};A&&(Xr=function(e,r){if(!Buffer.isBuffer(e))return jr(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},Kr=function(e,r){if(!Buffer.isBuffer(e))return Yr(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},qr=function(e,r){if(!Buffer.isBuffer(e))return Jr(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)},Qr=function(e,r){if(!Buffer.isBuffer(e))return Zr(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)},rt=function(e,r){if(!Buffer.isBuffer(e))return et(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)},at=function(e,r){return Buffer.isBuffer(e)?e.readDoubleLE(r):tt(e,r)},nt=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array});var st=function(e,r){return e[r]},it=function(e,r){return 256*e[r+1]+e[r]},ct=function(e,r){var t=256*e[r+1]+e[r];return t<32768?t:-1*(65535-t+1)},ot=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]},lt=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]},ft=function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]};function ht(e,r){var t,a,n,s,i,c,o="",l=[];switch(r){case"dbcs":if(c=this.l,A&&Buffer.isBuffer(this))o=this.slice(this.l,this.l+2*e).toString("utf16le");else for(i=0;i<e;++i)o+=String.fromCharCode(it(this,c)),c+=2;e*=2;break;case"utf8":o=$r(this,this.l,this.l+e);break;case"utf16le":e*=2,o=Hr(this,this.l,this.l+e);break;case"wstr":return ht.call(this,e,"dbcs");case"lpstr-ansi":o=Xr(this,this.l),e=4+ot(this,this.l);break;case"lpstr-cp":o=Kr(this,this.l),e=4+ot(this,this.l);break;case"lpwstr":o=qr(this,this.l),e=4+2*ot(this,this.l);break;case"lpp4":e=4+ot(this,this.l),o=Qr(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+ot(this,this.l),o=rt(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,o="";0!==(n=st(this,this.l+e++));)l.push(v(n));o=l.join("");break;case"_wstr":for(e=0,o="";0!==(n=it(this,this.l+e));)l.push(v(n)),e+=2;e+=2,o=l.join("");break;case"dbcs-cont":for(o="",c=this.l,i=0;i<e;++i){if(this.lens&&-1!==this.lens.indexOf(c))return n=st(this,c),this.l=c+1,s=ht.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(v(it(this,c))),c+=2}o=l.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(o="",c=this.l,i=0;i!=e;++i){if(this.lens&&-1!==this.lens.indexOf(c))return n=st(this,c),this.l=c+1,s=ht.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(v(st(this,c))),c+=1}o=l.join("");break;default:switch(e){case 1:return t=st(this,this.l),this.l++,t;case 2:return t=("i"===r?ct:it)(this,this.l),this.l+=2,t;case 4:case-4:return"i"===r||0==(128&this[this.l+3])?(t=(e>0?lt:ft)(this,this.l),this.l+=4,t):(a=ot(this,this.l),this.l+=4,a);case 8:case-8:if("f"===r)return a=8==e?at(this,this.l):at([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:o=zr(this,this.l,e)}}return this.l+=e,o}var ut=function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255};function dt(e,r,t){var a=0,n=0;if("dbcs"===t){for(n=0;n!=r.length;++n)ut(this,r.charCodeAt(n),this.l+2*n);a=2*r.length}else if("sbcs"===t){for(r=r.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=r.length;++n)this[this.l+n]=255&r.charCodeAt(n);a=r.length}else{if("hex"===t){for(;n<e;++n)this[this.l++]=parseInt(r.slice(2*n,2*n+2),16)||0;return this}if("utf16le"===t){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(r.length,e);++n){var i=r.charCodeAt(n);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}switch(e){case 1:a=1,this[this.l]=255&r;break;case 2:a=2,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r;break;case 3:a=3,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r,r>>>=8,this[this.l+2]=255&r;break;case 4:a=4,function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255,e[t+2]=r>>>16&255,e[t+3]=r>>>24&255}(this,r,this.l);break;case 8:if(a=8,"f"===t){!function(e,r,t){var a=(r<0||1/r==-1/0?1:0)<<7,n=0,s=0,i=a?-r:r;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(r)?26985:0);for(var c=0;c<=5;++c,s/=256)e[t+c]=255&s;e[t+6]=(15&n)<<4|15&s,e[t+7]=n>>4|a}(this,r,this.l);break}case 16:break;case-4:a=4,function(e,r,t){e[t]=255&r,e[t+1]=r>>8&255,e[t+2]=r>>16&255,e[t+3]=r>>24&255}(this,r,this.l)}}return this.l+=a,this}function pt(e,r){var t=zr(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function mt(e,r){e.l=r,e.read_shift=ht,e.chk=pt,e.write_shift=dt}function gt(e,r){e.l+=r}function vt(e){var r=k(e);return mt(r,0),r}function bt(e,r,t){if(e){var a,n,s;mt(e,e.l||0);for(var i=e.length,c=0,o=0;e.l<i;){128&(c=e.read_shift(1))&&(c=(127&c)+((127&e.read_shift(1))<<7));var l=kc[c]||kc[65535];for(s=127&(a=e.read_shift(1)),n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;o=e.l+s;var f=l.f&&l.f(e,s,t);if(e.l=o,r(f,l,c))return}}}function Tt(){var e=[],r=A?256:2048,t=function(e){var r=vt(e);return mt(r,0),r},a=t(r),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=t(Math.max(e+1,r)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(r)},end:function(){return n(),O(e)},_bufs:e}}function Et(e,r,t){var a=He(e);if(r.s?(a.cRel&&(a.c+=r.s.c),a.rRel&&(a.r+=r.s.r)):(a.cRel&&(a.c+=r.c),a.rRel&&(a.r+=r.r)),!t||t.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function wt(e,r,t){var a=He(e);return a.s=Et(a.s,r.s,t),a.e=Et(a.e,r.s,t),a}function At(e,r){if(e.cRel&&e.c<0)for(e=He(e);e.c<0;)e.c+=r>8?16384:256;if(e.rRel&&e.r<0)for(e=He(e);e.r<0;)e.r+=r>8?1048576:r>5?65536:16384;var t=Ot(e);return e.cRel||null==e.cRel||(t=t.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(t=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(t)),t}function St(e,r){return 0!=e.s.r||e.s.rRel||e.e.r!=(r.biff>=12?1048575:r.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(r.biff>=12?16383:255)||e.e.cRel?At(e.s,r.biff)+":"+At(e.e,r.biff):(e.s.rRel?"":"$")+yt(e.s.r)+":"+(e.e.rRel?"":"$")+yt(e.e.r):(e.s.cRel?"":"$")+Ct(e.s.c)+":"+(e.e.cRel?"":"$")+Ct(e.e.c)}function kt(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function yt(e){return""+(e+1)}function _t(e){for(var r=e.replace(/^\$([A-Z])/,"$1"),t=0,a=0;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function Ct(e){if(e<0)throw new Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function xt(e){for(var r=0,t=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?r=10*r+(n-48):n>=65&&n<=90&&(t=26*t+(n-64))}return{c:t-1,r:r-1}}function Ot(e){for(var r=e.c+1,t="";r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function Rt(e){var r=e.indexOf(":");return-1==r?{s:xt(e),e:xt(e)}:{s:xt(e.slice(0,r)),e:xt(e.slice(r+1))}}function It(e,r){return void 0===r||"number"==typeof r?It(e.s,e.e):("string"!=typeof e&&(e=Ot(e)),"string"!=typeof r&&(r=Ot(r)),e==r?e:e+":"+r)}function Nt(e){var r={s:{c:0,r:0},e:{c:0,r:0}},t=0,a=0,n=0,s=e.length;for(t=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)t=26*t+n;for(r.s.c=--t,t=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)t=10*t+n;if(r.s.r=--t,a===s||10!=n)return r.e.c=r.s.c,r.e.r=r.s.r,r;for(++a,t=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)t=26*t+n;for(r.e.c=--t,t=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)t=10*t+n;return r.e.r=--t,r}function Dt(e,r){var t="d"==e.t&&r instanceof Date;if(null!=e.z)try{return e.w=Ee(e.z,t?Re(r):r)}catch(a){}try{return e.w=Ee((e.XF||{}).numFmtId||(t?14:0),t?Re(r):r)}catch(a){return""+r}}function Ft(e,r,t){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&t&&t.dateNF&&(e.z=t.dateNF),"e"==e.t?ca[e.v]||e.v:Dt(e,null==r?e.v:r))}function Pt(e,r){var t=r&&r.sheet?r.sheet:"Sheet1",a={};return a[t]=e,{SheetNames:[t],Sheets:a}}function Mt(e,r,t){var a=t||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,c=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var o="string"==typeof a.origin?xt(a.origin):a.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=Nt(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=r.length;++h)if(r[h]){if(!Array.isArray(r[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=r[h].length;++u)if(void 0!==r[h][u]){var d={v:r[h][u]},p=i+h,m=c+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!r[h][u]||"object"!=typeof r[h][u]||Array.isArray(r[h][u])||r[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=r[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||H[14],a.cellDates?(d.t="d",d.w=Ee(d.z,Re(d.v))):(d.t="n",d.v=Re(d.v),d.w=Ee(d.z,d.v))):d.t="s";else d=r[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=Ot({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=It(l)),s}function Lt(e,r){return Mt(null,e,r)}function Ut(e){var r=e.read_shift(4);return 0===r?"":e.read_shift(r,"dbcs")}function Bt(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Vt(e,r){var t=e.l,a=e.read_shift(1),n=Ut(e),s=[],i={t:n,h:n};if(0!=(1&a)){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push(Bt(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=t+r,i}var Ht=Vt;function Wt(e){var r=e.read_shift(4),t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:r,iStyleRef:t}}function zt(e){var r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:r}}var Gt=Ut;function $t(e){var r=e.read_shift(4);return 0===r||4294967295===r?"":e.read_shift(r,"dbcs")}var jt=Ut,Xt=$t;function Yt(e){var r=e.slice(e.l,e.l+4),t=1&r[0],a=2&r[0];e.l+=4;var n=0===a?at([0,0,0,0,252&r[0],r[1],r[2],r[3]],0):lt(r,0)>>2;return t?n/100:n}function Kt(e){var r={s:{},e:{}};return r.s.r=e.read_shift(4),r.e.r=e.read_shift(4),r.s.c=e.read_shift(4),r.e.c=e.read_shift(4),r}var Jt=Kt;function qt(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Zt(e,r){var t=e.read_shift(4);switch(t){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(t>400)throw new Error("Unsupported Clipboard: "+t.toString(16));return e.l-=4,e.read_shift(0,1==r?"lpstr":"lpwstr")}var Qt=80,ea=[Qt,81],ra={1:{n:"CodePage",t:2},2:{n:"Category",t:Qt},3:{n:"PresentationFormat",t:Qt},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:Qt},15:{n:"Company",t:Qt},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:Qt},27:{n:"ContentStatus",t:Qt},28:{n:"Language",t:Qt},29:{n:"Version",t:Qt},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},ta={1:{n:"CodePage",t:2},2:{n:"Title",t:Qt},3:{n:"Subject",t:Qt},4:{n:"Author",t:Qt},5:{n:"Keywords",t:Qt},6:{n:"Comments",t:Qt},7:{n:"Template",t:Qt},8:{n:"LastAuthor",t:Qt},9:{n:"RevNumber",t:Qt},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:Qt},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},aa={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},na=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function sa(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var ia=He(sa([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),ca={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},oa={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},la={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};var fa={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ha(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function ua(e,r){var t={"!id":{}};if(!e)return t;"/"!==r.charAt(0)&&(r="/"+r);var a={};return(e.match(cr)||[]).forEach((function(e){var n=fr(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:ar(n.Target,r);t[i]=s,a[n.Id]=s}})),t["!id"]=a,t}var da=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],pa=function(){for(var e=new Array(da.length),r=0;r<da.length;++r){var t=da[r],a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function ma(e){var r={};e=kr(e);for(var t=0;t<da.length;++t){var a=da[t],n=e.match(pa[t]);null!=n&&n.length>0&&(r[a[1]]=pr(n[1])),"date"===a[2]&&r[a[1]]&&(r[a[1]]=Be(r[a[1]]))}return r}var ga=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function va(e,r,t,a){var n=[];if("string"==typeof e)n=Ir(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof r?Ir(r,a).map((function(e){return e.v})):r,c=0,o=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(o=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":t.Worksheets=o,t.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":t.NamedRanges=o,t.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":t.Chartsheets=o,t.ChartNames=i.slice(c,c+o)}c+=o}}var ba=/<[^>]+>[^<]*/g;var Ta,Ea={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function wa(e,r,t){Ta||(Ta=xe(Ea)),e[r=Ta[r]||r]=t}function Aa(e){var r=e.read_shift(4),t=e.read_shift(4);return new Date(1e3*(t/1e7*Math.pow(2,32)+r/1e7-11644473600)).toISOString().replace(/\.000/,"")}function Sa(e,r,t){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(t)for(;e.l-a&3;)++e.l;return n}function ka(e,r,t){var a=e.read_shift(0,"lpwstr");return t&&(e.l+=4-(a.length+1&3)&3),a}function ya(e,r,t){return 31===r?ka(e):Sa(e,0,t)}function _a(e,r,t){return ya(e,r,!1===t?0:4)}function Ca(e){var r=e.l,t=Ra(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-r&2&&(e.l+=2),[t,Ra(e,3)]}function xa(e,r){for(var t=e.read_shift(4),a={},n=0;n!=t;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===r?"utf16le":"utf8").replace(R,"").replace(I,"!"),1200===r&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Oa(e){var r=e.read_shift(4),t=e.slice(e.l,e.l+r);return e.l+=r,(3&r)>0&&(e.l+=4-(3&r)&3),t}function Ra(e,r,t){var a,n=e.read_shift(2),s=t||{};if(e.l+=2,12!==r&&n!==r&&-1===ea.indexOf(r)&&(4126!=(65534&r)||4126!=(65534&n)))throw new Error("Expected type "+r+" saw "+n);switch(12===r?n:r){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return a=e.read_shift(4);case 30:return Sa(e,0,4).replace(R,"");case 31:return ka(e);case 64:return Aa(e);case 65:return Oa(e);case 71:return function(e){var r={};return r.Size=e.read_shift(4),e.l+=r.Size+3-(r.Size-1)%4,r}(e);case 80:return _a(e,n,!s.raw).replace(R,"");case 81:return function(e,r){if(!r)throw new Error("VtUnalignedString must have positive length");return ya(e,r,0)}(e,n).replace(R,"");case 4108:return function(e){for(var r=e.read_shift(4),t=[],a=0;a<r/2;++a)t.push(Ca(e));return t}(e);case 4126:case 4127:return 4127==n?function(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a){var n=e.l;t[a]=e.read_shift(0,"lpwstr").replace(R,""),e.l-n&2&&(e.l+=2)}return t}(e):function(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a)t[a]=e.read_shift(0,"lpstr-cp").replace(R,"");return t}(e);default:throw new Error("TypedPropertyValue unrecognized type "+r+" "+n)}}function Ia(e,r){var t=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,c=0,o=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),u=e.read_shift(4);s[i]=[f,u+t]}s.sort((function(e,r){return e[1]-r[1]}));var d={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&r)switch(r[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1)}if((!r||0==i)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(r){var m=r[s[i][0]];if(d[m.n]=Ra(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:h(c=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===s[i][0]){if(c=d.CodePage=Ra(e,2),h(c),-1!==o){var g=e.l;e.l=s[o][1],l=xa(e,c),e.l=g}}else if(0===s[i][0]){if(0===c){o=i,e.l=s[i+1][1];continue}l=xa(e,c)}else{var v,b=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=Oa(e);break;case 30:case 31:e.l+=4,v=_a(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=Fa(e,4);break;case 64:e.l+=4,v=Be(Aa(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[b]=v}}return e.l=t+a,d}function Na(e,r,t){var a=e.content;if(!a)return{};mt(a,0);var n,s,i,c,o=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==_e.utils.consts.HEADER_CLSID&&f!==t)throw new Error("Bad PropertySet CLSID "+f);if(1!==(n=a.read_shift(4))&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),c=a.read_shift(4),1===n&&c!==a.l)throw new Error("Length mismatch: "+c+" !== "+a.l);2===n&&(i=a.read_shift(16),o=a.read_shift(4));var h,u=Ia(a,r),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(o-a.l==2&&(a.l+=2),a.l!==o)throw new Error("Length mismatch 2: "+a.l+" !== "+o);try{h=Ia(a,null)}catch(m){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function Da(e,r){return e.read_shift(r),null}function Fa(e,r){return 1===e.read_shift(r)}function Pa(e){return e.read_shift(2,"u")}function Ma(e,r){return function(e,r,t){for(var a=[],n=e.l+r;e.l<n;)a.push(t(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}(e,r,Pa)}function La(e,r,t){var a=e.read_shift(t&&t.biff>=12?2:1),n="sbcs-cont";(t&&t.biff,t&&8!=t.biff)?12==t.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont");return t.biff>=2&&t.biff<=5&&(n="cpstr"),a?e.read_shift(a,n):""}function Ua(e){var r,t=e.read_shift(2),a=e.read_shift(1),n=4&a,s=8&a,i=1+(1&a),c=0,o={};s&&(c=e.read_shift(2)),n&&(r=e.read_shift(4));var l=2==i?"dbcs-cont":"sbcs-cont",f=0===t?"":e.read_shift(t,l);return s&&(e.l+=4*c),n&&(e.l+=r),o.t=f,s||(o.raw="<t>"+o.t+"</t>",o.r=o.t),o}function Ba(e,r,t){if(t){if(t.biff>=2&&t.biff<=5)return e.read_shift(r,"cpstr");if(t.biff>=12)return e.read_shift(r,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(r,"sbcs-cont"):e.read_shift(r,"dbcs-cont")}function Va(e,r,t){var a=e.read_shift(t&&2==t.biff?1:2);return 0===a?(e.l++,""):Ba(e,a,t)}function Ha(e,r,t){if(t.biff>5)return Va(e,0,t);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,t.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Wa(e,r){var t=e.read_shift(16);switch(t){case"e0c9ea79f9bace118c8200aa004ba90b":return function(e){var r=e.read_shift(4),t=e.l,a=!1;r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=t);var n=e.read_shift((a?r-24:r)>>1,"utf16le").replace(R,"");return a&&(e.l+=24),n}(e);case"0303000000000000c000000000000046":return function(e){for(var r=e.read_shift(2),t="";r-- >0;)t+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return t+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");return t+e.read_shift(n>>1,"utf16le").replace(R,"")}(e);default:throw new Error("Unsupported Moniker "+t)}}function za(e){var r=e.read_shift(4);return r>0?e.read_shift(r,"utf16le").replace(R,""):""}function Ga(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function $a(e,r){var t=Ga(e);return t[3]=0,t}function ja(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function Xa(e,r,t){var a=t.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}function Ya(e){return[e.read_shift(2),Yt(e)]}function Ka(e){var r=e.read_shift(2),t=e.read_shift(2);return{s:{c:e.read_shift(2),r:r},e:{c:e.read_shift(2),r:t}}}function Ja(e){var r=e.read_shift(2),t=e.read_shift(2);return{s:{c:e.read_shift(1),r:r},e:{c:e.read_shift(1),r:t}}}var qa=Ja;function Za(e){e.l+=4;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[t,r,a]}function Qa(e){e.l+=2,e.l+=e.read_shift(2)}var en={0:Qa,4:Qa,5:Qa,6:Qa,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:Qa,9:Qa,10:Qa,11:Qa,12:Qa,13:function(e){var r={};return e.l+=4,e.l+=16,r.fSharedNote=e.read_shift(2),e.l+=4,r},14:Qa,15:Qa,16:Qa,17:Qa,18:Qa,19:Qa,20:Qa,21:Za};function rn(e,r){var t={BIFFVer:0,dt:0};switch(t.BIFFVer=e.read_shift(2),(r-=2)>=2&&(t.dt=e.read_shift(2),e.l-=2),t.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(r>6)throw new Error("Unexpected BIFF Ver "+t.BIFFVer)}return e.read_shift(r),t}function tn(e,r,t){var a=0;t&&2==t.biff||(a=e.read_shift(2));var n=e.read_shift(2);return t&&2==t.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}var an=Ha;function nn(e,r,t){var a=e.l+r,n=8!=t.biff&&t.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(2),o=e.read_shift(2);return e.l=a,{s:{r:s,c:c},e:{r:i,c:o}}}function sn(e,r,t){var a=ja(e);2!=t.biff&&9!=r||++e.l;var n=function(e){var r=e.read_shift(1);return 1===e.read_shift(1)?r:1===r}(e);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}var cn=function(e,r,t){return 0===r?"":Ha(e,0,t)};function on(e,r,t){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===t.sbcch&&(a=function(e,r,t){e.l+=4,r-=4;var a=e.l+r,n=La(e,0,t),s=e.read_shift(2);if(s!==(a-=e.l))throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,r-2,t)),s.body=a||e.read_shift(r-2),"string"==typeof a&&(s.Name=a),s}var ln=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function fn(e,r,t){var a=e.l+r,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(t&&2==t.biff?1:2),o=0;(!t||t.biff>=5)&&(5!=t.biff&&(e.l+=2),o=e.read_shift(2),5==t.biff&&(e.l+=2),e.l+=4);var l=Ba(e,i,t);32&n&&(l=ln[l.charCodeAt(0)]);var f=a-e.l;t&&2==t.biff&&--f;var h=a!=e.l&&0!==c&&f>0?function(e,r,t,a){var n,s=e.l+r,i=Js(e,a,t);s!==e.l&&(n=Ks(e,s-e.l,i,t));return[i,n]}(e,f,t,c):[];return{chKey:s,Name:l,itab:o,rgce:h}}function hn(e,r,t){if(t.biff<8)return function(e,r,t){3==e[e.l+1]&&e[e.l]++;var a=La(e,0,t);return 3==a.charCodeAt(0)?a.slice(1):a}(e,0,t);for(var a=[],n=e.l+r,s=e.read_shift(t.biff>8?4:2);0!=s--;)a.push(Xa(e,t.biff,t));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function un(e,r,t){var a=qa(e);switch(t.biff){case 2:e.l++,r-=7;break;case 3:case 4:e.l+=2,r-=8;break;default:e.l+=6,r-=12}return[a,ti(e,r,t)]}var dn={8:function(e,r){var t=e.l+r;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=t,{fmt:a}}};function pn(e,r,t){if(!t.cellStyles)return gt(e,r);var a=t&&t.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(a),o=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:c,flags:o};return(t.biff>=5||!t.biff)&&(l.level=o>>8&7),l}var mn=ja,gn=Ma,vn=Va;var bn=[2,3,48,49,131,139,140,245],Tn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},r=xe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function t(r,t){var a=t||{};a.dateNF||(a.dateNF="yyyymmdd");var s=Lt(function(r,t){var a=[],s=k(1);switch(t.type){case"base64":s=_(w(r));break;case"binary":s=_(r);break;case"buffer":case"array":s=r}mt(s,0);var i=s.read_shift(1),c=!!(136&i),o=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,c=!0;break;case 140:l=!0;break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=t.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],g={},v=Math.min(s.length,2==i?521:h-10-(o?264:0)),b=l?32:11;s.l<v&&13!=s[s.l];)switch((g={}).name=m.utils.decode(d,s.slice(s.l,s.l+b)).replace(/[\u0000\r\n].*$/g,""),s.l+=b,g.type=String.fromCharCode(s.read_shift(1)),2==i||l||(g.offset=s.read_shift(4)),g.len=s.read_shift(1),2==i&&(g.offset=s.read_shift(2)),g.dec=s.read_shift(1),g.name.length&&p.push(g),2!=i&&(s.l+=l?13:14),g.type){case"B":o&&8==g.len||!t.WTF||n("log","at node_modules/xlsx/xlsx.mjs:7656","Skipping "+g.name+":"+g.type);break;case"G":case"P":t.WTF&&n("log","at node_modules/xlsx/xlsx.mjs:7660","Skipping "+g.name+":"+g.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+g.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw new Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,E=0;for(a[0]=[],E=0;E!=p.length;++E)a[0][E]=p[E].name;for(;f-- >0;)if(42!==s[s.l])for(++s.l,a[++T]=[],E=0,E=0;E!=p.length;++E){var A=s.slice(s.l,s.l+p[E].len);s.l+=p[E].len,mt(A,0);var S=m.utils.decode(d,A);switch(p[E].type){case"C":S.trim().length&&(a[T][E]=S.replace(/\s+$/,""));break;case"D":8===S.length?a[T][E]=new Date(+S.slice(0,4),+S.slice(4,6)-1,+S.slice(6,8)):a[T][E]=S;break;case"F":a[T][E]=parseFloat(S.trim());break;case"+":case"I":a[T][E]=l?2147483648^A.read_shift(-4,"i"):A.read_shift(4,"i");break;case"L":switch(S.trim().toUpperCase()){case"Y":case"T":a[T][E]=!0;break;case"N":case"F":a[T][E]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+S+"|")}break;case"M":if(!c)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));a[T][E]="##MEMO##"+(l?parseInt(S.trim(),10):A.read_shift(4));break;case"N":(S=S.replace(/\u0000/g,"").trim())&&"."!=S&&(a[T][E]=+S||0);break;case"@":a[T][E]=new Date(A.read_shift(-8,"f")-621356832e5);break;case"T":a[T][E]=new Date(864e5*(A.read_shift(4)-2440588)+A.read_shift(4));break;case"Y":a[T][E]=A.read_shift(4,"i")/1e4+A.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":a[T][E]=-A.read_shift(-8,"f");break;case"B":if(o&&8==p[E].len){a[T][E]=A.read_shift(8,"f");break}case"G":case"P":A.l+=p[E].len;break;case"0":if("_NullFlags"===p[E].name)break;default:throw new Error("DBF Unsupported data type "+p[E].type)}}else s.l+=u;if(2!=i&&s.l<s.length&&26!=s[s.l++])throw new Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return t&&t.sheetRows&&(a=a.slice(0,t.sheetRows)),t.DBF=p,a}(r,a),a);return s["!cols"]=a.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,r){try{return Pt(t(e,r),r)}catch(a){if(r&&r.WTF)throw a}return{SheetNames:[],Sheets:{}}},to_sheet:t,from_sheet:function(e,t){var n=t||{};if(+n.codepage>=0&&h(+n.codepage),"string"==n.type)throw new Error("Cannot write DBF to JS string");var s=Tt(),i=lo(e,{header:1,raw:!0,cellDates:!0}),o=i[0],l=i.slice(1),f=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<o.length;++u)if(((f[u]||{}).DBF||{}).name)o[u]=f[u].DBF.name,++p;else if(null!=o[u]){if(++p,"number"==typeof o[u]&&(o[u]=o[u].toString(10)),"string"!=typeof o[u])throw new Error("DBF Invalid column name "+o[u]+" |"+typeof o[u]+"|");if(o.indexOf(o[u])!==u)for(d=0;d<1024;++d)if(-1==o.indexOf(o[u]+"_"+d)){o[u]+="_"+d;break}}var g=Nt(e["!ref"]),v=[],b=[],T=[];for(u=0;u<=g.e.c-g.s.c;++u){var E="",w="",A=0,S=[];for(d=0;d<l.length;++d)null!=l[d][u]&&S.push(l[d][u]);if(0!=S.length&&null!=o[u]){for(d=0;d<S.length;++d){switch(typeof S[d]){case"number":w="B";break;case"string":default:w="C";break;case"boolean":w="L";break;case"object":w=S[d]instanceof Date?"D":"C"}A=Math.max(A,String(S[d]).length),E=E&&E!=w?"C":w}A>250&&(A=250),"C"==(w=((f[u]||{}).DBF||{}).type)&&f[u].DBF.len>A&&(A=f[u].DBF.len),"B"==E&&"N"==w&&(E="N",T[u]=f[u].DBF.dec,A=f[u].DBF.len),b[u]="C"==E||"N"==w?A:a[E]||0,m+=b[u],v[u]=E}else v[u]="?"}var k=s.next(32);for(k.write_shift(4,318902576),k.write_shift(4,l.length),k.write_shift(2,296+32*p),k.write_shift(2,m),u=0;u<4;++u)k.write_shift(4,0);for(k.write_shift(4,0|(+r[c]||3)<<8),u=0,d=0;u<o.length;++u)if(null!=o[u]){var y=s.next(32),_=(o[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,_,"sbcs"),y.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),y.write_shift(4,d),y.write_shift(1,b[u]||a[v[u]]||0),y.write_shift(1,T[u]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),d+=b[u]||a[v[u]]||0}var C=s.next(264);for(C.write_shift(4,13),u=0;u<65;++u)C.write_shift(4,0);for(u=0;u<l.length;++u){var x=s.next(m);for(x.write_shift(1,0),d=0;d<o.length;++d)if(null!=o[d])switch(v[d]){case"L":x.write_shift(1,null==l[u][d]?63:l[u][d]?84:70);break;case"B":x.write_shift(8,l[u][d]||0,"f");break;case"N":var O="0";for("number"==typeof l[u][d]&&(O=l[u][d].toFixed(T[d]||0)),p=0;p<b[d]-O.length;++p)x.write_shift(1,32);x.write_shift(1,O,"sbcs");break;case"D":l[u][d]?(x.write_shift(4,("0000"+l[u][d].getFullYear()).slice(-4),"sbcs"),x.write_shift(2,("00"+(l[u][d].getMonth()+1)).slice(-2),"sbcs"),x.write_shift(2,("00"+l[u][d].getDate()).slice(-2),"sbcs")):x.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=l[u][d]?l[u][d]:"").slice(0,b[d]);for(x.write_shift(1,R,"sbcs"),p=0;p<b[d]-R.length;++p)x.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),En=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},r=new RegExp("N("+Ce(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),t=function(r,t){var a=e[t];return"number"==typeof a?b(a):a},a=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return 59==a?e:b(a)};function n(e,n){var s,i=e.split(/[\n\r]+/),c=-1,o=-1,l=0,f=0,u=[],d=[],p=null,m={},g=[],v=[],b=[],T=0;for(+n.codepage>=0&&h(+n.codepage);l!==i.length;++l){T=0;var E,w=i[l].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(r,t),A=w.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),S=A[0];if(w.length>0)switch(S){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==A[1].charAt(0)&&d.push(w.slice(3).replace(/;;/g,";"));break;case"C":var k=!1,y=!1,_=!1,C=!1,x=-1,O=-1;for(f=1;f<A.length;++f)switch(A[f].charAt(0)){case"A":case"G":break;case"X":o=parseInt(A[f].slice(1))-1,y=!0;break;case"Y":for(c=parseInt(A[f].slice(1))-1,y||(o=0),s=u.length;s<=c;++s)u[s]=[];break;case"K":'"'===(E=A[f].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(ze(E))?isNaN($e(E).getDate())||(E=Be(E)):(E=ze(E),null!==p&&ge(p)&&(E=Fe(E))),k=!0;break;case"E":C=!0;var R=ks(A[f].slice(1),{r:c,c:o});u[c][o]=[u[c][o],R];break;case"S":_=!0,u[c][o]=[u[c][o],"S5S"];break;case"R":x=parseInt(A[f].slice(1))-1;break;case"C":O=parseInt(A[f].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}if(k&&(u[c][o]&&2==u[c][o].length?u[c][o][0]=E:u[c][o]=E,p=null),_){if(C)throw new Error("SYLK shared formula cannot have own formula");var I=x>-1&&u[x][O];if(!I||!I[1])throw new Error("SYLK shared formula cannot find base");u[c][o][1]=Cs(I[1],{r:c-x,c:o-O})}break;case"F":var N=0;for(f=1;f<A.length;++f)switch(A[f].charAt(0)){case"X":o=parseInt(A[f].slice(1))-1,++N;break;case"Y":for(c=parseInt(A[f].slice(1))-1,s=u.length;s<=c;++s)u[s]=[];break;case"M":T=parseInt(A[f].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(A[f].slice(1))];break;case"W":for(b=A[f].slice(1).split(" "),s=parseInt(b[0],10);s<=parseInt(b[1],10);++s)T=parseInt(b[2],10),v[s-1]=0===T?{hidden:!0}:{wch:T},ts(v[s-1]);break;case"C":v[o=parseInt(A[f].slice(1))-1]||(v[o]={});break;case"R":g[c=parseInt(A[f].slice(1))-1]||(g[c]={}),T>0?(g[c].hpt=T,g[c].hpx=ns(T)):0===T&&(g[c].hidden=!0);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}N<1&&(p=null);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),n&&n.sheetRows&&(u=u.slice(0,n.sheetRows)),[u,m]}function s(e,r){var t=function(e,r){switch(r.type){case"base64":return n(w(e),r);case"binary":return n(e,r);case"buffer":return n(A&&Buffer.isBuffer(e)?e.toString("binary"):C(e),r);case"array":return n(Ve(e),r)}throw new Error("Unrecognized type "+r.type)}(e,r),a=t[0],s=t[1],i=Lt(a,r);return Ce(s).forEach((function(e){i[e]=s[e]})),i}function i(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+_s(e.f,{r:t,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}return e["|"]=254,{to_workbook:function(e,r){return Pt(s(e,r),r)},to_sheet:s,from_sheet:function(e,r){var t,a,n=["ID;PWXL;N;E"],s=[],c=Nt(e["!ref"]),o=Array.isArray(e),l="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&(a=n,e["!cols"].forEach((function(e,r){var t="F;W"+(r+1)+" "+(r+1)+" ";e.hidden?t+="0":("number"!=typeof e.width||e.wpx||(e.wpx=qn(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=Zn(e.wpx)),"number"==typeof e.wch&&(t+=Math.round(e.wch)))," "!=t.charAt(t.length-1)&&a.push(t)}))),e["!rows"]&&function(e,r){r.forEach((function(r,t){var a="F;";r.hidden?a+="M0;":r.hpt?a+="M"+20*r.hpt+";":r.hpx&&(a+="M"+20*as(r.hpx)+";"),a.length>2&&e.push(a+"R"+(t+1))}))}(n,e["!rows"]),n.push("B;Y"+(c.e.r-c.s.r+1)+";X"+(c.e.c-c.s.c+1)+";D"+[c.s.c,c.s.r,c.e.c,c.e.r].join(" "));for(var f=c.s.r;f<=c.e.r;++f)for(var h=c.s.c;h<=c.e.c;++h){var u=Ot({r:f,c:h});(t=o?(e[f]||[])[h]:e[u])&&(null!=t.v||t.f&&!t.F)&&s.push(i(t,0,f,h))}return n.join(l)+l+s.join(l)+l+"E"+l}}}(),wn=function(){function e(e,r){for(var t=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==t.length;++s)if("BOT"!==t[s].trim()){if(!(a<0)){for(var c=t[s].trim().split(","),o=c[0],l=c[1],f=t[++s]||"";1&(f.match(/["]/g)||[]).length&&s<t.length-1;)f+="\n"+t[++s];switch(f=f.trim(),+o){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(ze(l))?isNaN($e(l).getDate())?i[a][n]=l:i[a][n]=Be(l):i[a][n]=ze(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}else i[++a]=[],n=0;return r&&r.sheetRows&&(i=i.slice(0,r.sheetRows)),i}function r(r,t){return Lt(function(r,t){switch(t.type){case"base64":return e(w(r),t);case"binary":return e(r,t);case"buffer":return e(A&&Buffer.isBuffer(r)?r.toString("binary"):C(r),t);case"array":return e(Ve(r),t)}throw new Error("Unrecognized type "+t.type)}(r,t),t)}return{to_workbook:function(e,t){return Pt(r(e,t),t)},to_sheet:r,from_sheet:function(){var e=function(e,r,t,a,n){e.push(r),e.push(t+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},r=function(e,r,t,a){e.push(r+","+t),e.push(1==r?'"'+a.replace(/"/g,'""')+'"':a)};return function(t){var a,n=[],s=Nt(t["!ref"]),i=Array.isArray(t);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var c=s.s.r;c<=s.e.r;++c){r(n,-1,0,"BOT");for(var o=s.s.c;o<=s.e.c;++o){var l=Ot({r:c,c:o});if(a=i?(t[c]||[])[o]:t[l])switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?a.f&&!a.F?r(n,1,0,"="+a.f):r(n,1,0,""):r(n,0,f,"V");break;case"b":r(n,0,a.v?1:0,a.v?"TRUE":"FALSE");break;case"s":r(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=Ee(a.z||H[14],Re(Be(a.v)))),r(n,0,a.w,"V");break;default:r(n,1,0,"")}else r(n,1,0,"")}}r(n,-1,0,"EOD");return n.join("\r\n")}}()}}(),An=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(e,r){return Lt(function(e,r){for(var t=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==t.length;++s){var c=t[s].trim().split(":");if("cell"===c[0]){var o=xt(c[1]);if(i.length<=o.r)for(a=i.length;a<=o.r;++a)i[a]||(i[a]=[]);switch(a=o.r,n=o.c,c[2]){case"t":i[a][n]=c[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+c[3];break;case"vtf":var l=c[c.length-1];case"vtc":"nl"===c[3]?i[a][n]=!!+c[4]:i[a][n]=+c[4],"vtf"==c[2]&&(i[a][n]=[i[a][n],l])}}}return r&&r.sheetRows&&(i=i.slice(0,r.sheetRows)),i}(e,r),r)}var t=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",n=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function i(r){if(!r||!r["!ref"])return"";for(var t,a=[],n=[],s="",i=Rt(r["!ref"]),c=Array.isArray(r),o=i.s.r;o<=i.e.r;++o)for(var l=i.s.c;l<=i.e.c;++l)if(s=Ot({r:o,c:l}),(t=c?(r[o]||[])[l]:r[s])&&null!=t.v&&"z"!==t.t){switch(n=["cell",s,"t"],t.t){case"s":case"str":n.push(e(t.v));break;case"n":t.f?(n[2]="vtf",n[3]="n",n[4]=t.v,n[5]=e(t.f)):(n[2]="v",n[3]=t.v);break;case"b":n[2]="vt"+(t.f?"f":"c"),n[3]="nl",n[4]=t.v?"1":"0",n[5]=e(t.f||(t.v?"TRUE":"FALSE"));break;case"d":var f=Re(Be(t.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=t.w||Ee(t.z||H[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}return{to_workbook:function(e,t){return Pt(r(e,t),t)},to_sheet:r,from_sheet:function(e){return[t,a,n,a,i(e),s].join("\n")}}}(),Sn=function(){function e(e,r,t,a,n){n.raw?r[t][a]=e:""===e||("TRUE"===e?r[t][a]=!0:"FALSE"===e?r[t][a]=!1:isNaN(ze(e))?isNaN($e(e).getDate())?r[t][a]=e:r[t][a]=Be(e):r[t][a]=ze(e))}var r={44:",",9:"\t",59:";",124:"|"},t={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in r&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=t)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort((function(e,r){return e[0]-r[0]||t[e[1]]-t[r[1]]})),r[i.pop()[1]]||44}function n(e,r){var t=r||{},n="",s=t.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=t&&t.FS?t.FS:a(e.slice(0,1024));var c=0,o=0,l=0,f=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g,v,b=null!=t.dateNF?(g=t.dateNF,v=(v="number"==typeof g?H[g]:g).replace(ke,"(\\d+)"),new RegExp("^"+v+"$")):null;function T(){var r=e.slice(f,h),a={};if('"'==r.charAt(0)&&'"'==r.charAt(r.length-1)&&(r=r.slice(1,-1).replace(/""/g,'"')),0===r.length)a.t="z";else if(t.raw)a.t="s",a.v=r;else if(0===r.trim().length)a.t="s",a.v=r;else if(61==r.charCodeAt(0))34==r.charCodeAt(1)&&34==r.charCodeAt(r.length-1)?(a.t="s",a.v=r.slice(2,-1).replace(/""/g,'"')):1!=r.length?(a.t="n",a.f=r.slice(1)):(a.t="s",a.v=r);else if("TRUE"==r)a.t="b",a.v=!0;else if("FALSE"==r)a.t="b",a.v=!1;else if(isNaN(l=ze(r)))if(!isNaN($e(r).getDate())||b&&r.match(b)){a.z=t.dateNF||H[14];var n=0;b&&r.match(b)&&(r=function(e,r,t){var a=-1,n=-1,s=-1,i=-1,c=-1,o=-1;(r.match(ke)||[]).forEach((function(e,r){var l=parseInt(t[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":o=l;break;case"m":i>=0?c=l:n=l}})),o>=0&&-1==c&&n>=0&&(c=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return-1==i&&-1==c&&-1==o?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}(0,t.dateNF,r.match(b)||[]),n=1),t.cellDates?(a.t="d",a.v=Be(r,n)):(a.t="n",a.v=Re(Be(r,n))),!1!==t.cellText&&(a.w=Ee(a.z,a.v instanceof Date?Re(a.v):a.v)),t.cellNF||delete a.z}else a.t="s",a.v=r;else a.t="n",!1!==t.cellText&&(a.w=r),a.v=l;if("z"==a.t||(t.dense?(s[c]||(s[c]=[]),s[c][o]=a):s[Ot({c:o,r:c})]=a),f=h+1,m=e.charCodeAt(f),i.e.c<o&&(i.e.c=o),i.e.r<c&&(i.e.r=c),p==u)++o;else if(o=0,++c,t.sheetRows&&t.sheetRows<=c)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&T())break e}return h-f>0&&T(),s["!ref"]=It(i),s}function s(r,t){return t&&t.PRN?t.FS||"sep="==r.slice(0,4)||r.indexOf("\t")>=0||r.indexOf(",")>=0||r.indexOf(";")>=0?n(r,t):Lt(function(r,t){var a=t||{},n=[];if(!r||0===r.length)return n;for(var s=r.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var c=10,o=0,l=0;l<=i;++l)-1==(o=s[l].indexOf(" "))?o=s[l].length:o++,c=Math.max(c,o);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,c).trim(),n,l,f,a),f=1;f<=(s[l].length-c)/10+1;++f)e(s[l].slice(c+10*(f-1),c+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(r,t),t):n(r,t)}function i(e,r){var t="",a="string"==r.type?[0,0,0,0]:no(e,r);switch(r.type){case"base64":t=w(e);break;case"binary":case"string":t=e;break;case"buffer":65001==r.codepage?t=e.toString("utf8"):(r.codepage,t=A&&Buffer.isBuffer(e)?e.toString("binary"):C(e));break;case"array":t=Ve(e);break;default:throw new Error("Unrecognized type "+r.type)}return 239==a[0]&&187==a[1]&&191==a[2]?t=kr(t.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?t=kr(t):r.type,"socialcalc:version:"==t.slice(0,19)?An.to_sheet("string"==r.type?t:kr(t),r):s(t,r)}return{to_workbook:function(e,r){return Pt(i(e,r),r)},to_sheet:i,from_sheet:function(e){for(var r,t=[],a=Nt(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],c=a.s.c;c<=a.e.c;++c){var o=Ot({r:s,c:c});if((r=n?(e[s]||[])[c]:e[o])&&null!=r.v){for(var l=(r.w||(Ft(r),r.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===c?" ":""))}else i.push("          ")}t.push(i.join(""))}return t.join("\n")}}}();var kn=function(){function e(e,r,t){if(e){mt(e,e.l||0);for(var a=t.Enum||T;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),c=e.l+i,o=s.f&&s.f(e,i,t);if(e.l=c,r(o,s,n))return}}}function r(r,t){if(!r)return r;var a=t||{},n=a.dense?[]:{},s="Sheet1",i="",c=0,o={},l=[],f=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=a.sheetRows||0;if(0==r[2]&&(8==r[3]||9==r[3])&&r.length>=16&&5==r[14]&&108===r[15])throw new Error("Unsupported Works 3 for Mac file");if(2==r[2])a.Enum=T,e(r,(function(e,r,t){switch(t){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:h=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==t&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||H[14],a.cellDates&&(e[1].t="d",e[1].v=Fe(e[1].v))),a.qpro&&e[3]>c&&(n["!ref"]=It(h),o[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s=i||"Sheet"+(c+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[Ot(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Ot(e[0])]=e[1]}}),a);else{if(26!=r[2]&&14!=r[2])throw new Error("Unrecognized LOTUS BOF "+r[2]);a.Enum=E,14==r[2]&&(a.qpro=!0,r.l=0),e(r,(function(e,r,t){switch(t){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>c&&(n["!ref"]=It(h),o[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s="Sheet"+(c+1)),u>0&&e[0].r>=u)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Ot(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==c&&(s=e[1])}}),a)}if(n["!ref"]=It(h),o[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:o};for(var d={},p=[],m=0;m<f.length;++m)o[l[m]]?(p.push(f[m]||l[m]),d[f[m]]=o[f[m]]||o[l[m]]):(p.push(f[m]),d[f[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function t(e,r,t){var a=[{c:0,r:0},{t:"n",v:0},0,0];return t.qpro&&20768!=t.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,r,a){var n=e.l+r,s=t(e,0,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function s(e,r,t){var a=vt(7+t.length);a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=t.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function i(e,r,t){var a=vt(7);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(2,t,"i"),a}function c(e,r,t){var a=vt(13);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(8,t,"f"),a}function o(e,r,t){var a=32768&r;return r=(a?e:0)+((r&=-32769)>=8192?r-16384:r),(a?"":"$")+(t?Ct(r):yt(r))}var l={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},f=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function u(e){var r=[{c:0,r:0},{t:"n",v:0},0];return r[0].r=e.read_shift(2),r[3]=e[e.l++],r[0].c=e[e.l++],r}function d(e,r,t,a){var n=vt(6+a.length);n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function p(e,r){var t=u(e),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(t[1].t="e",t[1].v=15):0===a&&3489660928===n?(t[1].t="e",t[1].v=42):t[1].v=0,t;var i=32768&s;return s=(32767&s)-16446,t[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),t}function m(e,r,t,a){var n=vt(14);if(n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s,i=0,c=0,o=0;return a<0&&(i=1,a=-a),c=0|Math.log2(a),0==(2147483648&(o=(a/=Math.pow(2,c-31))>>>0))&&(++c,o=(a/=2)>>>0),a-=o,o|=2147483648,o>>>=0,s=(a*=Math.pow(2,32))>>>0,n.write_shift(4,s),n.write_shift(4,o),c+=16383+(i?32768:0),n.write_shift(2,c),n}function g(e,r){var t=u(e),a=e.read_shift(8,"f");return t[1].v=a,t}function v(e,r){return 0==e[e.l+r-1]?e.read_shift(r,"cstr"):""}function b(e,r){var t=vt(5+e.length);t.write_shift(2,14e3),t.write_shift(2,r);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);t[t.l++]=n>127?95:n}return t[t.l++]=0,t}var T={0:{n:"BOF",f:Pa},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,r,t){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==r&&t.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,r,a){var n=t(e,0,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,r,a){var n=t(e,0,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,r,a){var s=e.l+r,i=t(e,0,a);if(i[1].v=e.read_shift(8,"f"),a.qpro)e.l=s;else{var c=e.read_shift(2);!function(e,r){mt(e,0);var t=[],a=0,s="",i="",c="",h="";for(;e.l<e.length;){var u=e[e.l++];switch(u){case 0:t.push(e.read_shift(8,"f"));break;case 1:i=o(r[0].c,e.read_shift(2),!0),s=o(r[0].r,e.read_shift(2),!1),t.push(i+s);break;case 2:var d=o(r[0].c,e.read_shift(2),!0),p=o(r[0].r,e.read_shift(2),!1);i=o(r[0].c,e.read_shift(2),!0),s=o(r[0].r,e.read_shift(2),!1),t.push(d+p+":"+i+s);break;case 3:if(e.l<e.length)return void n("error","at node_modules/xlsx/xlsx.mjs:8967","WK1 premature formula end");break;case 4:t.push("("+t.pop()+")");break;case 5:t.push(e.read_shift(2));break;case 6:for(var m="";u=e[e.l++];)m+=String.fromCharCode(u);t.push('"'+m.replace(/"/g,'""')+'"');break;case 8:t.push("-"+t.pop());break;case 23:t.push("+"+t.pop());break;case 22:t.push("NOT("+t.pop()+")");break;case 20:case 21:h=t.pop(),c=t.pop(),t.push(["AND","OR"][u-20]+"("+c+","+h+")");break;default:if(u<32&&f[u])h=t.pop(),c=t.pop(),t.push(c+f[u]+h);else{if(!l[u])return u<=7?n("error","at node_modules/xlsx/xlsx.mjs:8998","WK1 invalid opcode "+u.toString(16)):u<=24?n("error","at node_modules/xlsx/xlsx.mjs:8999","WK1 unsupported op "+u.toString(16)):u<=30?n("error","at node_modules/xlsx/xlsx.mjs:9000","WK1 invalid opcode "+u.toString(16)):u<=115?n("error","at node_modules/xlsx/xlsx.mjs:9001","WK1 unsupported function opcode "+u.toString(16)):n("error","at node_modules/xlsx/xlsx.mjs:9003","WK1 unrecognized opcode "+u.toString(16));if(69==(a=l[u][1])&&(a=e[e.l++]),a>t.length)return void n("error","at node_modules/xlsx/xlsx.mjs:8993","WK1 bad formula parse 0x"+u.toString(16)+":|"+t.join("|")+"|");var g=t.slice(-a);t.length-=a,t.push(l[u][0]+"("+g.join(",")+")")}}}1==t.length?r[1].f=""+t[0]:n("error","at node_modules/xlsx/xlsx.mjs:9007","WK1 bad formula parse |"+t.join("|")+"|")}(e.slice(e.l,e.l+c),i),e.l+=c}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:v},222:{n:"SHEETNAMELP",f:function(e,r){var t=e[e.l++];t>r-1&&(t=r-1);for(var a="";a.length<t;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},E={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,r){var t=u(e);return t[1].t="s",t[1].v=e.read_shift(r-4,"cstr"),t}},23:{n:"NUMBER17",f:p},24:{n:"NUMBER18",f:function(e,r){var t=u(e);t[1].v=e.read_shift(2);var a=t[1].v>>1;if(1&t[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return t[1].v=a,t}},25:{n:"FORMULA19",f:function(e,r){var t=p(e);return e.l+=r-14,t}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,r){for(var t={},a=e.l+r;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(t[n]=[0,""],t[n][0]=e.read_shift(2);e[e.l];)t[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return t}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,r){var t=u(e),a=e.read_shift(4);return t[1].v=a>>6,t}},38:{n:"??"},39:{n:"NUMBER27",f:g},40:{n:"FORMULA28",f:function(e,r){var t=g(e);return e.l+=r-10,t}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:v},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,r,t){if(t.qpro&&!(r<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(r-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,r){var t=r||{};if(+t.codepage>=0&&h(+t.codepage),"string"==t.type)throw new Error("Cannot write WK1 to JS string");var a,n,o=Tt(),l=Nt(e["!ref"]),f=Array.isArray(e),u=[];_c(o,0,(a=1030,(n=vt(2)).write_shift(2,a),n)),_c(o,6,function(e){var r=vt(8);return r.write_shift(2,e.s.c),r.write_shift(2,e.s.r),r.write_shift(2,e.e.c),r.write_shift(2,e.e.r),r}(l));for(var d=Math.min(l.e.r,8191),p=l.s.r;p<=d;++p)for(var m=yt(p),g=l.s.c;g<=l.e.c;++g){p===l.s.r&&(u[g]=Ct(g));var v=u[g]+m,b=f?(e[p]||[])[g]:e[v];if(b&&"z"!=b.t)if("n"==b.t)(0|b.v)==b.v&&b.v>=-32768&&b.v<=32767?_c(o,13,i(p,g,b.v)):_c(o,14,c(p,g,b.v));else _c(o,15,s(p,g,Ft(b).slice(0,239)))}return _c(o,1),o.end()},book_to_wk3:function(e,r){var t=r||{};if(+t.codepage>=0&&h(+t.codepage),"string"==t.type)throw new Error("Cannot write WK3 to JS string");var a=Tt();_c(a,0,function(e){var r=vt(26);r.write_shift(2,4096),r.write_shift(2,4),r.write_shift(4,0);for(var t=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],c=e.Sheets[i];if(c&&c["!ref"]){++n;var o=Rt(c["!ref"]);t<o.e.r&&(t=o.e.r),a<o.e.c&&(a=o.e.c)}}t>8191&&(t=8191);return r.write_shift(2,t),r.write_shift(1,n),r.write_shift(1,a),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,1),r.write_shift(1,2),r.write_shift(4,0),r.write_shift(4,0),r}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&_c(a,27,b(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var o=Nt(c["!ref"]),l=Array.isArray(c),f=[],u=Math.min(o.e.r,8191),p=o.s.r;p<=u;++p)for(var g=yt(p),v=o.s.c;v<=o.e.c;++v){p===o.s.r&&(f[v]=Ct(v));var T=f[v]+g,E=l?(c[p]||[])[v]:c[T];if(E&&"z"!=E.t)if("n"==E.t)_c(a,23,m(p,v,i,E.v));else _c(a,22,d(p,v,i,Ft(E).slice(0,239)))}++i}}return _c(a,1),a.end()},to_workbook:function(e,t){switch(t.type){case"base64":return r(_(w(e)),t);case"binary":return r(_(e),t);case"buffer":case"array":return r(e,t)}throw"Unsupported type "+t.type}}}();var yn=function(){var e=_r("t"),r=_r("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:pr(a[1])},s=t.match(r);return s&&(n.s=function(e){var r={},t=e.match(cr),a=0,n=!1;if(t)for(;a!=t.length;++a){var s=fr(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;r.cp=l[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting"}case"<u>":case"<u/>":r.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":r.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":r.i=1;break;case"</i>":break;case"<color":s.rgb&&(r.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":r.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":r.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw new Error("Unrecognized rich format "+s[0])}}return r}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(t).filter((function(e){return e.v}))}}(),_n=function(){var e=/(\r\n|\n)/g;function r(r){var t=[[],r.v,[]];return r.v?(r.s&&function(e,r,t){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),r.push('<span style="'+a.join("")+'">'),e.b&&(r.push("<b>"),t.push("</b>")),e.i&&(r.push("<i>"),t.push("</i>")),e.strike&&(r.push("<s>"),t.push("</s>"));var n=e.valign||"";"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(r.push("<"+n+">"),t.push("</"+n+">")),t.push("</span>")}(r.s,t[0],t[2]),t[0].join("")+t[1].replace(e,"<br/>")+t[2].join("")):""}return function(e){return e.map(r).join("")}}(),Cn=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,xn=/<(?:\w+:)?r>/,On=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Rn(e,r){var t=!r||r.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=pr(kr(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=kr(e),t&&(a.h=vr(a.t))):e.match(xn)&&(a.r=kr(e),a.t=pr(kr((e.replace(On,"").match(Cn)||[]).join("").replace(cr,""))),t&&(a.h=_n(yn(a.r)))),a):{t:""}}var In=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Nn=/<(?:\w+:)?(?:si|sstItem)>/g,Dn=/<\/(?:\w+:)?(?:si|sstItem)>/;function Fn(e){for(var r=[],t=e.split(""),a=0;a<t.length;++a)r[a]=t[a].charCodeAt(0);return r}function Pn(e,r){var t={};return t.Major=e.read_shift(2),t.Minor=e.read_shift(2),r>=4&&(e.l+=r-4),t}function Mn(e){for(var r=e.read_shift(4),t=e.l+r-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=t)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+t);return a}function Ln(e){var r=function(e){var r={};return e.read_shift(4),e.l+=4,r.id=e.read_shift(0,"lpp4"),r.name=e.read_shift(0,"lpp4"),r.R=Pn(e,4),r.U=Pn(e,4),r.W=Pn(e,4),r}(e);if(r.ename=e.read_shift(0,"8lpp4"),r.blksz=e.read_shift(4),r.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return r}function Un(e,r){var t=e.l+r,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(t-e.l>>1,"utf16le"),e.l=t,a}function Bn(e,r){var t={},a=e.l+r;return e.l+=4,t.Salt=e.slice(e.l,e.l+16),e.l+=16,t.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),t.VerifierHash=e.slice(e.l,a),e.l=a,t}function Vn(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4);return{t:"Std",h:Un(e,r),v:Bn(e,e.length-e.l)}}function Hn(){throw new Error("File is password-protected: ECMA-376 Extensible")}function Wn(e){var r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var t=e.read_shift(e.length-e.l,"utf8"),a={};return t.replace(cr,(function(e){var t=fr(e);switch(hr(t[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":r.forEach((function(e){a[e]=t[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=t.encryptedHmacKey,a.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"<keyEncryptor":a.uri=t.uri;break;case"<encryptedKey":a.encs.push(t);break;default:throw t[0]}})),a}var zn=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],r=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],t=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,r){return 255&((t=e^r)/2|128*t);var t};return function(n){for(var s,i,c,o=Fn(n),l=function(e){for(var a=r[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],c=0;7!=c;++c)64&i&&(a^=t[n]),i*=2,--n;return a}(o),f=o.length,h=k(16),u=0;16!=u;++u)h[u]=0;for(1==(1&f)&&(s=l>>8,h[f]=a(e[0],s),--f,s=255&l,i=o[o.length-1],h[f]=a(i,s));f>0;)s=l>>8,h[--f]=a(o[f],s),s=255&l,h[--f]=a(o[f],s);for(f=15,c=15-o.length;c>0;)s=l>>8,h[f]=a(e[c],s),--c,s=255&l,h[--f]=a(o[f],s),--f,--c;return h}}(),Gn=function(e){var r=0,t=zn(e);return function(e){var a=function(e,r,t,a,n){var s,i;for(n||(n=r),a||(a=zn(e)),s=0;s!=r.length;++s)i=r[s],i=255&((i^=a[t])>>5|i<<3),n[s]=i,++t;return[n,t,a]}("",e,r,t);return r=a[1],a[0]}};function $n(e,r,t,a){var n={key:Pa(e),verificationBytes:Pa(e)};return t.password&&(n.verifier=function(e){var r,t,a=0,n=Fn(e),s=n.length+1;for((r=k(s))[0]=n.length,t=1;t!=s;++t)r[t]=n[t-1];for(t=s-1;t>=0;--t)a=((0==(16384&a)?0:1)|a<<1&32767)^r[t];return 52811^a}(t.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=Gn(t.password)),n}function jn(e,r,t){var a=t||{};return a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var r={},t=r.EncryptionVersionInfo=Pn(e,4);if(1!=t.Major||1!=t.Minor)throw"unrecognized version code "+t.Major+" : "+t.Minor;return r.Salt=e.read_shift(16),r.EncryptedVerifier=e.read_shift(16),r.EncryptedVerifierHash=e.read_shift(16),r}(e):a.Data=function(e,r){var t={},a=t.EncryptionVersionInfo=Pn(e,4);if(r-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);t.Flags=e.read_shift(4),r-=4;var n=e.read_shift(4);return r-=4,t.EncryptionHeader=Un(e,n),r-=n,t.EncryptionVerifier=Bn(e,r),t}(e,r),a}var Xn=function(){function e(e,t){switch(t.type){case"base64":return r(w(e),t);case"binary":return r(e,t);case"buffer":return r(A&&Buffer.isBuffer(e)?e.toString("binary"):C(e),t);case"array":return r(Ve(e),t)}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=(r||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,r){Array.isArray(t)&&(t[r]=[]);for(var a,s=/\\\w+\b/g,i=0,c=-1;a=s.exec(e);){if("\\cell"===a[0]){var o=e.slice(i,s.lastIndex-a[0].length);if(" "==o[0]&&(o=o.slice(1)),++c,o.length){var l={v:o,t:"s"};Array.isArray(t)?t[r][c]=l:t[Ot({r:r,c:c})]=l}}i=s.lastIndex}c>n.e.c&&(n.e.c=c)})),t["!ref"]=It(n),t}return{to_workbook:function(r,t){return Pt(e(r,t),t)},to_sheet:e,from_sheet:function(e){for(var r,t=["{\\rtf1\\ansi"],a=Nt(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){t.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)t.push("\\cellx"+(i+1));for(t.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var c=Ot({r:s,c:i});(r=n?(e[s]||[])[i]:e[c])&&(null!=r.v||r.f&&!r.F)&&(t.push(" "+(r.w||(Ft(r),r.w))),t.push("\\cell"))}t.push("\\pard\\intbl\\row")}return t.join("")+"}"}}}();function Yn(e){for(var r=0,t=1;3!=r;++r)t=256*t+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function Kn(e,r){if(0===r)return e;var t,a,n=function(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255,n=Math.max(r,t,a),s=Math.min(r,t,a),i=n-s;if(0===i)return[0,0,r];var c,o=0,l=n+s;switch(c=i/(l>1?2-l:l),n){case r:o=((t-a)/i+6)%6;break;case t:o=(a-r)/i+2;break;case a:o=(r-t)/i+4}return[o/6,c,l/2]}((a=(t=e).slice("#"===t[0]?1:0).slice(0,6),[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]));return n[2]=r<0?n[2]*(1+r):1-(1-n[2])*(1-r),Yn(function(e){var r,t=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,c=[i,i,i],o=6*t;if(0!==a)switch(0|o){case 0:case 6:r=s*o,c[0]+=s,c[1]+=r;break;case 1:r=s*(2-o),c[0]+=r,c[1]+=s;break;case 2:r=s*(o-2),c[1]+=s,c[2]+=r;break;case 3:r=s*(4-o),c[1]+=r,c[2]+=s;break;case 4:r=s*(o-4),c[2]+=s,c[0]+=r;break;case 5:r=s*(6-o),c[2]+=r,c[0]+=s}for(var l=0;3!=l;++l)c[l]=Math.round(255*c[l]);return c}(n))}var Jn=6;function qn(e){return Math.floor((e+Math.round(128/Jn)/256)*Jn)}function Zn(e){return Math.floor((e-5)/Jn*100+.5)/100}function Qn(e){return Math.round((e*Jn+5)/Jn*256)/256}function es(e){return Qn(Zn(qn(e)))}function rs(e){var r=Math.abs(e-es(e)),t=Jn;if(r>.005)for(Jn=1;Jn<15;++Jn)Math.abs(e-es(e))<=r&&(r=Math.abs(e-es(e)),t=Jn);Jn=t}function ts(e){e.width?(e.wpx=qn(e.width),e.wch=Zn(e.wpx),e.MDW=Jn):e.wpx?(e.wch=Zn(e.wpx),e.width=Qn(e.wch),e.MDW=Jn):"number"==typeof e.wch&&(e.width=Qn(e.wch),e.wpx=qn(e.width),e.MDW=Jn),e.customWidth&&delete e.customWidth}function as(e){return 96*e/96}function ns(e){return 96*e/96}var ss={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};var is=["numFmtId","fillId","fontId","borderId","xfId"],cs=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];var os=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,c){var o,f={};return s?((o=(s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,r,t){r.NumberFmt=[];for(var a=Ce(H),n=0;n<a.length;++n)r.NumberFmt[a[n]]=H[a[n]];var s=e[0].match(cr);if(s)for(n=0;n<s.length;++n){var i=fr(s[n]);switch(hr(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var c=pr(kr(i.formatCode)),o=parseInt(i.numFmtId,10);if(r.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&null!=r.NumberFmt[o];--o);r.NumberFmt[o]=c}we(c,o)}break;default:if(t.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(o,f,c),(o=s.match(a))&&function(e,r,t,a){r.Fonts=[];var n={},s=!1;(e[0].match(cr)||[]).forEach((function(e){var i=fr(e);switch(hr(i[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":r.Fonts.push(n),n={};break;case"<name":i.val&&(n.name=kr(i.val));break;case"<b":n.bold=i.val?Tr(i.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=i.val?Tr(i.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(i.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=i.val?Tr(i.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=i.val?Tr(i.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=i.val?Tr(i.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=i.val?Tr(i.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=i.val?Tr(i.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":i.val&&(n.sz=+i.val);break;case"<vertAlign":i.val&&(n.vertAlign=i.val);break;case"<family":i.val&&(n.family=parseInt(i.val,10));break;case"<scheme":i.val&&(n.scheme=i.val);break;case"<charset":if("1"==i.val)break;i.codepage=l[parseInt(i.val,10)];break;case"<color":if(n.color||(n.color={}),i.auto&&(n.color.auto=Tr(i.auto)),i.rgb)n.color.rgb=i.rgb.slice(-6);else if(i.indexed){n.color.index=parseInt(i.indexed,10);var c=ia[n.color.index];81==n.color.index&&(c=ia[1]),c||(c=ia[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else i.theme&&(n.color.theme=parseInt(i.theme,10),i.tint&&(n.color.tint=parseFloat(i.tint)),i.theme&&t.themeElements&&t.themeElements.clrScheme&&(n.color.rgb=Kn(t.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<AlternateContent":case"<ext":s=!0;break;case"</AlternateContent>":case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+i[0]+" in fonts")}}))}(o,f,i,c),(o=s.match(t))&&function(e,r,t,a){r.Fills=[];var n={},s=!1;(e[0].match(cr)||[]).forEach((function(e){var t=fr(e);switch(hr(t[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":n={},r.Fills.push(n);break;case"<gradientFill":case"</gradientFill>":r.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":t.patternType&&(n.patternType=t.patternType);break;case"<bgColor":n.bgColor||(n.bgColor={}),t.indexed&&(n.bgColor.indexed=parseInt(t.indexed,10)),t.theme&&(n.bgColor.theme=parseInt(t.theme,10)),t.tint&&(n.bgColor.tint=parseFloat(t.tint)),t.rgb&&(n.bgColor.rgb=t.rgb.slice(-6));break;case"<fgColor":n.fgColor||(n.fgColor={}),t.theme&&(n.fgColor.theme=parseInt(t.theme,10)),t.tint&&(n.fgColor.tint=parseFloat(t.tint)),null!=t.rgb&&(n.fgColor.rgb=t.rgb.slice(-6));break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+t[0]+" in fills")}}))}(o,f,0,c),(o=s.match(n))&&function(e,r,t,a){r.Borders=[];var n={},s=!1;(e[0].match(cr)||[]).forEach((function(e){var t=fr(e);switch(hr(t[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":n={},t.diagonalUp&&(n.diagonalUp=Tr(t.diagonalUp)),t.diagonalDown&&(n.diagonalDown=Tr(t.diagonalDown)),r.Borders.push(n);break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+t[0]+" in borders")}}))}(o,f,0,c),(o=s.match(r))&&function(e,r,t){var a;r.CellXf=[];var n=!1;(e[0].match(cr)||[]).forEach((function(e){var s=fr(e),i=0;switch(hr(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(delete(a=s)[0],i=0;i<is.length;++i)a[is[i]]&&(a[is[i]]=parseInt(a[is[i]],10));for(i=0;i<cs.length;++i)a[cs[i]]&&(a[cs[i]]=Tr(a[cs[i]]));if(r.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(r.NumberFmt[a.numFmtId]==r.NumberFmt[i]){a.numFmtId=i;break}r.CellXf.push(a);break;case"<alignment":case"<alignment/>":var c={};s.vertical&&(c.vertical=s.vertical),s.horizontal&&(c.horizontal=s.horizontal),null!=s.textRotation&&(c.textRotation=s.textRotation),s.indent&&(c.indent=s.indent),s.wrapText&&(c.wrapText=Tr(s.wrapText)),a.alignment=c;break;case"<AlternateContent":case"<ext":n=!0;break;case"</AlternateContent>":case"</ext>":n=!1;break;default:if(t&&t.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}(o,f,c),f):f}}();var ls=gt;var fs=gt;var hs=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function us(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(cr)||[]).forEach((function(e){var n=fr(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(r.themeElements.clrScheme[hs.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(t&&t.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}function ds(){}function ps(){}var ms=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,gs=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,vs=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;var bs=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Ts(e,r){var t;e&&0!==e.length||(e=function(e,r){if(r&&r.themeXLSX)return r.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var t=[nr];return t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',t[t.length]="<a:themeElements>",t[t.length]='<a:clrScheme name="Office">',t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',t[t.length]="</a:clrScheme>",t[t.length]='<a:fontScheme name="Office">',t[t.length]="<a:majorFont>",t[t.length]='<a:latin typeface="Cambria"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>',t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:majorFont>",t[t.length]="<a:minorFont>",t[t.length]='<a:latin typeface="Calibri"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Arial"/>',t[t.length]='<a:font script="Hebr" typeface="Arial"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Arial"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:minorFont>",t[t.length]="</a:fontScheme>",t[t.length]='<a:fmtScheme name="Office">',t[t.length]="<a:fillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="1"/>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="0"/>',t[t.length]="</a:gradFill>",t[t.length]="</a:fillStyleLst>",t[t.length]="<a:lnStyleLst>",t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]="</a:lnStyleLst>",t[t.length]="<a:effectStyleLst>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',t[t.length]="</a:effectStyle>",t[t.length]="</a:effectStyleLst>",t[t.length]="<a:bgFillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]="</a:bgFillStyleLst>",t[t.length]="</a:fmtScheme>",t[t.length]="</a:themeElements>",t[t.length]="<a:objectDefaults>",t[t.length]="<a:spDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',t[t.length]="</a:spDef>",t[t.length]="<a:lnDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',t[t.length]="</a:lnDef>",t[t.length]="</a:objectDefaults>",t[t.length]="<a:extraClrSchemeLst/>",t[t.length]="</a:theme>",t.join("")}());var a={};if(!(t=e.match(bs)))throw new Error("themeElements not found in theme");return function(e,r,t){var a;r.themeElements={},[["clrScheme",ms,us],["fontScheme",gs,ds],["fmtScheme",vs,ps]].forEach((function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,r,t)}))}(t[0],a,r),a.raw=e,a}function Es(e){var r={};switch(r.xclrType=e.read_shift(2),r.nTintShade=e.read_shift(2),r.xclrType){case 0:case 4:e.l+=4;break;case 1:r.xclrValue=function(e,r){return gt(e,r)}(e,4);break;case 2:r.xclrValue=Ga(e);break;case 3:r.xclrValue=function(e){return e.read_shift(4)}(e)}return e.l+=8,r}function ws(e){var r=e.read_shift(2),t=e.read_shift(2)-4,a=[r];switch(r){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=Es(e);break;case 6:a[1]=function(e,r){return gt(e,r)}(e,t);break;case 14:case 15:a[1]=e.read_shift(1===t?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+r+" "+t)}return a}function As(e,r,t,a){var n,s=Array.isArray(e);r.forEach((function(r){var i=xt(r.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[r.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[r.ref]=n;var c=Nt(e["!ref"]||"BDWGO1000001:A1");c.s.r>i.r&&(c.s.r=i.r),c.e.r<i.r&&(c.e.r=i.r),c.s.c>i.c&&(c.s.c=i.c),c.e.c<i.c&&(c.e.c=i.c);var o=It(c);o!==e["!ref"]&&(e["!ref"]=o)}n.c||(n.c=[]);var l={a:r.author,t:r.t,r:r.r,T:t};r.h&&(l.h=r.h);for(var f=n.c.length-1;f>=0;--f){if(!t&&n.c[f].T)return;t&&!n.c[f].T&&n.c.splice(f,1)}if(t&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}))}var Ss=Ut;var ks=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,r={r:0,c:0};function t(e,t,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var c=a.length>0?0|parseInt(a,10):0,o=n.length>0?0|parseInt(n,10):0;return s?o+=r.c:--o,i?c+=r.r:--c,t+(s?"":"$")+Ct(o)+(i?"":"$")+yt(c)}return function(a,n){return r=n,a.replace(e,t)}}(),ys=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,_s=function(){return function(e,r){return e.replace(ys,(function(e,t,a,n,s,i){var c=_t(n)-(a?0:r.c),o=kt(i)-(s?0:r.r);return t+"R"+(0==o?"":s?o+1:"["+o+"]")+"C"+(0==c?"":a?c+1:"["+c+"]")}))}}();function Cs(e,r){return e.replace(ys,(function(e,t,a,n,s,i){return t+("$"==a?a+n:Ct(_t(n)+r.c))+("$"==s?s+i:yt(kt(i)+r.r))}))}function xs(e,r,t){var a=Rt(r).s,n=xt(t);return Cs(e,{r:n.r-a.r,c:n.c-a.c})}function Os(e){return e.replace(/_xlfn\./g,"")}function Rs(e){e.l+=1}function Is(e,r){var t=e.read_shift(1==r?1:2);return[16383&t,t>>14&1,t>>15&1]}function Ns(e,r,t){var a=2;if(t){if(t.biff>=2&&t.biff<=5)return Ds(e);12==t.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=Is(e,2),c=Is(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Ds(e){var r=Is(e,2),t=Is(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:r[0],c:a,cRel:r[1],rRel:r[2]},e:{r:t[0],c:n,cRel:t[1],rRel:t[2]}}}function Fs(e,r,t){if(t&&t.biff>=2&&t.biff<=5)return function(e){var r=Is(e,2),t=e.read_shift(1);return{r:r[0],c:t,cRel:r[1],rRel:r[2]}}(e);var a=e.read_shift(t&&12==t.biff?4:2),n=Is(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Ps(e){var r=e.read_shift(2),t=e.read_shift(2);return{r:r,c:255&t,fQuoted:!!(16384&t),cRel:t>>15,rRel:t>>15}}function Ms(e){var r=1&e[e.l+1];return e.l+=4,[r,1]}function Ls(e){return[e.read_shift(1),e.read_shift(1)]}function Us(e,r){var t=[e.read_shift(1)];if(12==r)switch(t[0]){case 2:t[0]=4;break;case 4:t[0]=16;break;case 0:t[0]=1;break;case 1:t[0]=2}switch(t[0]){case 4:t[1]=Fa(e,1)?"TRUE":"FALSE",12!=r&&(e.l+=7);break;case 37:case 16:t[1]=ca[e[e.l]],e.l+=12==r?4:8;break;case 0:e.l+=8;break;case 1:t[1]=qt(e);break;case 2:t[1]=Ha(e,0,{biff:r>0&&r<8?2:r});break;default:throw new Error("Bad SerAr: "+t[0])}return t}function Bs(e,r,t){for(var a=e.read_shift(12==t.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==t.biff?Jt:Ka)(e));return n}function Vs(e,r,t){var a=0,n=0;12==t.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),t.biff>=2&&t.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var c=0;c!=n;++c)i[s][c]=Us(e,t.biff);return i}function Hs(e,r,t){return e.l+=2,[Ps(e)]}function Ws(e){return e.l+=6,[]}function zs(e){return e.l+=2,[Pa(e),1&e.read_shift(2)]}var Gs=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var $s={1:{n:"PtgExp",f:function(e,r,t){return e.l++,t&&12==t.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(t&&2==t.biff?1:2)]}},2:{n:"PtgTbl",f:gt},3:{n:"PtgAdd",f:Rs},4:{n:"PtgSub",f:Rs},5:{n:"PtgMul",f:Rs},6:{n:"PtgDiv",f:Rs},7:{n:"PtgPower",f:Rs},8:{n:"PtgConcat",f:Rs},9:{n:"PtgLt",f:Rs},10:{n:"PtgLe",f:Rs},11:{n:"PtgEq",f:Rs},12:{n:"PtgGe",f:Rs},13:{n:"PtgGt",f:Rs},14:{n:"PtgNe",f:Rs},15:{n:"PtgIsect",f:Rs},16:{n:"PtgUnion",f:Rs},17:{n:"PtgRange",f:Rs},18:{n:"PtgUplus",f:Rs},19:{n:"PtgUminus",f:Rs},20:{n:"PtgPercent",f:Rs},21:{n:"PtgParen",f:Rs},22:{n:"PtgMissArg",f:Rs},23:{n:"PtgStr",f:function(e,r,t){return e.l++,La(e,0,t)}},26:{n:"PtgSheet",f:function(e,r,t){return e.l+=5,e.l+=2,e.l+=2==t.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,r,t){return e.l+=2==t.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,ca[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,qt(e)}},32:{n:"PtgArray",f:function(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=2==t.biff?6:12==t.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(t&&t.biff<=3?1:2);return[ui[n],hi[n],a]}},34:{n:"PtgFuncVar",f:function(e,r,t){var a=e[e.l++],n=e.read_shift(1),s=t&&t.biff<=3?[88==a?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[n,(0===s[0]?hi:fi)[s[1]]]}},35:{n:"PtgName",f:function(e,r,t){var a=e.read_shift(1)>>>5&3,n=!t||t.biff>=8?4:2,s=e.read_shift(n);switch(t.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,r,t){var a=(96&e[e.l])>>5;return e.l+=1,[a,Fs(e,0,t)]}},37:{n:"PtgArea",f:function(e,r,t){return[(96&e[e.l++])>>5,Ns(e,t.biff>=2&&t.biff,t)]}},38:{n:"PtgMemArea",f:function(e,r,t){var a=e.read_shift(1)>>>5&3;return e.l+=t&&2==t.biff?3:4,[a,e.read_shift(t&&2==t.biff?1:2)]}},39:{n:"PtgMemErr",f:gt},40:{n:"PtgMemNoMem",f:gt},41:{n:"PtgMemFunc",f:function(e,r,t){return[e.read_shift(1)>>>5&3,e.read_shift(t&&2==t.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,r,t){var a=e.read_shift(1)>>>5&3;return e.l+=4,t.biff<8&&e.l--,12==t.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=t&&t.biff>8?12:t.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=function(e,r,t){var a=t&&t.biff?t.biff:8;if(a>=2&&a<=5)return function(e){var r=e.read_shift(2),t=e.read_shift(1),a=(32768&r)>>15,n=(16384&r)>>14;return r&=16383,1==a&&r>=8192&&(r-=16384),1==n&&t>=128&&(t-=256),{r:r,c:t,cRel:n,rRel:a}}(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,c=(32768&s)>>15;if(s&=16383,1==c)for(;n>524287;)n-=1048576;if(1==i)for(;s>8191;)s-=16384;return{r:n,c:s,cRel:i,rRel:c}}(e,0,t);return[a,n]}},45:{n:"PtgAreaN",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=function(e,r,t){if(t.biff<8)return Ds(e);var a=e.read_shift(12==t.biff?4:2),n=e.read_shift(12==t.biff?4:2),s=Is(e,2),i=Is(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,0,t);return[a,n]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,r,t){return 5==t.biff?function(e){var r=e.read_shift(1)>>>5&3,t=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[r,t,a]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return t&&5==t.biff&&(e.l+=12),[a,n,Fs(e,0,t)]}},59:{n:"PtgArea3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i");if(t)switch(t.biff){case 5:e.l+=12,6;break;case 12:12}return[a,n,Ns(e,0,t)]}},60:{n:"PtgRefErr3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(t)switch(t.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(t)switch(t.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},js={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Xs={1:{n:"PtgElfLel",f:zs},2:{n:"PtgElfRw",f:Hs},3:{n:"PtgElfCol",f:Hs},6:{n:"PtgElfRwV",f:Hs},7:{n:"PtgElfColV",f:Hs},10:{n:"PtgElfRadical",f:Hs},11:{n:"PtgElfRadicalS",f:Ws},13:{n:"PtgElfColS",f:Ws},15:{n:"PtgElfColSV",f:Ws},16:{n:"PtgElfRadicalLel",f:zs},25:{n:"PtgList",f:function(e){e.l+=2;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2);return{ixti:r,coltype:3&t,rt:Gs[t>>2&31],idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Ys={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=t&&2==t.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,r,t){e.l+=2;for(var a=e.read_shift(t&&2==t.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(t&&2==t.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,r,t){e.l+=t&&2==t.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Ms},33:{n:"PtgAttrBaxcel",f:Ms},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),Ls(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),Ls(e)}},128:{n:"PtgAttrIfError",f:function(e){var r=255&e[e.l+1]?1:0;return e.l+=2,[r,e.read_shift(2)]}},255:{}};function Ks(e,r,t,a){if(a.biff<8)return gt(e,r);for(var n=e.l+r,s=[],i=0;i!==t.length;++i)switch(t[i][0]){case"PtgArray":t[i][1]=Vs(e,0,a),s.push(t[i][1]);break;case"PtgMemArea":t[i][2]=Bs(e,t[i][1],a),s.push(t[i][2]);break;case"PtgExp":a&&12==a.biff&&(t[i][1][1]=e.read_shift(4),s.push(t[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+t[i][0]}return 0!==(r=n-e.l)&&s.push(gt(e,r)),s}function Js(e,r,t){for(var a,n,s=e.l+r,i=[];s!=e.l;)r=s-e.l,n=e[e.l],a=$s[n]||$s[js[n]],24!==n&&25!==n||(a=(24===n?Xs:Ys)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,r,t)]):gt(e,r);return i}function qs(e){for(var r=[],t=0;t<e.length;++t){for(var a=e[t],n=[],s=0;s<a.length;++s){var i=a[s];if(i)if(2===i[0])n.push('"'+i[1].replace(/"/g,'""')+'"');else n.push(i[1]);else n.push("")}r.push(n.join(","))}return r.join(";")}var Zs={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Qs(e,r,t){if(!e)return"SH33TJSERR0";if(t.biff>8&&(!e.XTI||!e.XTI[r]))return e.SheetNames[r];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[r];if(t.biff<8)return r>1e4&&(r-=65536),r<0&&(r=-r),0==r?"":e.XTI[r-1];if(!a)return"SH33TJSERR1";var n="";if(t.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=t.SID?e.SheetNames[t.SID]:"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function ei(e,r,t){var a=Qs(e,r,t);return"#REF"==a?a:function(e,r){if(!(e||r&&r.biff<=5&&r.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,t)}function ri(e,r,t,a,n){var s,i,c,o,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var T=e[0][v];switch(T[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=We(" ",e[0][m][1][1]);break;case 1:g=We("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+Zs[T[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":c=Et(T[1][1],f,n),h.push(At(c,l));break;case"PtgRefN":c=t?Et(T[1][1],t,n):T[1][1],h.push(At(c,l));break;case"PtgRef3d":u=T[1][1],c=Et(T[1][2],f,n),p=ei(a,u,n),h.push(p+"!"+At(c,l));break;case"PtgFunc":case"PtgFuncVar":var E=T[1][0],w=T[1][1];E||(E=0);var A=0==(E&=127)?[]:h.slice(-E);h.length-=E,"User"===w&&(w=A.shift()),h.push(w+"("+A.join(",")+")");break;case"PtgBool":h.push(T[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(T[1]);break;case"PtgNum":h.push(String(T[1]));break;case"PtgStr":h.push('"'+T[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":o=wt(T[1][1],t?{s:t}:f,n),h.push(St(o,n));break;case"PtgArea":o=wt(T[1][1],f,n),h.push(St(o,n));break;case"PtgArea3d":u=T[1][1],o=T[1][2],p=ei(a,u,n),h.push(p+"!"+St(o,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=T[1][2];var S=(a.names||[])[d-1]||(a[0]||[])[d],k=S?S.Name:"SH33TJSNAME"+String(d);k&&"_xlfn."==k.slice(0,6)&&!n.xlfn&&(k=k.slice(6)),h.push(k);break;case"PtgNameX":var y,_=T[1][1];if(d=T[1][2],!(n.biff<=5)){var C="";if(14849==((a[_]||[])[0]||[])[0]||(1025==((a[_]||[])[0]||[])[0]?a[_][d]&&a[_][d].itab>0&&(C=a.SheetNames[a[_][d].itab-1]+"!"):C=a.SheetNames[d-1]+"!"),a[_]&&a[_][d])C+=a[_][d].Name;else if(a[0]&&a[0][d])C+=a[0][d].Name;else{var x=(Qs(a,_,n)||"").split(";;");x[d-1]?C=x[d-1]:C+="SH33TJSERRX"}h.push(C);break}_<0&&(_=-_),a[_]&&(y=a[_][d]),y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:O=We(" ",e[0][m][1][1])+O;break;case 3:O=We("\r",e[0][m][1][1])+O;break;case 4:R=We(" ",e[0][m][1][1])+R;break;case 5:R=We("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":c={c:T[1][1],r:T[1][0]};var I={c:t.c,r:t.r};if(a.sharedf[Ot(c)]){var N=a.sharedf[Ot(c)];h.push(ri(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(c.c<i[0].s.c||c.c>i[0].e.c||c.r<i[0].s.r||c.r>i[0].e.r)){h.push(ri(i[1],f,I,a,n)),D=!0;break}D||h.push(T[1])}break;case"PtgArray":h.push("{"+qs(T[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+T[1].idx+"[#"+T[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(T))}if(3!=n.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var F=!0;switch((T=e[0][m])[1][0]){case 4:F=!1;case 0:g=We(" ",T[1][1]);break;case 5:F=!1;case 1:g=We("\r",T[1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+T[1][0])}h.push((F?g:"")+h.pop()+(F?"":g)),m=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return h[0]}function ti(e,r,t){var a,n=e.l+r,s=2==t.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],gt(e,r-2)];var c=Js(e,i,t);return r!==i+s&&(a=Ks(e,r-i-s,c,t)),e.l=n,[c,a]}function ai(e,r,t){var a,n=e.l+r,s=e.read_shift(2),i=Js(e,s,t);return 65535==s?[[],gt(e,r-2)]:(r!==s+2&&(a=Ks(e,n-s-2,i,t)),[i,a])}function ni(e,r,t){var a=e.l+r,n=ja(e);2==t.biff&&++e.l;var s=function(e){var r;if(65535!==it(e,e.l+6))return[qt(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return r=1===e[e.l+2],e.l+=8,[r,"b"];case 2:return r=e[e.l+2],e.l+=8,[r,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),i=e.read_shift(1);2!=t.biff&&(e.read_shift(1),t.biff>=5&&e.read_shift(4));var c=function(e,r,t){var a,n=e.l+r,s=2==t.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],gt(e,r-2)];var c=Js(e,i,t);return r!==i+s&&(a=Ks(e,r-i-s,c,t)),e.l=n,[c,a]}(e,a-e.l,t);return{cell:n,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function si(e,r,t){var a=e.read_shift(4),n=Js(e,a,t),s=e.read_shift(4);return[n,s>0?Ks(e,s,n,t):null]}var ii=si,ci=si,oi=si,li=si,fi={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},hi={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},ui={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function di(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,r){return r.replace(/\./g,"")}))).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function pi(e){var r=e.split(":");return[r[0].split(".")[0],r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}var mi={},gi={};function vi(e,r){if(e){var t=[.7,.7,.75,.75,.3,.3];"xlml"==r&&(t=[1,1,1,1,.5,.5]),null==e.left&&(e.left=t[0]),null==e.right&&(e.right=t[1]),null==e.top&&(e.top=t[2]),null==e.bottom&&(e.bottom=t[3]),null==e.header&&(e.header=t[4]),null==e.footer&&(e.footer=t[5])}}function bi(e,r,t,a,n,s){try{a.cellNF&&(e.z=H[r])}catch(c){if(a.WTF)throw c}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=Be(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==H[r]&&we(Se[r]||"General",r),"e"===e.t)e.w=e.w||ca[e.v];else if(0===r)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v);else if("d"===e.t){var i=Re(e.v);e.w=(0|i)===i?i.toString(10):Z(i)}else{if(void 0===e.v)return"";e.w=Q(e.v,gi)}else"d"===e.t?e.w=Ee(r,Re(e.v),gi):e.w=Ee(r,e.v,gi)}catch(c){if(a.WTF)throw c}if(a.cellStyles&&null!=t)try{e.s=s.Fills[t],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Kn(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Kn(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(a.WTF&&s.Fills)throw c}}}var Ti=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Ei=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,wi=/<(?:\w:)?hyperlink [^>]*>/gm,Ai=/"(\w*:\w*)"/,Si=/<(?:\w:)?col\b[^>]*[\/]?>/g,ki=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,yi=/<(?:\w:)?pageMargins[^>]*\/>/g,_i=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Ci=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,xi=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Oi(e,r,t,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var c=r.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(Ei);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(_i);u?Ri(u[0],c,n,t):(u=l.match(Ci))&&function(e,r,t,a,n){Ri(e.slice(0,e.indexOf(">")),t,a,n)}(u[0],u[1],c,n,t);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(Ai);p&&function(e,r){var t=Nt(r);t.s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0&&(e["!ref"]=It(t))}(c,p[1])}var m=l.match(xi);m&&m[1]&&function(e,r){r.Views||(r.Views=[{}]);(e.match(Ii)||[]).forEach((function(e,t){var a=fr(e);r.Views[t]||(r.Views[t]={}),+a.zoomScale&&(r.Views[t].zoom=+a.zoomScale),Tr(a.rightToLeft)&&(r.Views[t].RTL=!0)}))}(m[1],n);var g=[];if(r.cellStyles){var v=l.match(Si);v&&function(e,r){for(var t=!1,a=0;a!=r.length;++a){var n=fr(r[a],!0);n.hidden&&(n.hidden=Tr(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!t&&n.width&&(t=!0,rs(n.width)),ts(n);s<=i;)e[s++]=He(n)}}(g,v)}h&&Ni(h[1],c,r,o,s,i);var b=f.match(ki);b&&(c["!autofilter"]=function(e){return{ref:(e.match(/ref="([^"]*)"/)||[])[1]}}(b[0]));var T=[],E=f.match(Ti);if(E)for(d=0;d!=E.length;++d)T[d]=Nt(E[d].slice(E[d].indexOf('"')+1));var w=f.match(wi);w&&function(e,r,t){for(var a=Array.isArray(e),n=0;n!=r.length;++n){var s=fr(kr(r[n]),!0);if(!s.ref)return;var i=((t||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+pr(s.location))):(s.Target="#"+pr(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=Nt(s.ref),o=c.s.r;o<=c.e.r;++o)for(var l=c.s.c;l<=c.e.c;++l){var f=Ot({c:l,r:o});a?(e[o]||(e[o]=[]),e[o][l]||(e[o][l]={t:"z",v:void 0}),e[o][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(c,w,a);var A,S,k=f.match(yi);if(k&&(c["!margins"]=(A=fr(k[0]),S={},["left","right","top","bottom","header","footer"].forEach((function(e){A[e]&&(S[e]=parseFloat(A[e]))})),S)),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=It(o)),r.sheetRows>0&&c["!ref"]){var y=Nt(c["!ref"]);r.sheetRows<=+y.e.r&&(y.e.r=r.sheetRows-1,y.e.r>o.e.r&&(y.e.r=o.e.r),y.e.r<y.s.r&&(y.s.r=y.e.r),y.e.c>o.e.c&&(y.e.c=o.e.c),y.e.c<y.s.c&&(y.s.c=y.e.c),c["!fullref"]=c["!ref"],c["!ref"]=It(y))}return g.length>0&&(c["!cols"]=g),T.length>0&&(c["!merges"]=T),c}function Ri(e,r,t,a){var n=fr(e);t.Sheets[a]||(t.Sheets[a]={}),n.codeName&&(t.Sheets[a].CodeName=pr(kr(n.codeName)))}var Ii=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;var Ni=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/,t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=_r("v"),i=_r("f");return function(c,o,l,f,h,u){for(var d,p,m,g,v,b=0,T="",E=[],w=[],A=0,S=0,k=0,y="",_=0,C=0,x=0,O=0,R=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(o),F=[],P={},M=!1,L=!!l.sheetStubs,U=c.split(r),B=0,V=U.length;B!=V;++B){var W=(T=U[B].trim()).length;if(0!==W){var z=0;e:for(b=0;b<W;++b)switch(T[b]){case">":if("/"!=T[b-1]){++b;break e}if(l&&l.cellStyles){if(_=null!=(p=fr(T.slice(z,b),!0)).r?parseInt(p.r,10):_+1,C=-1,l.sheetRows&&l.sheetRows<_)continue;P={},M=!1,p.ht&&(M=!0,P.hpt=parseFloat(p.ht),P.hpx=ns(P.hpt)),"1"==p.hidden&&(M=!0,P.hidden=!0),null!=p.outlineLevel&&(M=!0,P.level=+p.outlineLevel),M&&(F[_-1]=P)}break;case"<":z=b}if(z>=b)break;if(_=null!=(p=fr(T.slice(z,b),!0)).r?parseInt(p.r,10):_+1,C=-1,!(l.sheetRows&&l.sheetRows<_)){f.s.r>_-1&&(f.s.r=_-1),f.e.r<_-1&&(f.e.r=_-1),l&&l.cellStyles&&(P={},M=!1,p.ht&&(M=!0,P.hpt=parseFloat(p.ht),P.hpx=ns(P.hpt)),"1"==p.hidden&&(M=!0,P.hidden=!0),null!=p.outlineLevel&&(M=!0,P.level=+p.outlineLevel),M&&(F[_-1]=P)),E=T.slice(b).split(e);for(var G=0;G!=E.length&&"<"==E[G].trim().charAt(0);++G);for(E=E.slice(G),b=0;b!=E.length;++b)if(0!==(T=E[b].trim()).length){if(w=T.match(t),A=b,S=0,k=0,T="<c "+("<"==T.slice(0,1)?">":"")+T,null!=w&&2===w.length){for(A=0,y=w[1],S=0;S!=y.length&&!((k=y.charCodeAt(S)-64)<1||k>26);++S)A=26*A+k;C=--A}else++C;for(S=0;S!=T.length&&62!==T.charCodeAt(S);++S);if(++S,(p=fr(T.slice(0,S),!0)).r||(p.r=Ot({r:_-1,c:C})),d={t:""},null!=(w=(y=T.slice(S)).match(s))&&""!==w[1]&&(d.v=pr(w[1])),l.cellFormula){if(null!=(w=y.match(i))&&""!==w[1]){if(d.f=pr(kr(w[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=Os(d.f)),w[0].indexOf('t="array"')>-1)d.F=(y.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([Nt(d.F),d.F]);else if(w[0].indexOf('t="shared"')>-1){g=fr(w[0]);var $=pr(kr(w[1]));l.xlfn||($=Os($)),N[parseInt(g.si,10)]=[g,$,p.r]}}else(w=y.match(/<f[^>]*\/>/))&&N[(g=fr(w[0])).si]&&(d.f=xs(N[g.si][1],N[g.si][2],p.r));var j=xt(p.r);for(S=0;S<I.length;++S)j.r>=I[S][0].s.r&&j.r<=I[S][0].e.r&&j.c>=I[S][0].s.c&&j.c<=I[S][0].e.c&&(d.F=I[S][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!L)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>C&&(f.s.c=C),f.e.c<C&&(f.e.c=C),d.t){case"n":if(""==d.v||null==d.v){if(!L)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!L)continue;d.t="z"}else m=mi[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?kr(d.v):"",l.cellHTML&&(d.h=vr(d.v));break;case"inlineStr":w=y.match(a),d.t="s",null!=w&&(m=Rn(w[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=Tr(d.v);break;case"d":l.cellDates?d.v=Be(d.v,1):(d.v=Re(Be(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=oa[d.v]}if(x=O=0,v=null,R&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(x=v.numFmtId),l.cellStyles&&null!=v.fillId&&(O=v.fillId)),bi(d,x,O,l,h,u),l.cellDates&&R&&"n"==d.t&&ge(H[x])&&(d.t="d",d.v=Fe(d.v)),p.cm&&l.xlmeta){var X=(l.xlmeta.Cell||[])[+p.cm-1];X&&"XLDAPR"==X.type&&(d.D=!0)}if(D){var Y=xt(p.r);o[Y.r]||(o[Y.r]=[]),o[Y.r][Y.c]=d}else o[p.r]=d}}}}F.length>0&&(o["!rows"]=F)}}();var Di=Jt;function Fi(e){return[zt(e),qt(e),"n"]}var Pi=Jt;var Mi=["left","right","top","bottom","header","footer"];function Li(e,r,t,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach((function(e){var r=function(e){var r,t=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach((function(e){var r=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);r&&(t[+r[1]]=a?+r[2]:r[2])}));var n=pr((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach((function(e){r=e.replace(/<.*?>/g,"")})),[t,n,r]}(e);f.s.r=f.s.c=0,f.e.c=c,l=Ct(c),r[0].forEach((function(e,t){i[l+yt(t)]={t:"n",v:e,z:r[1]},o=t})),f.e.r<o&&(f.e.r=o),++c})),c>0&&(i["!ref"]=It(f)),i}var Ui=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Bi=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Vi=[],Hi=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Wi(e,r){for(var t=0;t!=e.length;++t)for(var a=e[t],n=0;n!=r.length;++n){var s=r[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=Tr(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function zi(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=Tr(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function Gi(e){zi(e.WBProps,Ui),zi(e.CalcPr,Hi),Wi(e.WBView,Bi),Wi(e.Sheets,Vi),gi.date1904=Tr(e.WBProps.date1904)}var $i="][*?/\\".split("");var ji=/<\w+:workbook/;function Xi(e,r){var t={};return e.read_shift(4),t.ArchID=e.read_shift(4),e.l+=r-8,t}function Yi(e,r,t){return".bin"===r.slice(-4)?function(e,r){var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;r||(r={}),r.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],kc[16]={n:"BrtFRTArchID$",f:Xi},bt(e,(function(e,c,o){switch(o){case 156:i.SheetNames.push(e.name),t.Sheets.push(e);break;case 153:t.WBProps=e;break;case 39:null!=e.Sheet&&(r.SID=e.Sheet),e.Ref=ri(e.Ptg,0,null,i,r),delete r.SID,delete e.Ptg,s.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([o,e]):i[0]=[o,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 35:case 37:a.push(o),n=!0;break;case 36:case 38:a.pop(),n=!1;break;default:if(c.T);else if(!n||r.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}}),r),Gi(t),t.Names=s,t.supbooks=i,t}(e,t):function(e,r){if(!e)throw new Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(cr,(function(c,o){var l=fr(c);switch(hr(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":c.match(ji)&&(n="xmlns"+c.match(/<(\w+):/)[1]),t.xmlns=l[n];break;case"<fileVersion":delete l[0],t.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":Ui.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":t.WBProps[e[0]]=Tr(l[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:t.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(t.WBProps.CodeName=kr(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],t.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=pr(kr(l.name)),delete l[0],t.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=kr(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),Tr(l.hidden||"0")&&(s.Hidden=!0),i=o+c.length;break;case"</definedName>":s.Ref=pr(kr(e.slice(i,o))),t.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],t.CalcPr=l;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return c})),-1===Lr.indexOf(t.xmlns))throw new Error("Unknown Namespace: "+t.xmlns);return Gi(t),t}(e,t)}function Ki(e,r,t,a,n,s,i,c){return".bin"===r.slice(-4)?function(e,r,t,a,n,s,i){if(!e)return e;var c=r||{};a||(a={"!id":{}});var o,l,f,h,u,d,p,m,g,v,b=c.dense?[]:{},T={s:{r:2e6,c:2e6},e:{r:0,c:0}},E=!1,w=!1,A=[];c.biff=12,c["!row"]=0;var S=0,k=!1,y=[],_={},C=c.supbooks||n.supbooks||[[]];if(C.sharedf=_,C.arrayf=y,C.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!c.supbooks&&(c.supbooks=C,n.Names))for(var x=0;x<n.Names.length;++x)C[0][x+1]=n.Names[x];var O,R=[],I=[],N=!1;if(kc[16]={n:"BrtShortReal",f:Fi},bt(e,(function(e,r,x){if(!w)switch(x){case 148:o=e;break;case 0:l=e,c.sheetRows&&c.sheetRows<=l.r&&(w=!0),g=yt(u=l.r),c["!row"]=l.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=ns(e.hpt)),I[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(f={t:e[2]},e[2]){case"n":f.v=e[1];break;case"s":m=mi[e[1]],f.v=m.t,f.r=m.r;break;case"b":f.v=!!e[1];break;case"e":f.v=e[1],!1!==c.cellText&&(f.w=ca[f.v]);break;case"str":f.t="s",f.v=e[1];break;case"is":f.t="s",f.v=e[1].t}if((h=i.CellXf[e[0].iStyleRef])&&bi(f,h.numFmtId,null,c,s,i),d=-1==e[0].c?d+1:e[0].c,c.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[Ct(d)+g]=f,c.cellFormula){for(k=!1,S=0;S<y.length;++S){var D=y[S];l.r>=D[0].s.r&&l.r<=D[0].e.r&&d>=D[0].s.c&&d<=D[0].e.c&&(f.F=It(D[0]),k=!0)}!k&&e.length>3&&(f.f=e[3])}if(T.s.r>l.r&&(T.s.r=l.r),T.s.c>d&&(T.s.c=d),T.e.r<l.r&&(T.e.r=l.r),T.e.c<d&&(T.e.c=d),c.cellDates&&h&&"n"==f.t&&ge(H[h.numFmtId])){var F=j(f.v);F&&(f.t="d",f.v=new Date(F.y,F.m-1,F.d,F.H,F.M,F.S,F.u))}O&&("XLDAPR"==O.type&&(f.D=!0),O=void 0);break;case 1:case 12:if(!c.sheetStubs||E)break;f={t:"z",v:void 0},d=-1==e[0].c?d+1:e[0].c,c.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[Ct(d)+g]=f,T.s.r>l.r&&(T.s.r=l.r),T.s.c>d&&(T.s.c=d),T.e.r<l.r&&(T.e.r=l.r),T.e.c<d&&(T.e.c=d),O&&("XLDAPR"==O.type&&(f.D=!0),O=void 0);break;case 176:A.push(e);break;case 49:O=((c.xlmeta||{}).Cell||[])[e-1];break;case 494:var P=a["!id"][e.relId];for(P?(e.Target=P.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=P):""==e.relId&&(e.Target="#"+e.loc),u=e.rfx.s.r;u<=e.rfx.e.r;++u)for(d=e.rfx.s.c;d<=e.rfx.e.c;++d)c.dense?(b[u]||(b[u]=[]),b[u][d]||(b[u][d]={t:"z",v:void 0}),b[u][d].l=e):(p=Ot({c:d,r:u}),b[p]||(b[p]={t:"z",v:void 0}),b[p].l=e);break;case 426:if(!c.cellFormula)break;y.push(e),(v=c.dense?b[u][d]:b[Ct(d)+g]).f=ri(e[1],0,{r:l.r,c:d},C,c),v.F=It(e[0]);break;case 427:if(!c.cellFormula)break;_[Ot(e[0].s)]=e[1],(v=c.dense?b[u][d]:b[Ct(d)+g]).f=ri(e[1],0,{r:l.r,c:d},C,c);break;case 60:if(!c.cellStyles)break;for(;e.e>=e.s;)R[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},N||(N=!0,rs(e.w/256)),ts(R[e.e+1]);break;case 161:b["!autofilter"]={ref:It(e)};break;case 476:b["!margins"]=e;break;case 147:n.Sheets[t]||(n.Sheets[t]={}),e.name&&(n.Sheets[t].CodeName=e.name),(e.above||e.left)&&(b["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:case 37:E=!0;break;case 36:case 38:E=!1;break;default:if(r.T);else if(!E||c.WTF)throw new Error("Unexpected record 0x"+x.toString(16))}}),c),delete c.supbooks,delete c["!row"],!b["!ref"]&&(T.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(b["!ref"]=It(o||T)),c.sheetRows&&b["!ref"]){var D=Nt(b["!ref"]);c.sheetRows<=+D.e.r&&(D.e.r=c.sheetRows-1,D.e.r>T.e.r&&(D.e.r=T.e.r),D.e.r<D.s.r&&(D.s.r=D.e.r),D.e.c>T.e.c&&(D.e.c=T.e.c),D.e.c<D.s.c&&(D.s.c=D.e.c),b["!fullref"]=b["!ref"],b["!ref"]=It(D))}return A.length>0&&(b["!merges"]=A),R.length>0&&(b["!cols"]=R),I.length>0&&(b["!rows"]=I),b}(e,a,t,n,s,i,c):Oi(e,a,t,n,s,i,c)}function Ji(e,r,t,a,n,s,i,c){return".bin"===r.slice(-4)?function(e,r,t,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return bt(e,(function(e,a,c){switch(c){case 550:s["!rel"]=e;break;case 651:n.Sheets[t]||(n.Sheets[t]={}),e.name&&(n.Sheets[t].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(a.T>0);else if(a.T<0);else if(!i||r.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}(e,a,t,n,s):function(e,r,t,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},c=e.match(_i);return c&&Ri(c[0],0,n,t),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}(e,0,t,n,s)}function qi(e,r,t,a){return".bin"===r.slice(-4)?function(e,r,t){var a={NumberFmt:[]};for(var n in H)a.NumberFmt[n]=H[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return bt(e,(function(e,n,c){switch(c){case 44:a.NumberFmt[e[0]]=e[1],we(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&r&&r.themeElements&&r.themeElements.clrScheme&&(e.color.rgb=Kn(r.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(c),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(c);else if(n.T<0)s.pop();else if(!i||t.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}})),a}(e,t,a):os(e,t,a)}function Zi(e,r,t){return".bin"===r.slice(-4)?function(e,r){var t=[],a=!1;return bt(e,(function(e,n,s){switch(s){case 159:t.Count=e[0],t.Unique=e[1];break;case 19:t.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||r.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),t}(e,t):function(e,r){var t=[],a="";if(!e)return t;var n=e.match(In);if(n){a=n[2].replace(Nn,"").split(Dn);for(var s=0;s!=a.length;++s){var i=Rn(a[s].trim(),r);null!=i&&(t[t.length]=i)}n=fr(n[1]),t.Count=n.count,t.Unique=n.uniqueCount}return t}(e,t)}function Qi(e,r,t){return".bin"===r.slice(-4)?function(e,r){var t=[],a=[],n={},s=!1;return bt(e,(function(e,i,c){switch(c){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,r.sheetRows&&n.rfx&&r.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,t.push(n);break;case 3072:case 37:case 38:break;case 35:s=!0;break;case 36:s=!1;break;default:if(i.T);else if(!s||r.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}})),t}(e,t):function(e,r){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var t=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);r&&t.push(r[1])}}));var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(n){var s=fr(n[0]),i={author:s.authorId&&t[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},c=xt(s.ref);if(!(r.sheetRows&&r.sheetRows<=c.r)){var o=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!o&&!!o[1]&&Rn(o[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),r.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}(e,t)}function ec(e,r,t){return".bin"===r.slice(-4)?function(e,r,t){var a=[];return bt(e,(function(e,r,t){if(63===t)a.push(e);else if(!r.T)throw new Error("Unexpected record 0x"+t.toString(16))})),a}(e):function(e){var r=[];if(!e)return r;var t=1;return(e.match(cr)||[]).forEach((function(e){var a=fr(e);switch(a[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?t=a.i:a.i=t,r.push(a)}})),r}(e)}function rc(e,r,t,a){if(".bin"===t.slice(-4))return function(e,r,t,a){if(!e)return e;var n=a||{},s=!1;bt(e,(function(e,r,t){switch(t){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(r.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+t.toString(16))}}),n)}(e,0,0,a)}function tc(e,r,t){return".bin"===r.slice(-4)?function(e,r,t){var a={Types:[],Cell:[],Value:[]},n=t||{},s=[],i=!1,c=2;return bt(e,(function(e,r,t){switch(t){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==c?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==c&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:c=e?1:0;break;case 338:c=2;break;case 35:s.push(t),i=!0;break;case 36:s.pop(),i=!1;break;default:if(r.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+t.toString(16))}})),a}(e,0,t):function(e,r,t){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(cr,(function(e){var r=fr(e);switch(hr(r[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":a.Types.push({name:r.name});break;case"<futureMetadata":for(var c=0;c<a.Types.length;++c)a.Types[c].name==r.name&&(n=a.Types[c]);break;case"<rc":1==i?a.Cell.push({type:a.Types[r.t-1].name,index:+r.v}):0==i&&a.Value.push({type:a.Types[r.t-1].name,index:+r.v});break;case"<cellMetadata":i=1;break;case"</cellMetadata>":case"</valueMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+r.i);break;default:if(!s&&t.WTF)throw new Error("unrecognized "+r[0]+" in metadata")}return e})),a}(e,0,t)}var ac,nc=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,sc=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function ic(e,r){var t=e.split(/\s+/),a=[];if(r||(a[0]=t[0]),1===t.length)return a;var n,s,i,c=e.match(nc);if(c)for(i=0;i!=c.length;++i)-1===(s=(n=c[i].match(sc))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function cc(e){var r={};if(1===e.split(/\s+/).length)return r;var t,a,n,s=e.match(nc);if(s)for(n=0;n!=s.length;++n)-1===(a=(t=s[n].match(sc))[1].indexOf(":"))?r[t[1]]=t[2].slice(1,t[2].length-1):r["xmlns:"===t[1].slice(0,6)?"xmlns"+t[1].slice(6):t[1].slice(a+1)]=t[2].slice(1,t[2].length-1);return r}function oc(e,r,t,a){var n=a;switch((t[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=Tr(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Be(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+t[0])}e[pr(r)]=n}function lc(e,r,t){if("z"!==e.t){if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||ca[e.v]:"General"===r?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v):e.w=Q(e.v):e.w=(a=r||"General",n=e.v,"General"===(s=ac[a]||pr(a))?Q(n):Ee(s,n))}catch(o){if(t.WTF)throw o}var a,n,s;try{var i=ac[r]||r||"General";if(t.cellNF&&(e.z=i),t.cellDates&&"n"==e.t&&ge(i)){var c=j(e.v);c&&(e.t="d",e.v=new Date(c.y,c.m-1,c.d,c.H,c.M,c.S,c.u))}}catch(o){if(t.WTF)throw o}}}function fc(e,r,t){if(t.cellStyles&&r.Interior){var a=r.Interior;a.Pattern&&(a.patternType=ss[a.Pattern]||a.Pattern)}e[r.ID]=r}function hc(e,r,t,a,n,s,i,c,o,l){var f="General",h=a.StyleID,u={};l=l||{};var d=[],p=0;for(void 0===h&&c&&(h=c.StyleID),void 0===h&&i&&(h=i.StyleID);void 0!==s[h]&&(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),s[h].Parent);)h=s[h].Parent;switch(t.Type){case"Boolean":a.t="b",a.v=Tr(e);break;case"String":a.t="s",a.r=br(pr(e)),a.v=e.indexOf("<")>-1?pr(r||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(Be(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=pr(e):a.v<60&&(a.v=a.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=oa[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==r?a.t="z":(a.t="s",a.v=br(r||e))}if(lc(a,f,l),!1!==l.cellFormula)if(a.Formula){var m=pr(a.Formula);61==m.charCodeAt(0)&&(m=m.slice(1)),a.f=ks(m,n),delete a.Formula,"RC"==a.ArrayRange?a.F=ks("RC:RC",n):a.ArrayRange&&(a.F=ks(a.ArrayRange,n),o.push([Nt(a.F),a.F]))}else for(p=0;p<o.length;++p)n.r>=o[p][0].s.r&&n.r<=o[p][0].e.r&&n.c>=o[p][0].s.c&&n.c<=o[p][0].e.c&&(a.F=o[p][1]);l.cellStyles&&(d.forEach((function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)})),a.s=u),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function uc(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function dc(e,r){var t=r||{};Ae();var a=g(Fr(e));"binary"!=t.type&&"array"!=t.type&&"base64"!=t.type||(a=kr(a));var n,s=a.slice(0,1024).toLowerCase(),i=!1;if((1023&(s=s.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&s.indexOf(","),1023&s.indexOf(";"))){var c=He(t);return c.type="string",Sn.to_workbook(a,c)}if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){s.indexOf("<"+e)>=0&&(i=!0)})),i)return function(e,r){var t=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!t||0==t.length)throw new Error("Invalid HTML: could not find <table>");if(1==t.length)return Pt(Cc(t[0],r),r);var a={SheetNames:[],Sheets:{}};return t.forEach((function(e,t){vo(a,Cc(e,r),"Sheet"+(t+1))})),a}(a,t);ac={"General Number":"General","General Date":H[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":H[15],"Short Date":H[14],"Long Time":H[19],"Medium Time":H[18],"Short Time":H[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:H[2],Standard:H[4],Percent:H[10],Scientific:H[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var o,l,f=[],h={},u=[],d=t.dense?[]:{},p="",m={},v={},b=ic('<Data ss:Type="String">'),T=0,E=0,w=0,A={s:{r:2e6,c:2e6},e:{r:0,c:0}},S={},k={},y="",_=0,C=[],x={},O={},R=0,I=[],N=[],D={},F=[],P=!1,M=[],L=[],U={},B=0,V=0,W={Sheets:[],WBProps:{date1904:!1}},z={};Pr.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");for(var G="";n=Pr.exec(a);)switch(n[3]=(G=n[3]).toLowerCase()){case"data":if("data"==G){if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break}if(f[f.length-1][1])break;"/"===n[1]?hc(a.slice(T,n.index),y,b,"comment"==f[f.length-1][0]?D:m,{c:E,r:w},S,F[E],v,M,t):(y="",b=ic(n[0]),T=n.index+n[0].length);break;case"cell":if("/"===n[1])if(N.length>0&&(m.c=N),(!t.sheetRows||t.sheetRows>w)&&void 0!==m.v&&(t.dense?(d[w]||(d[w]=[]),d[w][E]=m):d[Ct(E)+yt(w)]=m),m.HRef&&(m.l={Target:pr(m.HRef)},m.HRefScreenTip&&(m.l.Tooltip=m.HRefScreenTip),delete m.HRef,delete m.HRefScreenTip),(m.MergeAcross||m.MergeDown)&&(B=E+(0|parseInt(m.MergeAcross,10)),V=w+(0|parseInt(m.MergeDown,10)),C.push({s:{c:E,r:w},e:{c:B,r:V}})),t.sheetStubs)if(m.MergeAcross||m.MergeDown){for(var $=E;$<=B;++$)for(var j=w;j<=V;++j)($>E||j>w)&&(t.dense?(d[j]||(d[j]=[]),d[j][$]={t:"z"}):d[Ct($)+yt(j)]={t:"z"});E=B+1}else++E;else m.MergeAcross?E=B+1:++E;else(m=cc(n[0])).Index&&(E=+m.Index-1),E<A.s.c&&(A.s.c=E),E>A.e.c&&(A.e.c=E),"/>"===n[0].slice(-2)&&++E,N=[];break;case"row":"/"===n[1]||"/>"===n[0].slice(-2)?(w<A.s.r&&(A.s.r=w),w>A.e.r&&(A.e.r=w),"/>"===n[0].slice(-2)&&(v=ic(n[0])).Index&&(w=+v.Index-1),E=0,++w):((v=ic(n[0])).Index&&(w=+v.Index-1),U={},("0"==v.AutoFitHeight||v.Height)&&(U.hpx=parseInt(v.Height,10),U.hpt=as(U.hpx),L[w]=U),"1"==v.Hidden&&(U.hidden=!0,L[w]=U));break;case"worksheet":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));u.push(p),A.s.r<=A.e.r&&A.s.c<=A.e.c&&(d["!ref"]=It(A),t.sheetRows&&t.sheetRows<=A.e.r&&(d["!fullref"]=d["!ref"],A.e.r=t.sheetRows-1,d["!ref"]=It(A))),C.length&&(d["!merges"]=C),F.length>0&&(d["!cols"]=F),L.length>0&&(d["!rows"]=L),h[p]=d}else A={s:{r:2e6,c:2e6},e:{r:0,c:0}},w=E=0,f.push([n[3],!1]),o=ic(n[0]),p=pr(o.Name),d=t.dense?[]:{},C=[],M=[],L=[],z={name:p,Hidden:0},W.Sheets.push(z);break;case"table":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else{if("/>"==n[0].slice(-2))break;f.push([n[3],!1]),F=[],P=!1}break;case"style":"/"===n[1]?fc(S,k,t):k=ic(n[0]);break;case"numberformat":k.nf=pr(ic(n[0]).Format||"General"),ac[k.nf]&&(k.nf=ac[k.nf]);for(var X=0;392!=X&&H[X]!=k.nf;++X);if(392==X)for(X=57;392!=X;++X)if(null==H[X]){we(k.nf,X);break}break;case"column":if("table"!==f[f.length-1][0])break;if((l=ic(n[0])).Hidden&&(l.hidden=!0,delete l.Hidden),l.Width&&(l.wpx=parseInt(l.Width,10)),!P&&l.wpx>10){P=!0,Jn=6;for(var Y=0;Y<F.length;++Y)F[Y]&&ts(F[Y])}P&&ts(l),F[l.Index-1||F.length]=l;for(var K=0;K<+l.Span;++K)F[F.length]=He(l);break;case"namedrange":if("/"===n[1])break;W.Names||(W.Names=[]);var J=fr(n[0]),q={Name:J.Name,Ref:ks(J.RefersTo.slice(1),{r:0,c:0})};W.Sheets.length>0&&(q.Sheet=W.Sheets.length-1),W.Names.push(q);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":if("/>"===n[0].slice(-2))break;"/"===n[1]?y+=a.slice(_,n.index):_=n.index+n[0].length;break;case"interior":if(!t.cellStyles)break;k.Interior=ic(n[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===n[0].slice(-2))break;"/"===n[1]?wa(x,G,a.slice(R,n.index)):R=n.index+n[0].length;break;case"styles":case"workbook":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else f.push([n[3],!1]);break;case"comment":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));uc(D),N.push(D)}else f.push([n[3],!1]),D={a:(o=ic(n[0])).Author};break;case"autofilter":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else if("/"!==n[0].charAt(n[0].length-2)){var Z=ic(n[0]);d["!autofilter"]={ref:ks(Z.Range).replace(/\$/g,"")},f.push([n[3],!0])}break;case"datavalidation":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break;default:if(0==f.length&&"document"==n[3])return Pc(a,t);if(0==f.length&&"uof"==n[3])return Pc(a,t);var Q=!0;switch(f[f.length-1][0]){case"officedocumentsettings":switch(n[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:Q=!1}break;case"componentoptions":switch(n[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:Q=!1}break;case"excelworkbook":switch(n[3]){case"date1904":W.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:Q=!1}break;case"workbookoptions":switch(n[3]){case"owcversion":case"height":case"width":break;default:Q=!1}break;case"worksheetoptions":switch(n[3]){case"visible":if("/>"===n[0].slice(-2));else if("/"===n[1])switch(a.slice(R,n.index)){case"SheetHidden":z.Hidden=1;break;case"SheetVeryHidden":z.Hidden=2}else R=n.index+n[0].length;break;case"header":d["!margins"]||vi(d["!margins"]={},"xlml"),isNaN(+fr(n[0]).Margin)||(d["!margins"].header=+fr(n[0]).Margin);break;case"footer":d["!margins"]||vi(d["!margins"]={},"xlml"),isNaN(+fr(n[0]).Margin)||(d["!margins"].footer=+fr(n[0]).Margin);break;case"pagemargins":var ee=fr(n[0]);d["!margins"]||vi(d["!margins"]={},"xlml"),isNaN(+ee.Top)||(d["!margins"].top=+ee.Top),isNaN(+ee.Left)||(d["!margins"].left=+ee.Left),isNaN(+ee.Right)||(d["!margins"].right=+ee.Right),isNaN(+ee.Bottom)||(d["!margins"].bottom=+ee.Bottom);break;case"displayrighttoleft":W.Views||(W.Views=[]),W.Views[0]||(W.Views[0]={}),W.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].left=!0;break;default:Q=!1}break;case"pivottable":case"pivotcache":switch(n[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:Q=!1}break;case"pagebreaks":switch(n[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:Q=!1}break;case"autofilter":switch(n[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:Q=!1}break;case"querytable":switch(n[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:Q=!1}break;case"datavalidation":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:Q=!1}break;case"sorting":case"conditionalformatting":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:Q=!1}break;case"mapinfo":case"schema":case"data":switch(n[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:Q=!1}break;case"smarttags":break;default:Q=!1}if(Q)break;if(n[3].match(/!\[CDATA/))break;if(!f[f.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+f.join("|");if("customdocumentproperties"===f[f.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?oc(O,G,I,a.slice(R,n.index)):(I=n,R=n.index+n[0].length);break}if(t.WTF)throw"Unrecognized tag: "+n[3]+"|"+f.join("|")}var re={};return t.bookSheets||t.bookProps||(re.Sheets=h),re.SheetNames=u,re.Workbook=W,re.SSF=He(H),re.Props=x,re.Custprops=O,re}function pc(e,r){switch(Qc(r=r||{}),r.type||"base64"){case"base64":return dc(w(e),r);case"binary":case"buffer":case"file":return dc(e,r);case"array":return dc(C(e),r)}}function mc(e){var r={},t=e.content;if(t.l=28,r.AnsiUserType=t.read_shift(0,"lpstr-ansi"),r.AnsiClipboardFormat=function(e){return Zt(e,1)}(t),t.length-t.l<=4)return r;var a=t.read_shift(4);return 0==a||a>40?r:(t.l-=4,r.Reserved1=t.read_shift(0,"lpstr-ansi"),t.length-t.l<=4||1907505652!==(a=t.read_shift(4))?r:(r.UnicodeClipboardFormat=function(e){return Zt(e,2)}(t),0==(a=t.read_shift(4))||a>40?r:(t.l-=4,void(r.Reserved2=t.read_shift(0,"lpwstr")))))}var gc=[60,1084,2066,2165,2175];function vc(e,r,t,a,n){var s=a,i=[],c=t.slice(t.l,t.l+s);if(n&&n.enc&&n.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(c)}i.push(c),t.l+=s;for(var o=it(t,t.l),l=yc[o],f=0;null!=l&&gc.indexOf(o)>-1;)s=it(t,t.l+2),f=t.l+4,2066==o?f+=4:2165!=o&&2175!=o||(f+=12),c=t.slice(f,t.l+4+s),i.push(c),t.l+=4+s,l=yc[o=it(t,t.l)];var h=O(i);mt(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return r.f(h,h.length,n)}function bc(e,r,t){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,r.cellNF&&(e.z=H[a])}catch(s){if(r.WTF)throw s}if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||ca[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v):e.w=Q(e.v):e.w=Ee(a,e.v,{date1904:!!t,dateNF:r&&r.dateNF})}catch(s){if(r.WTF)throw s}if(r.cellDates&&a&&"n"==e.t&&ge(H[a]||String(a))){var n=j(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function Tc(e,r,t){return{v:e,ixfe:r,t:t}}function Ec(e,r){var t,a,s,i,c,o,l,f,u={opts:{}},d={},p=r.dense?[]:{},m={},g={},v=null,b=[],T="",E={},w="",A={},S=[],k=[],y=[],_={Sheets:[],WBProps:{date1904:!1},Views:[{}]},C={},x=function(e){return e<8?ia[e]:e<64&&y[e-8]||ia[e]},O=function(e,r,a){if(!(B>1||a.sheetRows&&e.r>=a.sheetRows)){if(a.cellStyles&&r.XF&&r.XF.data&&function(e,r,t){var a,n=r.XF.data;n&&n.patternType&&t&&t.cellStyles&&(r.s={},r.s.patternType=n.patternType,(a=Yn(x(n.icvFore)))&&(r.s.fgColor={rgb:a}),(a=Yn(x(n.icvBack)))&&(r.s.bgColor={rgb:a}))}(0,r,a),delete r.ixfe,delete r.XF,t=e,w=Ot(e),g&&g.s&&g.e||(g={s:{r:0,c:0},e:{r:0,c:0}}),e.r<g.s.r&&(g.s.r=e.r),e.c<g.s.c&&(g.s.c=e.c),e.r+1>g.e.r&&(g.e.r=e.r+1),e.c+1>g.e.c&&(g.e.c=e.c+1),a.cellFormula&&r.f)for(var n=0;n<S.length;++n)if(!(S[n][0].s.c>e.c||S[n][0].s.r>e.r||S[n][0].e.c<e.c||S[n][0].e.r<e.r)){r.F=It(S[n][0]),S[n][0].s.c==e.c&&S[n][0].s.r==e.r||delete r.f,r.f&&(r.f=""+ri(S[n][1],0,e,M,R));break}a.dense?(p[e.r]||(p[e.r]=[]),p[e.r][e.c]=r):p[w]=r}},R={enc:!1,sbcch:0,snames:[],sharedf:A,arrayf:S,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!r&&!!r.cellStyles,WTF:!!r&&!!r.wtf};r.password&&(R.password=r.password);var I=[],N=[],D=[],F=[],P=!1,M=[];M.SheetNames=R.snames,M.sharedf=R.sharedf,M.arrayf=R.arrayf,M.names=[],M.XTI=[];var L,U=0,B=0,V=0,W=[],z=[];R.codepage=1200,h(1200);for(var G=!1;e.l<e.length-1;){var $=e.l,j=e.read_shift(2);if(0===j&&10===U)break;var X=e.l===e.length?0:e.read_shift(2),Y=yc[j];if(Y&&Y.f){if(r.bookSheets&&133===U&&133!==j)break;if(U=j,2===Y.r||12==Y.r){var K=e.read_shift(2);if(X-=2,!R.enc&&K!==j&&((255&K)<<8|K>>8)!==j)throw new Error("rt mismatch: "+K+"!="+j);12==Y.r&&(e.l+=10,X-=10)}var J={};if(J=10===j?Y.f(e,X,R):vc(j,Y,e,X,R),0==B&&-1===[9,521,1033,2057].indexOf(U))continue;switch(j){case 34:u.opts.Date1904=_.WBProps.date1904=J;break;case 134:u.opts.WriteProtect=!0;break;case 47:if(R.enc||(e.l=0),R.enc=J,!r.password)throw new Error("File is password-protected");if(null==J.valid)throw new Error("Encryption scheme unsupported");if(!J.valid)throw new Error("Password is incorrect");break;case 92:R.lastuser=J;break;case 66:var q=Number(J);switch(q){case 21010:q=1200;break;case 32768:q=1e4;break;case 32769:q=1252}h(R.codepage=q),G=!0;break;case 317:R.rrtabid=J;break;case 25:R.winlocked=J;break;case 439:u.opts.RefreshAll=J;break;case 12:u.opts.CalcCount=J;break;case 16:u.opts.CalcDelta=J;break;case 17:u.opts.CalcIter=J;break;case 13:u.opts.CalcMode=J;break;case 14:u.opts.CalcPrecision=J;break;case 95:u.opts.CalcSaveRecalc=J;break;case 15:R.CalcRefMode=J;break;case 2211:u.opts.FullCalc=J;break;case 129:J.fDialog&&(p["!type"]="dialog"),J.fBelow||((p["!outline"]||(p["!outline"]={})).above=!0),J.fRight||((p["!outline"]||(p["!outline"]={})).left=!0);break;case 224:k.push(J);break;case 430:M.push([J]),M[M.length-1].XTI=[];break;case 35:case 547:M[M.length-1].push(J);break;case 24:case 536:L={Name:J.Name,Ref:ri(J.rgce,0,null,M,R)},J.itab>0&&(L.Sheet=J.itab-1),M.names.push(L),M[0]||(M[0]=[],M[0].XTI=[]),M[M.length-1].push(J),"_xlnm._FilterDatabase"==J.Name&&J.itab>0&&J.rgce&&J.rgce[0]&&J.rgce[0][0]&&"PtgArea3d"==J.rgce[0][0][0]&&(z[J.itab-1]={ref:It(J.rgce[0][0][1][2])});break;case 22:R.ExternCount=J;break;case 23:0==M.length&&(M[0]=[],M[0].XTI=[]),M[M.length-1].XTI=M[M.length-1].XTI.concat(J),M.XTI=M.XTI.concat(J);break;case 2196:if(R.biff<8)break;null!=L&&(L.Comment=J[1]);break;case 18:p["!protect"]=J;break;case 19:0!==J&&R.WTF&&n("error","at node_modules/xlsx/xlsx.mjs:18748","Password verifier: "+J);break;case 133:m[J.pos]=J,R.snames.push(J.name);break;case 10:if(--B)break;if(g.e){if(g.e.r>0&&g.e.c>0){if(g.e.r--,g.e.c--,p["!ref"]=It(g),r.sheetRows&&r.sheetRows<=g.e.r){var Z=g.e.r;g.e.r=r.sheetRows-1,p["!fullref"]=p["!ref"],p["!ref"]=It(g),g.e.r=Z}g.e.r++,g.e.c++}I.length>0&&(p["!merges"]=I),N.length>0&&(p["!objects"]=N),D.length>0&&(p["!cols"]=D),F.length>0&&(p["!rows"]=F),_.Sheets.push(C)}""===T?E=p:d[T]=p,p=r.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===R.biff&&(R.biff={9:2,521:3,1033:4}[j]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[J.BIFFVer]||8),R.biffguess=0==J.BIFFVer,0==J.BIFFVer&&4096==J.dt&&(R.biff=5,G=!0,h(R.codepage=28591)),8==R.biff&&0==J.BIFFVer&&16==J.dt&&(R.biff=2),B++)break;if(p=r.dense?[]:{},R.biff<8&&!G&&(G=!0,h(R.codepage=r.codepage||1252)),R.biff<5||0==J.BIFFVer&&4096==J.dt){""===T&&(T="Sheet1"),g={s:{r:0,c:0},e:{r:0,c:0}};var Q={pos:e.l-X,name:T};m[Q.pos]=Q,R.snames.push(T)}else T=(m[$]||{name:""}).name;32==J.dt&&(p["!type"]="chart"),64==J.dt&&(p["!type"]="macro"),I=[],N=[],R.arrayf=S=[],D=[],F=[],P=!1,C={Hidden:(m[$]||{hs:0}).hs,name:T};break;case 515:case 3:case 2:"chart"==p["!type"]&&(r.dense?(p[J.r]||[])[J.c]:p[Ot({c:J.c,r:J.r})])&&++J.c,o={ixfe:J.ixfe,XF:k[J.ixfe]||{},v:J.val,t:"n"},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r);break;case 5:case 517:o={ixfe:J.ixfe,XF:k[J.ixfe],v:J.val,t:J.t},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r);break;case 638:o={ixfe:J.ixfe,XF:k[J.ixfe],v:J.rknum,t:"n"},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r);break;case 189:for(var ee=J.c;ee<=J.C;++ee){var re=J.rkrec[ee-J.c][0];o={ixfe:re,XF:k[re],v:J.rkrec[ee-J.c][1],t:"n"},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:ee,r:J.r},o,r)}break;case 6:case 518:case 1030:if("String"==J.val){v=J;break}if((o=Tc(J.val,J.cell.ixfe,J.tt)).XF=k[o.ixfe],r.cellFormula){var te=J.formula;if(te&&te[0]&&te[0][0]&&"PtgExp"==te[0][0][0]){var ae=te[0][0][1][0],ne=te[0][0][1][1],se=Ot({r:ae,c:ne});A[se]?o.f=""+ri(J.formula,0,J.cell,M,R):o.F=((r.dense?(p[ae]||[])[ne]:p[se])||{}).F}else o.f=""+ri(J.formula,0,J.cell,M,R)}V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O(J.cell,o,r),v=J;break;case 7:case 519:if(!v)throw new Error("String record expects Formula");v.val=J,(o=Tc(J,v.cell.ixfe,"s")).XF=k[o.ixfe],r.cellFormula&&(o.f=""+ri(v.formula,0,v.cell,M,R)),V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O(v.cell,o,r),v=null;break;case 33:case 545:S.push(J);var ie=Ot(J[0].s);if(a=r.dense?(p[J[0].s.r]||[])[J[0].s.c]:p[ie],r.cellFormula&&a){if(!v)break;if(!ie||!a)break;a.f=""+ri(J[1],0,J[0],M,R),a.F=It(J[0])}break;case 1212:if(!r.cellFormula)break;if(w){if(!v)break;A[Ot(v.cell)]=J[0],((a=r.dense?(p[v.cell.r]||[])[v.cell.c]:p[Ot(v.cell)])||{}).f=""+ri(J[0],0,t,M,R)}break;case 253:o=Tc(b[J.isst].t,J.ixfe,"s"),b[J.isst].h&&(o.h=b[J.isst].h),o.XF=k[o.ixfe],V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r);break;case 513:r.sheetStubs&&(o={ixfe:J.ixfe,XF:k[J.ixfe],t:"z"},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r));break;case 190:if(r.sheetStubs)for(var ce=J.c;ce<=J.C;++ce){var oe=J.ixfe[ce-J.c];o={ixfe:oe,XF:k[oe],t:"z"},V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:ce,r:J.r},o,r)}break;case 214:case 516:case 4:(o=Tc(J.val,J.ixfe,"s")).XF=k[o.ixfe],V>0&&(o.z=W[o.ixfe>>8&63]),bc(o,r,u.opts.Date1904),O({c:J.c,r:J.r},o,r);break;case 0:case 512:1===B&&(g=J);break;case 252:b=J;break;case 1054:if(4==R.biff){W[V++]=J[1];for(var le=0;le<V+163&&H[le]!=J[1];++le);le>=163&&we(J[1],V+163)}else we(J[1],J[0]);break;case 30:W[V++]=J;for(var fe=0;fe<V+163&&H[fe]!=J;++fe);fe>=163&&we(J,V+163);break;case 229:I=I.concat(J);break;case 93:N[J.cmo[0]]=R.lastobj=J;break;case 438:R.lastobj.TxO=J;break;case 127:R.lastobj.ImData=J;break;case 440:for(c=J[0].s.r;c<=J[0].e.r;++c)for(i=J[0].s.c;i<=J[0].e.c;++i)(a=r.dense?(p[c]||[])[i]:p[Ot({c:i,r:c})])&&(a.l=J[1]);break;case 2048:for(c=J[0].s.r;c<=J[0].e.r;++c)for(i=J[0].s.c;i<=J[0].e.c;++i)(a=r.dense?(p[c]||[])[i]:p[Ot({c:i,r:c})])&&a.l&&(a.l.Tooltip=J[1]);break;case 28:if(R.biff<=5&&R.biff>=2)break;a=r.dense?(p[J[0].r]||[])[J[0].c]:p[Ot(J[0])];var he=N[J[2]];a||(r.dense?(p[J[0].r]||(p[J[0].r]=[]),a=p[J[0].r][J[0].c]={t:"z"}):a=p[Ot(J[0])]={t:"z"},g.e.r=Math.max(g.e.r,J[0].r),g.s.r=Math.min(g.s.r,J[0].r),g.e.c=Math.max(g.e.c,J[0].c),g.s.c=Math.min(g.s.c,J[0].c)),a.c||(a.c=[]),s={a:J[1],t:he.TxO.t},a.c.push(s);break;case 2173:k[J.ixfe],J.ext.forEach((function(e){e[0]}));break;case 125:if(!R.cellStyles)break;for(;J.e>=J.s;)D[J.e--]={width:J.w/256,level:J.level||0,hidden:!!(1&J.flags)},P||(P=!0,rs(J.w/256)),ts(D[J.e+1]);break;case 520:var ue={};null!=J.level&&(F[J.r]=ue,ue.level=J.level),J.hidden&&(F[J.r]=ue,ue.hidden=!0),J.hpt&&(F[J.r]=ue,ue.hpt=J.hpt,ue.hpx=ns(J.hpt));break;case 38:case 39:case 40:case 41:p["!margins"]||vi(p["!margins"]={}),p["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[j]]=J;break;case 161:p["!margins"]||vi(p["!margins"]={}),p["!margins"].header=J.header,p["!margins"].footer=J.footer;break;case 574:J.RTL&&(_.Views[0].RTL=!0);break;case 146:y=J;break;case 2198:f=J;break;case 140:l=J;break;case 442:T?C.CodeName=J||C.name:_.WBProps.CodeName=J||"ThisWorkbook"}}else Y||n("error","at node_modules/xlsx/xlsx.mjs:19034","Missing Info for XLS Record 0x"+j.toString(16)),e.l+=X}return u.SheetNames=Ce(m).sort((function(e,r){return Number(e)-Number(r)})).map((function(e){return m[e].name})),r.bookSheets||(u.Sheets=d),!u.SheetNames.length&&E["!ref"]?(u.SheetNames.push("Sheet1"),u.Sheets&&(u.Sheets.Sheet1=E)):u.Preamble=E,u.Sheets&&z.forEach((function(e,r){u.Sheets[u.SheetNames[r]]["!autofilter"]=e})),u.Strings=b,u.SSF=He(H),R.enc&&(u.Encryption=R.enc),f&&(u.Themes=f),u.Metadata={},void 0!==l&&(u.Metadata.Country=l),M.names.length>0&&(_.Names=M.names),u.Workbook=_,u}var wc="e0859ff2f94f6810ab9108002b27b3d9",Ac="02d5cdd59c2e1b10939708002b2cf9ae";function Sc(e,r){var t,a,n,s;if(r||(r={}),Qc(r),u(),r.codepage&&f(r.codepage),e.FullPaths){if(_e.find(e,"/encryption"))throw new Error("File is password-protected");t=_e.find(e,"!CompObj"),a=_e.find(e,"/Workbook")||_e.find(e,"/Book")}else{switch(r.type){case"base64":e=_(w(e));break;case"binary":e=_(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}mt(e,0),a={content:e}}if(t&&mc(t),r.bookProps&&!r.bookSheets)n={};else{var i=A?"buffer":"array";if(a&&a.content)n=Ec(a.content,r);else if((s=_e.find(e,"PerfectOffice_MAIN"))&&s.content)n=kn.to_workbook(s.content,(r.type=i,r));else{if(!(s=_e.find(e,"NativeContent_MAIN"))||!s.content)throw(s=_e.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");n=kn.to_workbook(s.content,(r.type=i,r))}r.bookVBA&&e.FullPaths&&_e.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=function(e){var r=_e.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(t,a){if("/"!==t.slice(-1)&&t.match(/_VBA_PROJECT_CUR/)){var n=t.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");_e.utils.cfb_add(r,n,e.FileIndex[a].content)}})),_e.write(r)}(e))}var c={};return e.FullPaths&&function(e,r,t){var a=_e.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Na(a,ra,Ac);for(var s in n)r[s]=n[s]}catch(l){if(t.WTF)throw l}var i=_e.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=Na(i,ta,wc);for(var o in c)null==r[o]&&(r[o]=c[o])}catch(l){if(t.WTF)throw l}r.HeadingPairs&&r.TitlesOfParts&&(va(r.HeadingPairs,r.TitlesOfParts,r,t),delete r.HeadingPairs,delete r.TitlesOfParts)}(e,c,r),n.Props=n.Custprops=c,r.bookFiles&&(n.cfb=e),n}var kc={0:{f:function(e,r){var t={},a=e.l+r;t.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(t.level=7&s),16&s&&(t.hidden=!0),32&s&&(t.hpt=n/20),t}},1:{f:function(e){return[Wt(e)]}},2:{f:function(e){return[Wt(e),Yt(e),"n"]}},3:{f:function(e){return[Wt(e),e.read_shift(1),"e"]}},4:{f:function(e){return[Wt(e),e.read_shift(1),"b"]}},5:{f:function(e){return[Wt(e),qt(e),"n"]}},6:{f:function(e){return[Wt(e),Ut(e),"str"]}},7:{f:function(e){return[Wt(e),e.read_shift(4),"s"]}},8:{f:function(e,r,t){var a=e.l+r,n=Wt(e);n.r=t["!row"];var s=[n,Ut(e),"str"];if(t.cellFormula){e.l+=2;var i=ci(e,a-e.l,t);s[3]=ri(i,0,n,t.supbooks,t)}else e.l=a;return s}},9:{f:function(e,r,t){var a=e.l+r,n=Wt(e);n.r=t["!row"];var s=[n,qt(e),"n"];if(t.cellFormula){e.l+=2;var i=ci(e,a-e.l,t);s[3]=ri(i,0,n,t.supbooks,t)}else e.l=a;return s}},10:{f:function(e,r,t){var a=e.l+r,n=Wt(e);n.r=t["!row"];var s=[n,e.read_shift(1),"b"];if(t.cellFormula){e.l+=2;var i=ci(e,a-e.l,t);s[3]=ri(i,0,n,t.supbooks,t)}else e.l=a;return s}},11:{f:function(e,r,t){var a=e.l+r,n=Wt(e);n.r=t["!row"];var s=[n,e.read_shift(1),"e"];if(t.cellFormula){e.l+=2;var i=ci(e,a-e.l,t);s[3]=ri(i,0,n,t.supbooks,t)}else e.l=a;return s}},12:{f:function(e){return[zt(e)]}},13:{f:function(e){return[zt(e),Yt(e),"n"]}},14:{f:function(e){return[zt(e),e.read_shift(1),"e"]}},15:{f:function(e){return[zt(e),e.read_shift(1),"b"]}},16:{f:Fi},17:{f:function(e){return[zt(e),Ut(e),"str"]}},18:{f:function(e){return[zt(e),e.read_shift(4),"s"]}},19:{f:Vt},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,r,t){var a=e.l+r;e.l+=4,e.l+=1;var n=e.read_shift(4),s=jt(e),i=oi(e,0,t),c=$t(e);e.l=a;var o={Name:s,Ptg:i};return n<268435455&&(o.Sheet=n),c&&(o.Comment=c),o}},40:{},42:{},43:{f:function(e,r,t){var a={};a.sz=e.read_shift(2)/20;var n=function(e){var r=e.read_shift(1);return e.l++,{fBold:1&r,fItalic:2&r,fUnderline:4&r,fStrikeout:8&r,fOutline:16&r,fShadow:32&r,fCondense:64&r,fExtend:128&r}}(e);switch(n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var i=e.read_shift(1);i>0&&(a.family=i);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=function(e){var r={},t=e.read_shift(1)>>>1,a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(1);switch(e.l++,t){case 0:r.auto=1;break;case 1:r.index=a;var o=ia[a];o&&(r.rgb=Yn(o));break;case 2:r.rgb=Yn([s,i,c]);break;case 3:r.theme=a}return 0!=n&&(r.tint=n>0?n/32767:n/32768),r}(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=Ut(e),a}},44:{f:function(e,r){return[e.read_shift(2),Ut(e)]}},45:{f:ls},46:{f:fs},47:{f:function(e,r){var t=e.l+r,a=e.read_shift(2),n=e.read_shift(2);return e.l=t,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var r=[],t=e.read_shift(4);t-- >0;)r.push([e.read_shift(4),e.read_shift(4)]);return r}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:pn},62:{f:function(e){return[Wt(e),Vt(e),"is"]}},63:{f:function(e){var r={};r.i=e.read_shift(4);var t={};t.r=e.read_shift(4),t.c=e.read_shift(4),r.r=Ot(t);var a=e.read_shift(1);return 2&a&&(r.l="1"),8&a&&(r.a="1"),r}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:gt,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var r=e.read_shift(2);return e.l+=28,{RTL:32&r}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,r){var t={},a=e[e.l];return++e.l,t.above=!(64&a),t.left=!(128&a),e.l+=18,t.name=Gt(e),t}},148:{f:Di,p:16},151:{f:function(){}},152:{},153:{f:function(e,r){var t={},a=e.read_shift(4);t.defaultThemeVersion=e.read_shift(4);var n=r>8?Ut(e):"";return n.length>0&&(t.CodeName=n),t.autoCompressPictures=!!(65536&a),t.backupFile=!!(64&a),t.checkCompatibility=!!(4096&a),t.date1904=!!(1&a),t.filterPrivacy=!!(8&a),t.hidePivotFieldList=!!(1024&a),t.promptedSolutions=!!(16&a),t.publishItems=!!(2048&a),t.refreshAllConnections=!!(262144&a),t.saveExternalLinkValues=!!(128&a),t.showBorderUnselectedTables=!!(4&a),t.showInkAnnotation=!!(32&a),t.showObjects=["all","placeholders","none"][a>>13&3],t.showPivotChartFilter=!!(32768&a),t.updateLinks=["userSet","never","always"][a>>8&3],t}},154:{},155:{},156:{f:function(e,r){var t={};return t.Hidden=e.read_shift(4),t.iTabID=e.read_shift(4),t.strRelID=Xt(e),t.name=Ut(e),t}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:Jt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Pi},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,r){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ut(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Xt},357:{},358:{},359:{},360:{T:1},361:{},362:{f:hn},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,r,t){var a=e.l+r,n=Kt(e),s=e.read_shift(1),i=[n];if(i[2]=s,t.cellFormula){var c=ii(e,a-e.l,t);i[1]=c}else e.l=a;return i}},427:{f:function(e,r,t){var a=e.l+r,n=[Jt(e)];if(t.cellFormula){var s=li(e,a-e.l,t);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var r={};return Mi.forEach((function(t){r[t]=qt(e)})),r}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,r){var t=e.l+r,a=Jt(e),n=$t(e),s=Ut(e),i=Ut(e),c=Ut(e);e.l=t;var o={rfx:a,relId:n,loc:s,display:c};return i&&(o.Tooltip=i),o}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Xt},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Ss},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var r={};r.iauthor=e.read_shift(4);var t=Jt(e);return r.rfx=t.s,r.ref=Ot(t.s),e.l+=16,r}},636:{T:-1},637:{f:Ht},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,r){return e.l+=10,{name:Ut(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},yc={6:{f:ni},10:{f:Da},12:{f:Pa},13:{f:Pa},14:{f:Fa},15:{f:Fa},16:{f:qt},17:{f:Fa},18:{f:Fa},19:{f:Pa},20:{f:cn},21:{f:cn},23:{f:hn},24:{f:fn},25:{f:Fa},26:{},27:{},28:{f:function(e,r,t){return function(e,r,t){if(!(t.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=Ha(e,0,t);return t.biff<8&&e.read_shift(1),[{r:a,c:n},c,i,s]}}(e,0,t)}},29:{},34:{f:Fa},35:{f:on},38:{f:qt},39:{f:qt},40:{f:qt},41:{f:qt},42:{f:Fa},43:{f:Fa},47:{f:function(e,r,t){var a={Type:t.biff>=8?e.read_shift(2):0};return a.Type?jn(e,r-2,a):$n(e,t.biff,t,a),a}},49:{f:function(e,r,t){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(t&&t.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=La(e,0,t),a}},51:{f:Pa},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:Fa},65:{f:function(){}},66:{f:Pa},77:{},80:{},81:{},82:{},85:{f:Pa},89:{},90:{},91:{},92:{f:function(e,r,t){if(t.enc)return e.l+=r,"";var a=e.l,n=Ha(e,0,t);return e.read_shift(r+a-e.l),n}},93:{f:function(e,r,t){if(t&&t.biff<8)return function(e,r,t){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,r-=36;var i=[];return i.push((dn[a]||gt)(e,r,t)),{cmo:[n,a,s],ft:i}}(e,r,t);var a=Za(e),n=function(e,r){for(var t=e.l+r,a=[];e.l<t;){var n=e.read_shift(2);e.l-=2;try{a.push(en[n](e,t-e.l))}catch(s){return e.l=t,a}}return e.l!=t&&(e.l=t),a}(e,r-22,a[1]);return{cmo:a,ft:n}}},94:{},95:{f:Fa},96:{},97:{},99:{f:Fa},125:{f:pn},128:{f:function(e){e.l+=4;var r=[e.read_shift(2),e.read_shift(2)];if(0!==r[0]&&r[0]--,0!==r[1]&&r[1]--,r[0]>7||r[1]>7)throw new Error("Bad Gutters: "+r.join("|"));return r}},129:{f:function(e,r,t){var a=t&&8==t.biff||2==r?e.read_shift(2):(e.l+=r,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:Pa},131:{f:Fa},132:{f:Fa},133:{f:function(e,r,t){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=La(e,0,t);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var r,t=[0,0];return r=e.read_shift(2),t[0]=aa[r]||r,r=e.read_shift(2),t[1]=aa[r]||r,t}},141:{f:Pa},144:{},146:{f:function(e){for(var r=e.read_shift(2),t=[];r-- >0;)t.push($a(e));return t}},151:{},152:{},153:{},154:{},155:{},156:{f:Pa},157:{},158:{},160:{f:gn},161:{f:function(e,r){var t={};return r<32||(e.l+=16,t.header=qt(e),t.footer=qt(e),e.l+=2),t}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,r){for(var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<t;)s.push(Ya(e));if(e.l!==t)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,r){for(var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<t;)s.push(e.read_shift(2));if(e.l!==t)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:Da},197:{},198:{},199:{},200:{},201:{},202:{f:Fa},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Pa},220:{},221:{f:Fa},222:{},224:{f:function(e,r,t){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,6,a.data=function(e,r,t,a){var n={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),o=e.read_shift(2);return n.patternType=na[c>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&c,n.icvBottom=c>>7&127,n.icvDiag=c>>14&127,n.dgDiag=c>>21&15,n.icvFore=127&o,n.icvBack=o>>7&127,n.fsxButton=o>>14&1,n):n}(e,0,a.fStyle,t),a}},225:{f:function(e,r){return 0===r||e.read_shift(2),1200}},226:{f:Da},227:{},229:{f:function(e,r){for(var t=[],a=e.read_shift(2);a--;)t.push(Ka(e));return t}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,r){for(var t=e.l+r,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<t;++i)s.push(Ua(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var r=ja(e);return r.isst=e.read_shift(4),r}},255:{f:function(e,r){var t={};return t.dsst=e.read_shift(2),e.l+=r-2,t}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Ma},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Fa},353:{f:Da},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,r,t){var a=e.l+r,n=e.read_shift(2),s=e.read_shift(2);if(t.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Ba(e,s),c=[];a>e.l;)c.push(Va(e));return[s,n,i,c]}},431:{f:Fa},432:{},433:{},434:{},437:{},438:{f:function(e,r,t){var a=e.l,n="";try{e.l+=4;var s=(t.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:function(e){var r=e.read_shift(1);e.l++;var t=e.read_shift(2);return e.l+=2,[r,t]}(e);var i=e.read_shift(2);e.read_shift(2),Pa(e);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var l=e[e.l];if((n+=Ba(e,e.lens[o+1]-e.lens[o]-1)).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+r,{t:n}}catch(f){return e.l=a+r,{t:n}}}},439:{f:Fa},440:{f:function(e,r){var t=Ka(e);e.l+=16;var a=function(e,r){var t=e.l+r,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,c,o,l,f,h="";16&n&&(s=za(e,e.l)),128&n&&(i=za(e,e.l)),257==(257&n)&&(c=za(e,e.l)),1==(257&n)&&(o=Wa(e,e.l)),8&n&&(h=za(e,e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=Aa(e)),e.l=t;var u=i||c||o||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,r-24);return[t,a]}},441:{},442:{f:Va},443:{},444:{f:Pa},445:{},446:{},448:{f:Da},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:Da},512:{f:nn},513:{f:mn},515:{f:function(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5);var a=ja(e),n=qt(e);return a.val=n,a}},516:{f:function(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5),e.l;var a=ja(e);2==t.biff&&e.l++;var n=Va(e,e.l,t);return a.val=n,a}},517:{f:sn},519:{f:vn},520:{f:function(e){var r={};r.r=e.read_shift(2),r.c=e.read_shift(2),r.cnt=e.read_shift(2)-r.c;var t=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(r.level=7&a),32&a&&(r.hidden=!0),64&a&&(r.hpt=t/20),r}},523:{},545:{f:un},549:{f:tn},566:{},574:{f:function(e,r,t){return t&&t.biff>=2&&t.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var r=e.read_shift(2),t=e.read_shift(2),a=Ya(e);return{r:r,c:t,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,r,t){return[e.read_shift(2),Ha(e,0,t)]}},1084:{},1212:{f:function(e,r,t){var a=Ja(e);e.l++;var n=e.read_shift(1);return[ai(e,r-=8,t),n,a]}},2048:{f:function(e,r){e.read_shift(2);var t=Ka(e),a=e.read_shift((r-10)/2,"dbcs-cont");return[t,a=a.replace(R,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:rn},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Da},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var r={cxfs:0,crc:0};return r.cxfs=e.read_shift(2),r.crc=e.read_shift(4),r},r:12},2173:{f:function(e,r){e.l,e.l+=2;var t=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),n=[];a-- >0;)n.push(ws(e,e.l));return{ixfe:t,ext:n}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Fa,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,r,t){if(!(t.biff<8)){var a=e.read_shift(2),n=e.read_shift(2);return[Ba(e,a,t),Ba(e,n,t)]}e.l+=r},r:12},2197:{},2198:{f:function(e,r,t){var a=e.l+r;if(124226!==e.read_shift(4))if(t.cellStyles){var n,s=e.slice(e.l);e.l=a;try{n=tr(s,{type:"array"})}catch(c){return}var i=Ze(n,"theme/theme/theme1.xml",!0);if(i)return Ts(i,t)}else e.l=a},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:Da},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var r=function(e){var r=e.read_shift(2),t=e.read_shift(2);return e.l+=8,{type:r,flags:t}}(e);if(2211!=r.type)throw new Error("Invalid Future Record "+r.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Pa},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,r,t){var a={area:!1};if(5!=t.biff)return e.l+=r,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var r=e.read_shift(2),t=[];r-- >0;)t.push($a(e));return t}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:nn},1:{},2:{f:function(e){var r=ja(e);++e.l;var t=e.read_shift(2);return r.t="n",r.val=t,r}},3:{f:function(e){var r=ja(e);++e.l;var t=qt(e);return r.t="n",r.val=t,r}},4:{f:function(e,r,t){t.biffguess&&5==t.biff&&(t.biff=2);var a=ja(e);++e.l;var n=Ha(e,0,t);return a.t="str",a.val=n,a}},5:{f:sn},7:{f:function(e){var r=e.read_shift(1);return 0===r?(e.l++,""):e.read_shift(r,"sbcs-cont")}},8:{},9:{f:rn},11:{},22:{f:Pa},30:{f:an},31:{},32:{},33:{f:un},36:{},37:{f:tn},50:{f:function(e,r){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=r-13}},62:{},52:{},67:{},68:{f:Pa},69:{},86:{},126:{},127:{f:function(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n={fmt:r,env:t,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,r,t){var a=e.l+r,n=ja(e),s=e.read_shift(2),i=Ba(e,s,t);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:ni},521:{f:rn},536:{f:fn},547:{f:on},561:{},579:{},1030:{f:ni},1033:{f:rn},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function _c(e,r,t,a){var n=r;if(!isNaN(n)){var s=a||(t||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&nt(t)&&e.push(t)}}function Cc(e,r){var t=r||{},a=t.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,c=s&&s.index||e.length,o=je(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<o.length;++i){var m=o[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"!=g){if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(c=0;c<v.length;++c){var b=v[c].trim();if(b.match(/<t[dh]/i)){for(var T=b,E=0;"<"==T.charAt(0)&&(E=T.indexOf(">"))>-1;)T=T.slice(E+1);for(var w=0;w<p.length;++w){var A=p[w];A.s.c==f&&A.s.r<l&&l<=A.e.r&&(f=A.e.c+1,w=-1)}var S=fr(b.slice(0,b.indexOf(">")));u=S.colspan?+S.colspan:1,((h=+S.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var k=S.t||S["data-t"]||"";if(T.length)if(T=Cr(T),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),T.length){var y={t:"s",v:T};t.raw||!T.trim().length||"s"==k||("TRUE"===T?y={t:"b",v:!0}:"FALSE"===T?y={t:"b",v:!1}:isNaN(ze(T))?isNaN($e(T).getDate())||(y={t:"d",v:Be(T)},t.cellDates||(y={t:"n",v:Re(y.v)}),y.z=t.dateNF||H[14]):y={t:"n",v:ze(T)}),t.dense?(a[l]||(a[l]=[]),a[l][f]=y):a[Ot({r:l,c:f})]=y,f+=u}else f+=u;else f+=u}}}}else{if(++l,t.sheetRows&&t.sheetRows<=l){--l;break}f=0}}return a["!ref"]=It(d),p.length&&(a["!merges"]=p),a}function xc(e,r,t,a){for(var n=e["!merges"]||[],s=[],i=r.s.c;i<=r.e.c;++i){for(var c=0,o=0,l=0;l<n.length;++l)if(!(n[l].s.r>t||n[l].s.c>i||n[l].e.r<t||n[l].e.c<i)){if(n[l].s.r<t||n[l].s.c<i){c=-1;break}c=n[l].e.r-n[l].s.r+1,o=n[l].e.c-n[l].s.c+1;break}if(!(c<0)){var f=Ot({r:t,c:i}),h=a.dense?(e[t]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||vr(h.w||(Ft(h),h.w)||""))||"",d={};c>1&&(d.rowspan=c),o>1&&(d.colspan=o),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(Dr("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}function Oc(e,r,t){var a=t||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?xt(a.origin):a.origin;n=i.r,s=i.c}var c=r.getElementsByTagName("tr"),o=Math.min(a.sheetRows||1e7,c.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=Rt(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,T=0;for(e["!cols"]||(e["!cols"]=[]);p<c.length&&m<o;++p){var E=c[p];if(Ic(E)){if(a.display)continue;d[m]={hidden:!0}}var w=E.children;for(g=v=0;g<w.length;++g){var A=w[g];if(!a.display||!Ic(A)){var S=A.hasAttribute("data-v")?A.getAttribute("data-v"):A.hasAttribute("v")?A.getAttribute("v"):Cr(A.innerHTML),k=A.getAttribute("data-z")||A.getAttribute("z");for(u=0;u<h.length;++u){var y=h[u];y.s.c==v+s&&y.s.r<m+n&&m+n<=y.e.r&&(v=y.e.c+1-s,u=-1)}T=+A.getAttribute("colspan")||1,((b=+A.getAttribute("rowspan")||1)>1||T>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(b||1)-1,c:v+s+(T||1)-1}});var _={t:"s",v:S},C=A.getAttribute("data-t")||A.getAttribute("t")||"";null!=S&&(0==S.length?_.t=C||"z":a.raw||0==S.trim().length||"s"==C||("TRUE"===S?_={t:"b",v:!0}:"FALSE"===S?_={t:"b",v:!1}:isNaN(ze(S))?isNaN($e(S).getDate())||(_={t:"d",v:Be(S)},a.cellDates||(_={t:"n",v:Re(_.v)}),_.z=a.dateNF||H[14]):_={t:"n",v:ze(S)})),void 0===_.z&&null!=k&&(_.z=k);var x="",O=A.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(x=O[R].getAttribute("href")).charAt(0));++R);x&&"#"!=x.charAt(0)&&(_.l={Target:x}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=_):e[Ot({c:v+s,r:m+n})]=_,l.e.c<v+s&&(l.e.c=v+s),v+=T}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=It(l),m>=o&&(e["!fullref"]=It((l.e.r=c.length-p+m-1+n,l))),e}function Rc(e,r){return Oc((r||{}).dense?[]:{},e,r)}function Ic(e){var r="",t=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return t&&(r=t(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}var Nc={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Dc(e,r){var t,a,n,s,i,c,o,l,f=r||{},h=Fr(e),u=[],d={name:""},p="",m=0,g={},v=[],b=f.dense?[]:{},T={value:""},E="",w=0,A=[],S=-1,k=-1,y={s:{r:1e6,c:1e7},e:{r:0,c:0}},_=0,C={},x=[],O={},R=[],I=1,N=1,D=[],F={Names:[]},P={},M=["",""],L=[],U={},B="",V=0,H=!1,W=!1,z=0;for(Pr.lastIndex=0,h=h.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");i=Pr.exec(h);)switch(i[3]=i[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===i[1]?(y.e.c>=y.s.c&&y.e.r>=y.s.r?b["!ref"]=It(y):b["!ref"]="A1:A1",f.sheetRows>0&&f.sheetRows<=y.e.r&&(b["!fullref"]=b["!ref"],y.e.r=f.sheetRows-1,b["!ref"]=It(y)),x.length&&(b["!merges"]=x),R.length&&(b["!rows"]=R),n.name=n["名称"]||n.name,"undefined"!=typeof JSON&&JSON.stringify(n),v.push(n.name),g[n.name]=b,W=!1):"/"!==i[0].charAt(i[0].length-2)&&(n=fr(i[0],!1),S=k=-1,y.s.r=y.s.c=1e7,y.e.r=y.e.c=0,b=f.dense?[]:{},x=[],R=[],W=!0);break;case"table-row-group":"/"===i[1]?--_:++_;break;case"table-row":case"行":if("/"===i[1]){S+=I,I=1;break}if((s=fr(i[0],!1))["行号"]?S=s["行号"]-1:-1==S&&(S=0),(I=+s["number-rows-repeated"]||1)<10)for(z=0;z<I;++z)_>0&&(R[S+z]={level:_});k=-1;break;case"covered-table-cell":"/"!==i[1]&&++k,f.sheetStubs&&(f.dense?(b[S]||(b[S]=[]),b[S][k]={t:"z"}):b[Ot({r:S,c:k})]={t:"z"}),E="",A=[];break;case"table-cell":case"数据":if("/"===i[0].charAt(i[0].length-2))++k,T=fr(i[0],!1),N=parseInt(T["number-columns-repeated"]||"1",10),c={t:"z",v:null},T.formula&&0!=f.cellFormula&&(c.f=di(pr(T.formula))),"string"==(T["数据类型"]||T["value-type"])&&(c.t="s",c.v=pr(T["string-value"]||""),f.dense?(b[S]||(b[S]=[]),b[S][k]=c):b[Ot({r:S,c:k})]=c),k+=N-1;else if("/"!==i[1]){E="",w=0,A=[],N=1;var G=I?S+I-1:S;if(++k>y.e.c&&(y.e.c=k),k<y.s.c&&(y.s.c=k),S<y.s.r&&(y.s.r=S),G>y.e.r&&(y.e.r=G),L=[],U={},c={t:(T=fr(i[0],!1))["数据类型"]||T["value-type"],v:null},f.cellFormula)if(T.formula&&(T.formula=pr(T.formula)),T["number-matrix-columns-spanned"]&&T["number-matrix-rows-spanned"]&&(O={s:{r:S,c:k},e:{r:S+(parseInt(T["number-matrix-rows-spanned"],10)||0)-1,c:k+(parseInt(T["number-matrix-columns-spanned"],10)||0)-1}},c.F=It(O),D.push([O,c.F])),T.formula)c.f=di(T.formula);else for(z=0;z<D.length;++z)S>=D[z][0].s.r&&S<=D[z][0].e.r&&k>=D[z][0].s.c&&k<=D[z][0].e.c&&(c.F=D[z][1]);switch((T["number-columns-spanned"]||T["number-rows-spanned"])&&(O={s:{r:S,c:k},e:{r:S+(parseInt(T["number-rows-spanned"],10)||0)-1,c:k+(parseInt(T["number-columns-spanned"],10)||0)-1}},x.push(O)),T["number-columns-repeated"]&&(N=parseInt(T["number-columns-repeated"],10)),c.t){case"boolean":c.t="b",c.v=Tr(T["boolean-value"]);break;case"float":case"percentage":case"currency":c.t="n",c.v=parseFloat(T.value);break;case"date":c.t="d",c.v=Be(T["date-value"]),f.cellDates||(c.t="n",c.v=Re(c.v)),c.z="m/d/yy";break;case"time":c.t="n",c.v=Pe(T["time-value"])/86400,f.cellDates&&(c.t="d",c.v=Fe(c.v)),c.z="HH:MM:SS";break;case"number":c.t="n",c.v=parseFloat(T["数据数值"]);break;default:if("string"!==c.t&&"text"!==c.t&&c.t)throw new Error("Unsupported value type "+c.t);c.t="s",null!=T["string-value"]&&(E=pr(T["string-value"]),A=[])}}else{if(H=!1,"s"===c.t&&(c.v=E||"",A.length&&(c.R=A),H=0==w),P.Target&&(c.l=P),L.length>0&&(c.c=L,L=[]),E&&!1!==f.cellText&&(c.w=E),H&&(c.t="z",delete c.v),(!H||f.sheetStubs)&&!(f.sheetRows&&f.sheetRows<=S))for(var $=0;$<I;++$){if(N=parseInt(T["number-columns-repeated"]||"1",10),f.dense)for(b[S+$]||(b[S+$]=[]),b[S+$][k]=0==$?c:He(c);--N>0;)b[S+$][k+N]=He(c);else for(b[Ot({r:S+$,c:k})]=c;--N>0;)b[Ot({r:S+$,c:k+N})]=He(c);y.e.c<=k&&(y.e.c=k)}k+=(N=parseInt(T["number-columns-repeated"]||"1",10))-1,N=0,c={},E="",A=[]}P={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===i[1]){if((t=u.pop())[0]!==i[3])throw"Bad state: "+t}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!0]);break;case"annotation":if("/"===i[1]){if((t=u.pop())[0]!==i[3])throw"Bad state: "+t;U.t=E,A.length&&(U.R=A),U.a=B,L.push(U)}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);B="",V=0,E="",w=0,A=[];break;case"creator":"/"===i[1]?B=h.slice(V,i.index):V=i.index+i[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===i[1]){if((t=u.pop())[0]!==i[3])throw"Bad state: "+t}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);E="",w=0,A=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===i[1]){if(C[d.name]=p,(t=u.pop())[0]!==i[3])throw"Bad state: "+t}else"/"!==i[0].charAt(i[0].length-2)&&(p="",d=fr(i[0],!1),u.push([i[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(u[u.length-1][0]){case"time-style":case"date-style":a=fr(i[0],!1),p+=Nc[i[3]]["long"===a.style?1:0]}break;case"text":if("/>"===i[0].slice(-2))break;if("/"===i[1])switch(u[u.length-1][0]){case"number-style":case"date-style":case"time-style":p+=h.slice(m,i.index)}else m=i.index+i[0].length;break;case"named-range":M=pi((a=fr(i[0],!1))["cell-range-address"]);var j={Name:a.name,Ref:M[0]+"!"+M[1]};W&&(j.Sheet=v.length),F.Names.push(j);break;case"p":case"文本串":if(["master-styles"].indexOf(u[u.length-1][0])>-1)break;if("/"!==i[1]||T&&T["string-value"])fr(i[0],!1),w=i.index+i[0].length;else{var X=(o=h.slice(w,i.index),l=void 0,l=o.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,r){return Array(parseInt(r,10)+1).join(" ")})).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n"),[pr(l.replace(/<[^>]*>/g,""))]);E=(E.length>0?E+"\n":"")+X[0]}break;case"database-range":if("/"===i[1])break;try{g[(M=pi(fr(i[0])["target-range-address"]))[0]]["!autofilter"]={ref:M[1]}}catch(K){}break;case"a":if("/"!==i[1]){if(!(P=fr(i[0],!1)).href)break;P.Target=pr(P.href),delete P.href,"#"==P.Target.charAt(0)&&P.Target.indexOf(".")>-1?(M=pi(P.Target.slice(1)),P.Target="#"+M[0]+"!"+M[1]):P.Target.match(/^\.\.[\\\/]/)&&(P.Target=P.Target.slice(3))}break;default:switch(i[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(f.WTF)throw new Error(i)}}var Y={Sheets:g,SheetNames:v,Workbook:F};return f.bookSheets&&delete Y.Sheets,Y}function Fc(e,r){r=r||{},Ke(e,"META-INF/manifest.xml")&&function(e,r){for(var t,a,n=Fr(e);t=Pr.exec(n);)switch(t[3]){case"manifest":break;case"file-entry":if("/"==(a=fr(t[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw t}}(qe(e,"META-INF/manifest.xml"),r);var t=Ze(e,"content.xml");if(!t)throw new Error("Missing content.xml in ODS / UOF file");var a=Dc(kr(t),r);return Ke(e,"meta.xml")&&(a.Props=ma(qe(e,"meta.xml"))),a}function Pc(e,r){return Dc(e,r)}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Mc(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Lc(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):kr(C(e))}function Uc(e){var r=e.reduce((function(e,r){return e+r.length}),0),t=new Uint8Array(r),a=0;return e.forEach((function(e){t.set(e,a),a+=e.length})),t}function Bc(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>>24}function Vc(e,r){var t=r?r[0]:0,a=127&e[t];e:if(e[t++]>=128){if(a|=(127&e[t])<<7,e[t++]<128)break e;if(a|=(127&e[t])<<14,e[t++]<128)break e;if(a|=(127&e[t])<<21,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,28),++t,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,35),++t,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,42),++t,e[t++]<128)break e}return r&&(r[0]=t),a}function Hc(e){var r=0,t=127&e[r];e:if(e[r++]>=128){if(t|=(127&e[r])<<7,e[r++]<128)break e;if(t|=(127&e[r])<<14,e[r++]<128)break e;if(t|=(127&e[r])<<21,e[r++]<128)break e;t|=(127&e[r])<<28}return t}function Wc(e){for(var r=[],t=[0];t[0]<e.length;){var a,n=t[0],s=Vc(e,t),i=7&s,c=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var o=t[0];e[t[0]++]>=128;);a=e.slice(o,t[0]);break;case 5:c=4,a=e.slice(t[0],t[0]+c),t[0]+=c;break;case 1:c=8,a=e.slice(t[0],t[0]+c),t[0]+=c;break;case 2:c=Vc(e,t),a=e.slice(t[0],t[0]+c),t[0]+=c;break;default:throw new Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==r[s]?r[s]=[l]:r[s].push(l)}return r}function zc(e,r){return(null==e?void 0:e.map((function(e){return r(e.data)})))||[]}function Gc(e,r){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var t=[0],a=Vc(r,t),n=[];t[0]<r.length;){var s=3&r[t[0]];if(0!=s){var i=0,c=0;if(1==s?(c=4+(r[t[0]]>>2&7),i=(224&r[t[0]++])<<3,i|=r[t[0]++]):(c=1+(r[t[0]++]>>2),2==s?(i=r[t[0]]|r[t[0]+1]<<8,t[0]+=2):(i=(r[t[0]]|r[t[0]+1]<<8|r[t[0]+2]<<16|r[t[0]+3]<<24)>>>0,t[0]+=4)),n=[Uc(n)],0==i)throw new Error("Invalid offset 0");if(i>n[0].length)throw new Error("Invalid offset beyond length");if(c>=i)for(n.push(n[0].slice(-i)),c-=i;c>=n[n.length-1].length;)n.push(n[n.length-1]),c-=n[n.length-1].length;n.push(n[0].slice(-i,-i+c))}else{var o=r[t[0]++]>>2;if(o<60)++o;else{var l=o-59;o=r[t[0]],l>1&&(o|=r[t[0]+1]<<8),l>2&&(o|=r[t[0]+2]<<16),l>3&&(o|=r[t[0]+3]<<24),o>>>=0,o++,t[0]+=l}n.push(r.slice(t[0],t[0]+o)),t[0]+=o}}var f=Uc(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function $c(e,r,t){var a,n=Mc(e),s=n.getUint32(8,!0),i=12,c=-1,o=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,r){for(var t=(127&e[r+15])<<7|e[r+14]>>1,a=1&e[r+14],n=r+13;n>=r;--n)a=256*a+e[n];return(128&e[r+15]?-a:a)*Math.pow(10,t-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(o=n.getUint32(i,!0),i+=4),16&s&&(c=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:r[o]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(!(c>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));a={t:"s",v:t[c]};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}function jc(e,r,t){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,r,t,a){var n,s=Mc(e),i=s.getUint32(4,!0),c=(a>1?12:8)+4*Bc(i&(a>1?3470:398)),o=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(o=s.getUint32(c,!0),c+=4),c+=4*Bc(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(c,!0),c+=4),32&i&&(f=s.getFloat64(c,!0),c+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(c,!0)),c+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:r[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(o>-1)n={t:"s",v:t[o]};else if(l>-1)n={t:"s",v:r[l]};else{if(isNaN(f))throw new Error("Unsupported cell type ".concat(e.slice(0,4)));n={t:"n",v:f}}break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,r,t,e[0]);case 5:return $c(e,r,t);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Xc(e){return Vc(Wc(e)[1][0].data)}function Yc(e,r){var t=Wc(r.data),a=Hc(t[1][0].data),n=t[3],s=[];return(n||[]).forEach((function(r){var t=Wc(r.data),n=Hc(t[1][0].data)>>>0;switch(a){case 1:s[n]=Lc(t[3][0].data);break;case 8:var i=Wc(e[Xc(t[9][0].data)][0].data),c=e[Xc(i[1][0].data)][0],o=Hc(c.meta[1][0].data);if(2001!=o)throw new Error("2000 unexpected reference to ".concat(o));var l=Wc(c.data);s[n]=l[3].map((function(e){return Lc(e.data)})).join("")}})),s}function Kc(e,r){var t,a=Wc(r.data),n=(null==(t=null==a?void 0:a[7])?void 0:t[0])?Hc(a[7][0].data)>>>0>0?1:0:-1,s=zc(a[5],(function(e){return function(e,r){var t,a,n,s,i,c,o,l,f,h,u,d,p,m,g,v,b=Wc(e),T=Hc(b[1][0].data)>>>0,E=Hc(b[2][0].data)>>>0,w=(null==(a=null==(t=b[8])?void 0:t[0])?void 0:a.data)&&Hc(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=r)g=null==(c=null==(i=b[7])?void 0:i[0])?void 0:c.data,v=null==(l=null==(o=b[6])?void 0:o[0])?void 0:l.data;else{if(!(null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)||1==r)throw"NUMBERS Tile missing ".concat(r," cell storage");g=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data}for(var A=w?4:1,S=Mc(g),k=[],y=0;y<g.length/2;++y){var _=S.getUint16(2*y,!0);_<65535&&k.push([y,_])}if(k.length!=E)throw"Expected ".concat(E," cells, found ").concat(k.length);var C=[];for(y=0;y<k.length-1;++y)C[k[y][0]]=v.subarray(k[y][1]*A,k[y+1][1]*A);return k.length>=1&&(C[k[k.length-1][0]]=v.subarray(k[k.length-1][1]*A)),{R:T,cells:C}}(e,n)}));return{nrows:Hc(a[4][0].data)>>>0,data:s.reduce((function(e,r){return e[r.R]||(e[r.R]=[]),r.cells.forEach((function(t,a){if(e[r.R][a])throw new Error("Duplicate cell r=".concat(r.R," c=").concat(a));e[r.R][a]=t})),e}),[])}}function Jc(e,r){var t={"!ref":"A1"},a=e[Xc(Wc(r.data)[2][0].data)],n=Hc(a[0].meta[1][0].data);if(6001!=n)throw new Error("6000 unexpected reference to ".concat(n));return function(e,r,t){var a,n=Wc(r.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Hc(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(Hc(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));t["!ref"]=It(s);var i=Wc(n[4][0].data),c=Yc(e,e[Xc(i[4][0].data)][0]),o=(null==(a=i[17])?void 0:a[0])?Yc(e,e[Xc(i[17][0].data)][0]):[],l=Wc(i[3][0].data),f=0;l[1].forEach((function(r){var a=Wc(r.data),n=e[Xc(a[2][0].data)][0],s=Hc(n.meta[1][0].data);if(6002!=s)throw new Error("6001 unexpected reference to ".concat(s));var i=Kc(0,n);i.data.forEach((function(e,r){e.forEach((function(e,a){var n=Ot({r:f+r,c:a}),s=jc(e,c,o);s&&(t[n]=s)}))})),f+=i.nrows}))}(e,a[0],t),t}function qc(e,r){var t={SheetNames:[],Sheets:{}};if(zc(Wc(r.data)[1],Xc).forEach((function(r){e[r].forEach((function(r){if(2==Hc(r.meta[1][0].data)){var a=function(e,r){var t,a=Wc(r.data),n={name:(null==(t=a[1])?void 0:t[0])?Lc(a[1][0].data):"",sheets:[]};return zc(a[2],Xc).forEach((function(r){e[r].forEach((function(r){6e3==Hc(r.meta[1][0].data)&&n.sheets.push(Jc(e,r))}))})),n}(e,r);a.sheets.forEach((function(e,r){vo(t,e,0==r?a.name:a.name+"_"+r,!0)}))}}))})),0==t.SheetNames.length)throw new Error("Empty NUMBERS file");return t}function Zc(e){var r,t,a,s,i={},c=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)){var r,t;try{r=function(e){for(var r=[],t=0;t<e.length;){var a=e[t++],n=e[t]|e[t+1]<<8|e[t+2]<<16;t+=3,r.push(Gc(a,e.slice(t,t+n))),t+=n}if(t!==e.length)throw new Error("data is not a valid framed stream!");return Uc(r)}(e.content)}catch(a){return n("log","at node_modules/xlsx/xlsx.mjs:22746","?? "+e.content.length+" "+(a.message||a))}try{t=function(e){for(var r,t=[],a=[0];a[0]<e.length;){var n=Vc(e,a),s=Wc(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:Hc(s[1][0].data),messages:[]};s[2].forEach((function(r){var t=Wc(r.data),n=Hc(t[3][0].data);i.messages.push({meta:t,data:e.slice(a[0],a[0]+n)}),a[0]+=n})),(null==(r=s[3])?void 0:r[0])&&(i.merge=Hc(s[3][0].data)>>>0>0),t.push(i)}return t}(r)}catch(a){return n("log","at node_modules/xlsx/xlsx.mjs:22752","## "+(a.message||a))}t.forEach((function(e){i[e.id]=e.messages,c.push(e.id)}))}})),!c.length)throw new Error("File has no messages");var o=(null==(s=null==(a=null==(t=null==(r=null==i?void 0:i[1])?void 0:r[0])?void 0:t.meta)?void 0:a[1])?void 0:s[0].data)&&1==Hc(i[1][0].meta[1][0].data)&&i[1][0];if(o||c.forEach((function(e){i[e].forEach((function(e){if(1==Hc(e.meta[1][0].data)>>>0){if(o)throw new Error("Document has multiple roots");o=e}}))})),!o)throw new Error("Cannot find Document root");return qc(i,o)}function Qc(e){var r;(r=[["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]],function(e){for(var t=0;t!=r.length;++t){var a=r[t];void 0===e[a[0]]&&(e[a[0]]=a[1]),"n"===a[2]&&(e[a[0]]=Number(e[a[0]]))}})(e)}function eo(e,r,t,a,n,s,i,c,o,l,f,h){try{s[a]=ua(Ze(e,t,!0),r);var u,d=qe(e,r);switch(c){case"sheet":u=Ki(d,r,n,o,s[a],l,f,h);break;case"chart":if(!(u=Ji(d,r,n,o,s[a],l))||!u["!drawel"])break;var p=ar(u["!drawel"].Target,r),m=ha(p),g=function(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}(Ze(e,p,!0),ua(Ze(e,m,!0),p)),v=ar(g,p),b=ha(v);u=Li(Ze(e,v,!0),0,0,ua(Ze(e,b,!0),v),0,u);break;case"macro":E=r,s[a],E.slice(-4),u={"!type":"macro"};break;case"dialog":u=function(e,r,t,a,n,s,i,c){return r.slice(-4),{"!type":"dialog"}}(0,r,0,0,s[a]);break;default:throw new Error("Unrecognized sheet type "+c)}i[a]=u;var T=[];s&&s[a]&&Ce(s[a]).forEach((function(t){var n="";if(s[a][t].Type==fa.CMNT){n=ar(s[a][t].Target,r);var i=Qi(qe(e,n,!0),n,o);if(!i||!i.length)return;As(u,i,!1)}s[a][t].Type==fa.TCMNT&&(n=ar(s[a][t].Target,r),T=T.concat(function(e,r){var t=[],a=!1,n={},s=0;return e.replace(cr,(function(i,c){var o=fr(i);switch(hr(o[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":n={author:o.personId,guid:o.id,ref:o.ref,T:1};break;case"</threadedComment>":null!=n.t&&t.push(n);break;case"<text>":case"<text":s=c+i.length;break;case"</text>":n.t=e.slice(s,c).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":a=!0;break;case"</mentions>":case"</ext>":a=!1;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+o[0]+" in threaded comments")}return i})),t}(qe(e,n,!0),o)))})),T&&T.length&&As(u,T,!0,o.people||[])}catch(w){if(o.WTF)throw w}var E}function ro(e){return"/"==e.charAt(0)?e.slice(1):e}function to(e,r){if(Ae(),Qc(r=r||{}),Ke(e,"META-INF/manifest.xml"))return Fc(e,r);if(Ke(e,"objectdata.xml"))return Fc(e,r);if(Ke(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return Zc(e);var t=_e.utils.cfb_new();return er(e).forEach((function(r){rr(t,r,Qe(e,r))})),Zc(t)}if(!Ke(e,"[Content_Types].xml")){if(Ke(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Ke(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a,s,i=er(e),c=function(e){var r={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return r;var t={};if((e.match(cr)||[]).forEach((function(e){var a=fr(e);switch(a[0].replace(or,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension]=a.ContentType;break;case"<Override":void 0!==r[la[a.ContentType]]&&r[la[a.ContentType]].push(a.PartName)}})),r.xmlns!==Mr)throw new Error("Unknown Namespace: "+r.xmlns);return r.calcchain=r.calcchains.length>0?r.calcchains[0]:"",r.sst=r.strs.length>0?r.strs[0]:"",r.style=r.styles.length>0?r.styles[0]:"",r.defaults=t,delete r.calcchains,r}(Ze(e,"[Content_Types].xml")),o=!1;if(0===c.workbooks.length&&qe(e,s="xl/workbook.xml",!0)&&c.workbooks.push(s),0===c.workbooks.length){if(!qe(e,s="xl/workbook.bin",!0))throw new Error("Could not find workbook");c.workbooks.push(s),o=!0}"bin"==c.workbooks[0].slice(-3)&&(o=!0);var l={},f={};if(!r.bookSheets&&!r.bookProps){if(mi=[],c.sst)try{mi=Zi(qe(e,ro(c.sst)),c.sst,r)}catch(I){if(r.WTF)throw I}r.cellStyles&&c.themes.length&&(l=function(e,r,t){return Ts(e,t)}(Ze(e,c.themes[0].replace(/^\//,""),!0)||"",c.themes[0],r)),c.style&&(f=qi(qe(e,ro(c.style)),c.style,l,r))}c.links.map((function(t){try{ua(Ze(e,ha(ro(t))),t);return rc(qe(e,ro(t)),0,t,r)}catch(I){}}));var h=Yi(qe(e,ro(c.workbooks[0])),c.workbooks[0],r),u={},d="";c.coreprops.length&&((d=qe(e,ro(c.coreprops[0]),!0))&&(u=ma(d)),0!==c.extprops.length&&(d=qe(e,ro(c.extprops[0]),!0))&&function(e,r,t){var a={};r||(r={}),e=kr(e),ga.forEach((function(t){var n=(e.match(_r(t[0]))||[])[1];switch(t[2]){case"string":n&&(r[t[1]]=pr(n));break;case"bool":r[t[1]]="true"===n;break;case"raw":var s=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));s&&s.length>0&&(a[t[1]]=s[1])}})),a.HeadingPairs&&a.TitlesOfParts&&va(a.HeadingPairs,a.TitlesOfParts,r,t)}(d,u,r));var p={};r.bookSheets&&!r.bookProps||0!==c.custprops.length&&(d=Ze(e,ro(c.custprops[0]),!0))&&(p=function(e,r){var t={},a="",s=e.match(ba);if(s)for(var i=0;i!=s.length;++i){var c=s[i],o=fr(c);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":a=pr(o.name);break;case"</property>":a=null;break;default:if(0===c.indexOf("<vt:")){var l=c.split(">"),f=l[0].slice(4),h=l[1];switch(f){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":t[a]=pr(h);break;case"bool":t[a]=Tr(h);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":t[a]=parseInt(h,10);break;case"r4":case"r8":case"decimal":t[a]=parseFloat(h);break;case"filetime":case"date":t[a]=Be(h);break;default:if("/"==f.slice(-1))break;r.WTF&&"undefined"!=typeof console&&n("warn","at node_modules/xlsx/xlsx.mjs:5669","Unexpected",c,f,l)}}else if("</"===c.slice(0,2));else if(r.WTF)throw new Error(c)}}return t}(d,r));var m={};if((r.bookSheets||r.bookProps)&&(h.Sheets?a=h.Sheets.map((function(e){return e.name})):u.Worksheets&&u.SheetNames.length>0&&(a=u.SheetNames),r.bookProps&&(m.Props=u,m.Custprops=p),r.bookSheets&&void 0!==a&&(m.SheetNames=a),r.bookSheets?m.SheetNames:r.bookProps))return m;a={};var g={};r.bookDeps&&c.calcchain&&(g=ec(qe(e,ro(c.calcchain)),c.calcchain));var v,b,T=0,E={},w=h.Sheets;u.Worksheets=w.length,u.SheetNames=[];for(var A=0;A!=w.length;++A)u.SheetNames[A]=w[A].name;var S=o?"bin":"xml",k=c.workbooks[0].lastIndexOf("/"),y=(c.workbooks[0].slice(0,k+1)+"_rels/"+c.workbooks[0].slice(k+1)+".rels").replace(/^\//,"");Ke(e,y)||(y="xl/_rels/workbook."+S+".rels");var _=ua(Ze(e,y,!0),y.replace(/_rels.*/,"s5s"));(c.metadata||[]).length>=1&&(r.xlmeta=tc(qe(e,ro(c.metadata[0])),c.metadata[0],r)),(c.people||[]).length>=1&&(r.people=function(e,r){var t=[],a=!1;return e.replace(cr,(function(e){var n=fr(e);switch(hr(n[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":t.push({name:n.displayname,id:n.id});break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),t}(qe(e,ro(c.people[0])),r)),_&&(_=function(e,r){if(!e)return 0;try{e=r.map((function(r){return r.id||(r.id=r.strRelID),[r.name,e["!id"][r.id].Target,(t=e["!id"][r.id].Type,fa.WS.indexOf(t)>-1?"sheet":t==fa.CS?"chart":t==fa.DS?"dialog":t==fa.MS?"macro":t&&t.length?t:"sheet")];var t}))}catch(I){return null}return e&&0!==e.length?e:null}(_,h.Sheets));var C=qe(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(T=0;T!=u.Worksheets;++T){var x="sheet";if(_&&_[T]?(v="xl/"+_[T][1].replace(/[\/]?xl\//,""),Ke(e,v)||(v=_[T][1]),Ke(e,v)||(v=y.replace(/_rels\/.*$/,"")+_[T][1]),x=_[T][2]):v=(v="xl/worksheets/sheet"+(T+1-C)+"."+S).replace(/sheet0\./,"sheet."),b=v.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),r&&null!=r.sheets)switch(typeof r.sheets){case"number":if(T!=r.sheets)continue e;break;case"string":if(u.SheetNames[T].toLowerCase()!=r.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(r.sheets)){for(var O=!1,R=0;R!=r.sheets.length;++R)"number"==typeof r.sheets[R]&&r.sheets[R]==T&&(O=1),"string"==typeof r.sheets[R]&&r.sheets[R].toLowerCase()==u.SheetNames[T].toLowerCase()&&(O=1);if(!O)continue e}}eo(e,v,b,u.SheetNames[T],T,E,a,x,r,h,l,f)}return m={Directory:c,Workbook:h,Props:u,Custprops:p,Deps:g,Sheets:a,SheetNames:u.SheetNames,Strings:mi,Styles:f,Themes:l,SSF:He(H)},r&&r.bookFiles&&(e.files?(m.keys=i,m.files=e.files):(m.keys=[],m.files={},e.FullPaths.forEach((function(r,t){r=r.replace(/^Root Entry[\/]/,""),m.keys.push(r),m.files[r]=e.FileIndex[t]})))),r&&r.bookVBA&&(c.vba.length>0?m.vbaraw=qe(e,ro(c.vba[0]),!0):c.defaults&&"application/vnd.ms-office.vbaProject"===c.defaults.bin&&(m.vbaraw=qe(e,"xl/vbaProject.bin",!0))),m}function ao(e,r){var t,a,n=r||{},s="Workbook",i=_e.find(e,s);try{if(s="/!DataSpaces/Version",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(t=i.content,(a={}).id=t.read_shift(0,"lpp4"),a.R=Pn(t,4),a.U=Pn(t,4),a.W=Pn(t,4),s="/!DataSpaces/DataSpaceMap",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var c=function(e){var r=[];e.l+=4;for(var t=e.read_shift(4);t-- >0;)r.push(Mn(e));return r}(i.content);if(1!==c.length||1!==c[0].comps.length||0!==c[0].comps[0].t||"StrongEncryptionDataSpace"!==c[0].name||"EncryptedPackage"!==c[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var o=function(e){var r=[];e.l+=4;for(var t=e.read_shift(4);t-- >0;)r.push(e.read_shift(0,"lpp4"));return r}(i.content);if(1!=o.length||"StrongEncryptionTransform"!=o[0])throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);Ln(i.content)}catch(f){}if(s="/EncryptionInfo",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var l=function(e){var r=Pn(e);switch(r.Minor){case 2:return[r.Minor,Vn(e)];case 3:return[r.Minor,Hn()];case 4:return[r.Minor,Wn(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+r.Minor)}(i.content);if(s="/EncryptedPackage",!(i=_e.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],i.content,n.password||"",n);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],i.content,n.password||"",n);throw new Error("File is password-protected")}function no(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=w(e.slice(0,12));break;case"binary":t=e;break;default:throw new Error("Unrecognized type "+(r&&r.type||"undefined"))}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function so(e,r){var t=0;e:for(;t<e.length;)switch(e.charCodeAt(t)){case 10:case 13:case 32:++t;break;case 60:return pc(e.slice(t),r);default:break e}return Sn.to_workbook(e,r)}function io(e,r,t,a){return a?(t.type="string",Sn.to_workbook(e,t)):Sn.to_workbook(r,t)}function co(e,r){u();var t=r||{};if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return co(new Uint8Array(e),((t=He(t)).type="array",t));"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&!t.type&&(t.type="undefined"!=typeof Deno?"buffer":"array");var a,n=e,s=!1;if(t.cellStyles&&(t.cellNF=!0,t.sheetStubs=!0),gi={},t.dateNF&&(gi.dateNF=t.dateNF),t.type||(t.type=A&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==t.type&&(t.type=A?"buffer":"binary",n=function(e){if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var r=File(e);r.open("r"),r.encoding="binary";var t=r.read();return r.close(),t}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}(e),"undefined"==typeof Uint8Array||A||(t.type="array")),"string"==t.type&&(s=!0,t.type="binary",t.codepage=65001,n=function(e){return e.match(/[^\x00-\x7F]/)?yr(e):e}(e)),"array"==t.type&&"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return(t=He(t)).type="array",co(x(n),t)}switch((a=no(n,t))[0]){case 208:if(207===a[1]&&17===a[2]&&224===a[3]&&161===a[4]&&177===a[5]&&26===a[6]&&225===a[7])return function(e,r){return _e.find(e,"EncryptedPackage")?ao(e,r):Sc(e,r)}(_e.read(n,t),t);break;case 9:if(a[1]<=8)return Sc(n,t);break;case 60:return pc(n,t);case 73:if(73===a[1]&&42===a[2]&&0===a[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===a[1])return function(e,r){var t=r||{},a=!!t.WTF;t.WTF=!0;try{var n=En.to_workbook(e,t);return t.WTF=a,n}catch(s){if(t.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return Sn.to_workbook(e,r)}}(n,t);break;case 84:if(65===a[1]&&66===a[2]&&76===a[3])return wn.to_workbook(n,t);break;case 80:return 75===a[1]&&a[2]<9&&a[3]<9?function(e,r){var t=e,a=r||{};return a.type||(a.type=A&&Buffer.isBuffer(e)?"buffer":"base64"),to(tr(t,a),a)}(n,t):io(e,n,t,s);case 239:return 60===a[3]?pc(n,t):io(e,n,t,s);case 255:if(254===a[1])return function(e,r){var t=e;return"base64"==r.type&&(t=w(t)),t=m.utils.decode(1200,t.slice(2),"str"),r.type="binary",so(t,r)}(n,t);if(0===a[1]&&2===a[2]&&0===a[3])return kn.to_workbook(n,t);break;case 0:if(0===a[1]){if(a[2]>=2&&0===a[3])return kn.to_workbook(n,t);if(0===a[2]&&(8===a[3]||9===a[3]))return kn.to_workbook(n,t)}break;case 3:case 131:case 139:case 140:return Tn.to_workbook(n,t);case 123:if(92===a[1]&&114===a[2]&&116===a[3])return Xn.to_workbook(n,t);break;case 10:case 13:case 32:return function(e,r){var t="",a=no(e,r);switch(r.type){case"base64":t=w(e);break;case"binary":t=e;break;case"buffer":t=e.toString("binary");break;case"array":t=Ve(e);break;default:throw new Error("Unrecognized type "+r.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(t=kr(t)),r.type="binary",so(t,r)}(n,t);case 137:if(80===a[1]&&78===a[2]&&71===a[3])throw new Error("PNG Image File is not a spreadsheet")}return bn.indexOf(a[0])>-1&&a[2]<=12&&a[3]<=31?Tn.to_workbook(n,t):io(e,n,t,s)}function oo(e,r,t,a,n,s,i,c){var o=yt(t),l=c.defval,f=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:t,enumerable:!1})}catch(g){u.__rowNum__=t}else u.__rowNum__=t;if(!i||e[t])for(var d=r.s.c;d<=r.e.c;++d){var p=i?e[t][d]:e[a[d]+o];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==c.rawNumbers)?m:Ft(p,m,c);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l)}}return{row:u,isempty:h}}function lo(e,r){if(null==e||null==e["!ref"])return[];var t={t:"n",v:0},a=0,n=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=r||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":o=Nt(f);break;case"number":(o=Nt(e["!ref"])).s.r=f;break;default:o=f}a>0&&(n=0);var h=yt(o.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=o.s.r,b=0,T={};g&&!e[v]&&(e[v]=[]);var E=l.skipHidden&&e["!cols"]||[],w=l.skipHidden&&e["!rows"]||[];for(b=o.s.c;b<=o.e.c;++b)if(!(E[b]||{}).hidden)switch(u[b]=Ct(b),t=g?e[v][b]:e[u[b]+h],a){case 1:s[b]=b-o.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-o.s.c];break;default:if(null==t&&(t={w:"__EMPTY",t:"s"}),c=i=Ft(t,null,l),m=T[i]||0){do{c=i+"_"+m++}while(T[c]);T[i]=m,T[c]=1}else T[i]=1;s[b]=c}for(v=o.s.r+n;v<=o.e.r;++v)if(!(w[v]||{}).hidden){var A=oo(e,o,v,u,a,s,g,l);(!1===A.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=A.row)}return d.length=p,d}var fo=/"/g;function ho(e,r,t,a,n,s,i,c){for(var o=!0,l=[],f="",h=yt(t),u=r.s.c;u<=r.e.c;++u)if(a[u]){var d=c.dense?(e[t]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){o=!1,f=""+(c.rawNumbers&&"n"==d.t?d.v:Ft(d,null,c));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||c.forceQuotes){f='"'+f.replace(fo,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(o=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(fo,'""')+'"'));l.push(f)}return!1===c.blankrows&&o?null:l.join(i)}function uo(e,r){var t=[],a=null==r?{}:r;if(null==e||null==e["!ref"])return"";var n=Nt(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),c=void 0!==a.RS?a.RS:"\n",o=c.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=Ct(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)(d[g]||{}).hidden||null!=(f=ho(e,n,g,h,i,o,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&t.push((m++?c:"")+f));return delete a.dense,t.join("")}function po(e,r,t){var a,n=t||{},s=+!n.skipHeader,i=e||{},c=0,o=0;if(i&&null!=n.origin)if("number"==typeof n.origin)c=n.origin;else{var l="string"==typeof n.origin?xt(n.origin):n.origin;c=l.r,o=l.c}var f={s:{c:0,r:0},e:{c:o,r:c+r.length-1+s}};if(i["!ref"]){var h=Nt(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==c&&(c=h.e.r+1,f.e.r=c+r.length-1+s)}else-1==c&&(c=0,f.e.r=r.length-1+s);var u=n.header||[],d=0;r.forEach((function(e,r){Ce(e).forEach((function(t){-1==(d=u.indexOf(t))&&(u[d=u.length]=t);var l=e[t],f="z",h="",p=Ot({c:o+d,r:c+r+s});a=mo(i,p),!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=Re(l)),h=n.dateNF||H[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l}))})),f.e.c=Math.max(f.e.c,o+u.length-1);var p=yt(c);if(s)for(d=0;d<u.length;++d)i[Ct(d+o)+p]={t:"s",v:u[d]};return i["!ref"]=It(f),i}function mo(e,r,t){if("string"==typeof r){if(Array.isArray(e)){var a=xt(r);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[r]||(e[r]={t:"z"})}return mo(e,Ot("number"!=typeof r?r:{r:r,c:t||0}))}function go(){return{SheetNames:[],Sheets:{}}}function vo(e,r,t,a){var n=1;if(!t)for(;n<=65535&&-1!=e.SheetNames.indexOf(t="Sheet"+n);++n,t=void 0);if(!t||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(t)>=0){var s=t.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||t;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(t=i+n);++n);}if(function(e,r){if(e.length>31){if(r)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var t=!0;$i.forEach((function(a){if(-1!=e.indexOf(a)){if(!r)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");t=!1}}))}(t),e.SheetNames.indexOf(t)>=0)throw new Error("Worksheet with name |"+t+"| already exists!");return e.SheetNames.push(t),e.Sheets[t]=r,t}function bo(e,r,t){return r?(e.l={Target:r},t&&(e.l.Tooltip=t)):delete e.l,e}var To={encode_col:Ct,encode_row:yt,encode_cell:Ot,encode_range:It,decode_col:_t,decode_row:kt,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:xt,decode_range:Rt,format_cell:Ft,sheet_add_aoa:Mt,sheet_add_json:po,sheet_add_dom:Oc,aoa_to_sheet:Lt,json_to_sheet:function(e,r){return po(null,e,r)},table_to_sheet:Rc,table_to_book:function(e,r){return Pt(Rc(e,r),r)},sheet_to_csv:uo,sheet_to_txt:function(e,r){return r||(r={}),r.FS="\t",r.RS="\n",uo(e,r)},sheet_to_json:lo,sheet_to_html:function(e,r){var t=r||{},a=null!=t.header?t.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',n=null!=t.footer?t.footer:"</body></html>",s=[a],i=Rt(e["!ref"]);t.dense=Array.isArray(e),s.push(function(e,r,t){return[].join("")+"<table"+(t&&t.id?' id="'+t.id+'"':"")+">"}(0,0,t));for(var c=i.s.r;c<=i.e.r;++c)s.push(xc(e,i,c,t));return s.push("</table>"+n),s.join("")},sheet_to_formulae:function(e){var r,t="",a="";if(null==e||null==e["!ref"])return[];var n,s=Nt(e["!ref"]),i="",c=[],o=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)c[n]=Ct(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=yt(f),n=s.s.c;n<=s.e.c;++n)if(t=c[n]+i,a="",void 0!==(r=l?(e[f]||[])[n]:e[t])){if(null!=r.F){if(t=r.F,!r.f)continue;a=r.f,-1==t.indexOf(":")&&(t=t+":"+t)}if(null!=r.f)a=r.f;else{if("z"==r.t)continue;if("n"==r.t&&null!=r.v)a=""+r.v;else if("b"==r.t)a=r.v?"TRUE":"FALSE";else if(void 0!==r.w)a="'"+r.w;else{if(void 0===r.v)continue;a="s"==r.t?"'"+r.v:""+r.v}}o[o.length]=t+"="+a}return o},sheet_to_row_object_array:lo,sheet_get_cell:mo,book_new:go,book_append_sheet:vo,book_set_sheet_visibility:function(e,r,t){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,r){if("number"==typeof r){if(r>=0&&e.SheetNames.length>r)return r;throw new Error("Cannot find sheet # "+r)}if("string"==typeof r){var t=e.SheetNames.indexOf(r);if(t>-1)return t;throw new Error("Cannot find sheet name |"+r+"|")}throw new Error("Cannot find sheet |"+r+"|")}(e,r);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),t){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+t)}e.Workbook.Sheets[a].Hidden=t},cell_set_number_format:function(e,r){return e.z=r,e},cell_set_hyperlink:bo,cell_set_internal_link:function(e,r,t){return bo(e,"#"+r,t)},cell_add_comment:function(e,r,t){e.c||(e.c=[]),e.c.push({t:r,a:t||"SheetJS"})},sheet_set_array_formula:function(e,r,t,a){for(var n="string"!=typeof r?r:Nt(r),s="string"==typeof r?r:It(r),i=n.s.r;i<=n.e.r;++i)for(var c=n.s.c;c<=n.e.c;++c){var o=mo(e,i,c);o.t="n",o.F=s,delete o.v,i==n.s.r&&c==n.s.c&&(o.f=t,a&&(o.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const Eo=r({data:()=>({schools:[],schoolNames:[],selectedIndex:null,selectedSchool:"",recipes:[]}),onLoad(){const e=uni.getStorageSync("schools");Array.isArray(e)&&(this.schools=e,this.schoolNames=e.map((e=>e.schoolName||"")),uni.showToast({title:"加载学校列表成功",icon:"success"}))},computed:{selectedIndexValid(){return null!==this.selectedIndex}},methods:{onSchoolChange(e){this.selectedIndex=e.detail.value,this.selectedSchool=this.schoolNames[this.selectedIndex]},confirmSelection(){uni.showToast({title:`已选择 ${this.selectedSchool}`,icon:"none"})},uploadFile(){if(this.selectedIndexValid)if(uni.chooseMessageFile)uni.chooseMessageFile({count:1,type:"file",extension:["xlsx"],success:e=>this.handleFile(e.tempFiles[0].path),fail:()=>uni.showToast({title:"选择失败",icon:"error"})});else{a("lemonjk-FileSelect").showPicker({mimeType:"*/*",utisType:["public.data"],filterConfig:{fileExtension:["xlsx","xls"]}},(e=>this.parseXLSX(e.files[0].filePath).then(this.saveRecipes).catch((()=>uni.showToast({title:"解析失败",icon:"error"})))))}},handleFile(e){uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const r=wx.base64ToArrayBuffer(e.data);this.parseData(r)},fail:()=>uni.showToast({title:"读取失败",icon:"error"})})},parseData(e){const r=co(e,{type:"array"}),t=r.Sheets[r.SheetNames[0]],a=To.sheet_to_json(t,{header:["foodName"],range:1}).map((e=>({foodName:String(e.foodName||"")})));this.saveRecipes(a)},readFile:e=>new Promise(((r,t)=>{plus.io.resolveLocalFileSystemURL(e,(e=>e.file((e=>{const a=new plus.io.FileReader;a.readAsDataURL(e),a.onloadend=e=>r(e.target.result),a.onerror=e=>t(e)}),(e=>t(e)))),(e=>t(e)))})),async parseXLSX(e){const r=co((await this.readFile(e)).replace(/^.*base64,/,""),{type:"base64"}),t=r.Sheets[r.SheetNames[0]];return To.sheet_to_json(t,{header:["foodName"],range:1}).map((e=>({foodName:String(e.foodName||"")})))},saveRecipes(e){this.recipes=e;const r=uni.getStorageSync("schoolRecipes")||{};r[this.selectedSchool]=e,uni.setStorageSync("schoolRecipes",r),n("log","at pages/food_updata/food_updata.vue:152",r),uni.showToast({title:"食谱上传并保存成功",icon:"success"})}}},[["render",function(r,t,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("text",{class:"title"},"学校食谱上传")]),e.createElementVNode("view",{class:"selector"},[e.createElementVNode("picker",{mode:"selector",range:s.schoolNames,onChange:t[0]||(t[0]=(...e)=>i.onSchoolChange&&i.onSchoolChange(...e))},[e.createElementVNode("view",{class:"picker-inner"},[e.createElementVNode("text",null,e.toDisplayString(s.selectedSchool||"请选择学校"),1)])],40,["range"]),e.createElementVNode("button",{class:"btn confirm",disabled:!i.selectedIndexValid,onClick:t[1]||(t[1]=(...e)=>i.confirmSelection&&i.confirmSelection(...e))},"确定",8,["disabled"])]),s.recipes.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"card"},[e.createElementVNode("scroll-view",{"scroll-x":"",class:"table-wrapper"},[e.createElementVNode("view",{class:"table-row header"},[e.createElementVNode("view",{class:"cell"},"序号"),e.createElementVNode("view",{class:"cell"},"食品名称")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.recipes,((r,t)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["table-row",{"odd-row":t%2==0,"even-row":t%2==1}]),key:t},[e.createElementVNode("view",{class:"cell"},e.toDisplayString(t+1),1),e.createElementVNode("view",{class:"cell"},e.toDisplayString(r.foodName),1)],2)))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"upload-section"},[e.createElementVNode("button",{class:"btn upload",disabled:!i.selectedIndexValid,onClick:t[2]||(t[2]=(...e)=>i.uploadFile&&i.uploadFile(...e))},"上传食谱文件",8,["disabled"])])])}],["__scopeId","data-v-0630ac79"]]);const wo=r({data:()=>({tableData:[]}),onLoad(){const e=uni.getStorageSync("schools");Array.isArray(e)&&(this.tableData=e,uni.showToast({title:"加载本地数据成功",icon:"success"}))},methods:{refreshPage(){this.$forceUpdate(),uni.showToast({title:"界面已刷新",icon:"success"})},uploadFile(){if(uni.chooseMessageFile)uni.chooseMessageFile({count:1,type:"file",extension:["xlsx"],success:e=>this.handleFile(e.tempFiles[0].path),fail:()=>uni.showToast({title:"选择失败",icon:"error"})});else{a("lemonjk-FileSelect").showPicker({mimeType:"*/*",utisType:["public.data"],filterConfig:{fileExtension:["xlsx","xls"]}},(e=>this.parseXLSX(e.files[0].filePath).then(this.saveData).catch((()=>uni.showToast({title:"解析失败",icon:"error"})))))}},handleFile(e){uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const r=wx.base64ToArrayBuffer(e.data);this.parseData(r)},fail:()=>uni.showToast({title:"读取失败",icon:"error"})})},parseData(e){const r=co(e,{type:"array"}),t=r.Sheets[r.SheetNames[0]],a=To.sheet_to_json(t,{header:["schoolName"],range:1}).map((e=>({schoolName:String(e.schoolName||"")})));this.saveData(a)},readFile:e=>new Promise(((r,t)=>{plus.io.resolveLocalFileSystemURL(e,(e=>e.file((e=>{const a=new plus.io.FileReader;a.readAsDataURL(e),a.onloadend=e=>r(e.target.result),a.onerror=e=>t(e)}),(e=>t(e)))),(e=>t(e)))})),async parseXLSX(e){const r=co((await this.readFile(e)).replace(/^.*base64,/,""),{type:"base64"}),t=r.Sheets[r.SheetNames[0]];return To.sheet_to_json(t,{header:["schoolName"],range:1}).map((e=>({schoolName:String(e.schoolName||"")})))},saveData(e){this.tableData=e,uni.setStorageSync("schools",e),uni.showToast({title:"上传并保存成功",icon:"success"})}}},[["render",function(r,t,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("text",{class:"title"},"学校名称上传"),e.createElementVNode("view",{class:"actions"},[e.createElementVNode("button",{class:"btn upload",onClick:t[0]||(t[0]=(...e)=>i.uploadFile&&i.uploadFile(...e))},"文件上传"),e.createElementVNode("button",{class:"btn refresh",onClick:t[1]||(t[1]=(...e)=>i.refreshPage&&i.refreshPage(...e))},"刷新")])]),e.createElementVNode("view",{class:"card"},[e.createElementVNode("scroll-view",{"scroll-x":"",class:"table-wrapper"},[e.createElementVNode("view",{class:"table-row header"},[e.createElementVNode("view",{class:"cell"},"序号"),e.createElementVNode("view",{class:"cell"},"学校名称")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.tableData,((r,t)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["table-row",{"odd-row":t%2==0,"even-row":t%2==1}]),key:t},[e.createElementVNode("view",{class:"cell"},e.toDisplayString(t+1),1),e.createElementVNode("view",{class:"cell"},e.toDisplayString(r.schoolName),1)],2)))),128))])])])}],["__scopeId","data-v-b89d4d29"]]),Ao=r({__name:"Module",setup(r){function t(){s.getUvcCamera((e=>{n("log","at pages/Module/Module.vue:50",JSON.parse(e))}))}async function a(e){return new Promise(((r,t)=>{let i={textData:[{content:"文本1",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:50},{content:"文本2",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:100}],imageData:[{url:"static/logo.png",width:50,height:50,topMargin:50,leftMargin:50},{url:"static/logo.png",width:50,height:50,topMargin:100,leftMargin:200}],vendorId:e,widthView:.5,heightView:.5,topMargin:.5,leftMargin:0,quirkFixBandwidth:!1};s.openUvcCamera(i,(t=>{let i=JSON.parse(t);n("log","at pages/Module/Module.vue:138",i),"ok"==i.msg?(s.clickUvcCameraImage({vendorId:e,imageNum:0},(e=>{n("log","at pages/Module/Module.vue:146","点击了第1张图片")})),s.clickUvcCameraImage({vendorId:e,imageNum:1},(e=>{n("log","at pages/Module/Module.vue:154","点击了第2张图片")})),r()):"用户拒绝了部分权限"==i.msg&&a()}))}))}async function i(){return new Promise(((e,r)=>{uni.getSystemInfo({success:function(r){let t=r.screenWidth,i=r.screenHeight;uni.createSelectorQuery().select(".sssAD").boundingClientRect().exec((r=>{let c={textData:[],imageData:[],vendorId:3034,widthView:1*(r[0].width/t).toFixed(2),heightView:1*(r[0].height/i).toFixed(2),topMargin:1*(r[0].top/i).toFixed(2),leftMargin:1*(r[0].left/t).toFixed(2),quirkFixBandwidth:!1};s.openUvcCamera(c,(r=>{let t=JSON.parse(r);n("log","at pages/Module/Module.vue:195",t),"ok"==t.msg?e():"用户拒绝了部分权限"==t.msg&&a(),e()}))}))}})}))}e.ref(null);let c=e.ref("");async function o(){var e;await(e=3034,new Promise(((r,t)=>{s.closedUvcCamera(e,(e=>{"ok"==JSON.parse(e).msg&&r()}))})))}return(r,a)=>(e.openBlock(),e.createElementBlock("view",null,[e.createElementVNode("button",{onClick:t},"获取当前UVC设备列表"),e.createElementVNode("button",{onClick:i},"打开UVC视频设备"),e.createElementVNode("button",{onClick:a[0]||(a[0]=e=>async function(e){return new Promise(((r,t)=>{let a={vendorId:e,someQuality:40,isBase64:!0};s.getUvcCameraImg(a,(e=>{let r=JSON.parse(e);"ok"==r.msg&&(n("log","at pages/Module/Module.vue:230",r),n("log","at pages/Module/Module.vue:231",r.file),c.value=r.base64.replace(/[\r\n]/g,""))}))}))}(4487))},"UVC设备拍照"),e.createElementVNode("button",{onClick:o},"关闭UVC设备拍照"),e.createElementVNode("image",{src:e.unref(c),mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"sssAD"})]))}},[["__scopeId","data-v-97b9999a"]]);__definePage("pages/login/login",t),__definePage("pages/index/index",i),__definePage("pages/food_updata/food_updata",Eo),__definePage("pages/school_updata/school_updata",wo),__definePage("pages/Module/Module",Ao);const So={onLaunch:function(){n("log","at App.vue:4","App Launch")},onShow:function(){n("log","at App.vue:7","App Show")},onHide:function(){n("log","at App.vue:10","App Hide")}};const{app:ko,Vuex:yo,Pinia:_o}={app:e.createVueApp(So)};uni.Vuex=yo,uni.Pinia=_o,ko.provide("__globalStyles",__uniConfig.styles),ko._component.mpType="app",ko._component.render=()=>{},ko.mount("#app")}(Vue);
