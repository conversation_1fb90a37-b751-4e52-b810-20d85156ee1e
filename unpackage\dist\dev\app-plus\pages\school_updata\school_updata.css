
.container[data-v-9b74eec5] {
  flex: 1;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}
.nav-bar[data-v-9b74eec5] { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; background: #fff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}
.title[data-v-9b74eec5] { font-size: 20px; font-weight: 600; color: #333;
}
.actions[data-v-9b74eec5] { display: flex;
}
.btn[data-v-9b74eec5] { margin-left: 12px; padding: 8px 16px; border: none; border-radius: 20px; font-size: 14px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform .2s;
}
.btn.upload[data-v-9b74eec5] { background: #4A90E2; color: #fff;
}
.btn.refresh[data-v-9b74eec5] { background: #50E3C2; color: #fff;
}
.btn[data-v-9b74eec5]:active { transform: translateY(1px);
}
.card[data-v-9b74eec5] { margin-top: 20px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 6px 20px rgba(0,0,0,0.05);
}
.empty-tip[data-v-9b74eec5] { padding: 20px; text-align: center; color: #888;
}
.table-wrapper[data-v-9b74eec5] { width: 100%;
}
.table-row[data-v-9b74eec5] { display: flex; padding: 12px 0;
}
.table-row.header[data-v-9b74eec5] { background: #f0f4f8; font-weight: 600;
}
.odd-row[data-v-9b74eec5] { background: #fff;
}
.even-row[data-v-9b74eec5] { background: #f9fbfd;
}
.cell[data-v-9b74eec5] { flex: 1; text-align: center; font-size: 14px; color: #555;
}
