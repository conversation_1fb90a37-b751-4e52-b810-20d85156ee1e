<template>
  <view class="login-container">
    <view class="background-gradient"></view>
    <view class="login-card">
      <view class="card-header">
        <view class="logo-area">
          <view class="logo-icon">
            <text class="logo-text">首衡</text>
          </view>
        </view>
        <text class="app-title">称重系统</text>
        <text class="app-subtitle">欢迎回来，请登录您的账户</text>
      </view>
      
      <view class="form-section">
        <view class="input-group">
          <view class="input-field">
            <view class="input-icon">
              <text class="icon">👤</text>
            </view>
            <input
              v-model.trim="username"
              type="text"
              placeholder="请输入账号"
              class="form-input"
              @blur="onBlur('username')"
              @focus="onFocus('username')"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-field">
            <view class="input-icon">
              <text class="icon">🔒</text>
            </view>
            <input
              v-model.trim="password"
              type="password"
              placeholder="请输入密码"
              class="form-input"
              @blur="onBlur('password')"
              @focus="onFocus('password')"
            />
          </view>
        </view>
        
        <view class="options-row">
          <view class="checkbox-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="rememberMe" 
                class="checkbox-input"
              />
              <view class="checkbox-custom"></view>
              <text class="checkbox-text">记住我</text>
            </label>
          </view>
          <text class="forgot-link" @tap="onForgot">忘记密码？</text>
        </view>
        
        <button class="login-button" @tap="onLogin">
          <text class="button-text">登录</text>
        </button>
        
        <view class="register-section">
          <text class="register-text">还没有账户？</text>
          <text class="register-link" @tap="onRegister">立即注册</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      username: 'user01',
      password: '123456',
      users: [],
      rememberMe: false,
      usernameFocus: false,
      passwordFocus: false
    };
  },
  onLoad() {
    const cached = uni.getStorageSync('users');
    if (Array.isArray(cached)) {
      this.users = cached;
    }
  },
  methods: {
    onLogin() {
      if (!this.username || !this.password) {
        uni.showToast({ title: '请输入账号和密码', icon: 'none' });
        return;
      }
      if (this.username === 'admin' && this.password === 'admin') {
        uni.showToast({ title: '上传学校信息', icon: 'success' });
        uni.navigateTo({ url: '/pages/school_updata/school_updata' });
        return;
      }
      if (this.username === 'updata_food' && this.password === '123456') {
        uni.showToast({ title: '上传食谱信息', icon: 'success' });
        uni.navigateTo({ url: '/pages/food_updata/food_updata' });
        return;
      }
      if (this.username === 'user01' && this.password === '123456') {
        uni.showToast({ title: '用户登录成功', icon: 'success' });
        uni.navigateTo({ url: '/pages/Module/Module' });
      } else {
        uni.showToast({ title: '账号或密码错误', icon: 'none' });
      }
    },
    onForgot() {
      uni.showToast({ title: '请联系管理员重置密码', icon: 'none' });
    },
    onRegister() {
      uni.showToast({ title: '请联系管理员注册', icon: 'none' });
    },
    onFocus(field) {
      if (field === 'username') this.usernameFocus = true;
      if (field === 'password') this.passwordFocus = true;
    },
    onBlur(field) {
      if (field === 'username') this.usernameFocus = false;
      if (field === 'password') this.passwordFocus = false;
    }
  }
};
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', system-ui, sans-serif;
}

/* 主容器 */
.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #D4C8BE 0%, #E0D6CC 25%, #E8E2DB 50%, #F0EDE8 75%, #F5F3F0 100%);
  overflow: hidden;
}

/* 背景渐变 - 增强对比度 */
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(168, 230, 207, 0.35) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 184, 163, 0.35) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(136, 201, 153, 0.25) 0%, transparent 70%),
    radial-gradient(circle at 10% 10%, rgba(119, 179, 153, 0.2) 0%, transparent 50%);
  z-index: 1;
}

/* 登录卡片 - 增大尺寸 */
.login-card {
  position: relative;
  z-index: 2;
  width: 95%;
  max-width: 600px;
  min-height: 650px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 2rem;
  padding: 3.5rem 3rem;
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-6px);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.25),
    0 12px 32px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.9);
}

/* 卡片头部 - 增大尺寸 */
.card-header {
  text-align: center;
  margin-bottom: 3rem;
}

.logo-area {
  margin-bottom: 1.5rem;
}

.logo-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8px 32px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
  position: relative;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border-radius: 50%;
}

.logo-text {
  font-size: 2.25rem;
  font-weight: 700;
  color: #271930;
  z-index: 1;
  position: relative;
}

.app-title {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #271930;
  margin-bottom: 0.75rem;
}

.app-subtitle {
  display: block;
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 400;
}

/* 表单区域 - 增大间距 */
.form-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.input-group {
  position: relative;
}

.input-field {
  position: relative;
  background: rgba(245, 243, 240, 0.9);
  border-radius: 1rem;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 70px;
}

.input-field:focus-within {
  border-color: #A8E6CF;
  box-shadow: 0 0 0 6px rgba(168, 230, 207, 0.15);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.95);
}

.input-icon {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.icon {
  font-size: 1.5rem;
  opacity: 0.7;
}

.form-input {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 4rem;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1.25rem;
  color: #271930;
  font-weight: 500;
  min-height: 70px;
}

.form-input::placeholder {
  color: #7FB3B3;
  font-weight: 400;
  font-size: 1.125rem;
}

/* 选项行 - 增大尺寸 */
.options-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0 2rem 0;
  padding: 0 0.5rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 0.75rem;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 24px;
  height: 24px;
  border: 3px solid #A8E6CF;
  border-radius: 0.5rem;
  background: rgba(168, 230, 207, 0.15);
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: #A8E6CF;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.15);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #271930;
  font-size: 1rem;
  font-weight: 700;
}

.checkbox-text {
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 500;
}

.forgot-link {
  font-size: 1.125rem;
  color: #FF9B85;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
  padding: 0.5rem;
}

.forgot-link:active {
  color: #FFB8A3;
}

/* 登录按钮 - 增大尺寸 */
.login-button {
  background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
  border: none;
  border-radius: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.375rem;
  font-weight: 700;
  color: #271930;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    0 8px 32px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  margin-top: 1rem;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 48px rgba(168, 230, 207, 0.5),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 
    0 4px 16px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(0, 0, 0, 0.15);
}

.button-text {
  position: relative;
  z-index: 1;
}

/* 注册区域 - 增大尺寸 */
.register-section {
  text-align: center;
  margin-top: 2rem;
}

.register-text {
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 500;
}

.register-link {
  font-size: 1.125rem;
  color: #FF9B85;
  font-weight: 600;
  text-decoration: none;
  margin-left: 0.5rem;
  transition: color 0.2s ease;
  padding: 0.5rem;
}

.register-link:active {
  color: #FFB8A3;
}

/* 平板优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .login-card {
    width: 90%;
    max-width: 700px;
    min-height: 700px;
    padding: 4rem 3.5rem;
  }
  
  .logo-icon {
    width: 140px;
    height: 140px;
  }
  
  .logo-text {
    font-size: 2.5rem;
  }
  
  .app-title {
    font-size: 2.75rem;
  }
  
  .app-subtitle {
    font-size: 1.25rem;
  }
  
  .form-input {
    font-size: 1.375rem;
    padding: 1.5rem 1.5rem 1.5rem 4.5rem;
    min-height: 80px;
  }
  
  .form-input::placeholder {
    font-size: 1.25rem;
  }
  
  .icon {
    font-size: 1.75rem;
  }
  
  .login-button {
    padding: 1.75rem 3.5rem;
    font-size: 1.5rem;
    min-height: 90px;
  }
  
  .checkbox-custom {
    width: 28px;
    height: 28px;
  }
  
  .checkbox-text,
  .forgot-link,
  .register-text,
  .register-link {
    font-size: 1.25rem;
  }
}

/* 大屏平板优化 */
@media (min-width: 1024px) {
  .login-card {
    width: 85%;
    max-width: 800px;
    min-height: 750px;
    padding: 4.5rem 4rem;
  }
  
  .logo-icon {
    width: 160px;
    height: 160px;
  }
  
  .logo-text {
    font-size: 3rem;
  }
  
  .app-title {
    font-size: 3rem;
  }
  
  .app-subtitle {
    font-size: 1.375rem;
  }
  
  .form-input {
    font-size: 1.5rem;
    padding: 1.75rem 1.75rem 1.75rem 5rem;
    min-height: 90px;
  }
  
  .form-input::placeholder {
    font-size: 1.375rem;
  }
  
  .icon {
    font-size: 2rem;
  }
  
  .login-button {
    padding: 2rem 4rem;
    font-size: 1.625rem;
    min-height: 100px;
  }
  
  .checkbox-custom {
    width: 32px;
    height: 32px;
  }
  
  .checkbox-text,
  .forgot-link,
  .register-text,
  .register-link {
    font-size: 1.375rem;
  }
}
</style>
