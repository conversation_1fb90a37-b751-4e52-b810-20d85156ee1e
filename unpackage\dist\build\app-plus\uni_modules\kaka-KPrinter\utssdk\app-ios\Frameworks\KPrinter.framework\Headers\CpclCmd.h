//
//  CpclCmd.h
//  KPrinter
//
//  Created by kaka on 12/17/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface CpclCmd: NSObject

- (void)addCommand:(NSString *)command;

/// 设置标签尺寸的宽和高
- (void)size:(NSString *)str;

/// 设置对齐
- (void)justification:(NSString *)str;

/// 矩形
- (void)box:(NSString *)str;

/// 图片
- (void)image:(NSString *)str;

/// 文本
- (void)text:(NSString *)str;

/// 二维码
- (void)qrCode:(NSString *)str;

/// 条码
- (void)barCode:(NSString *)str;

///  线条
- (void)line:(NSString *)str;

/// 打印完成后定位到下一页的顶部
- (void)form;

/// 打印标签
- (void)print;

@end

NS_ASSUME_NONNULL_END
