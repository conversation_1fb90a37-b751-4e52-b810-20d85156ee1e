<template>
  <view class="container">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <text class="title">学校名称上传</text>
      <view class="actions">
        <button class="btn upload" @click="uploadFile">文件上传</button>
        <button class="btn refresh" @click="refreshPage">刷新</button>
      </view>
    </view>

    <!-- 数据表格 -->
    <view class="card">
      <scroll-view scroll-x class="table-wrapper">
        <view class="table-row header">
          <view class="cell">序号</view>
          <view class="cell">学校名称</view>
        </view>
        <view
          class="table-row"
          v-for="(name, index) in schoolNames"
          :key="index"
          :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 === 1 }"
        >
          <view class="cell">{{ index + 1 }}</view>
          <view class="cell">{{ name }}</view>
        </view>
      </scroll-view>
      <view v-if="schoolNames.length === 0" class="empty-tip">暂无数据，请先上传文件。</view>
    </view>
  </view>
</template>

<script>
import * as XLSX from 'xlsx';
const lemonjkFileSelect = uni.requireNativePlugin('lemonjk-FileSelect');

export default {
  data() {
    return {
      schoolNames: []
    };
  },
  onLoad() {
    // 从缓存读取，兼容历史字符串格式和新对象格式
    const cached = uni.getStorageSync('schools') || [];
    let names = [];
    if (cached.length && typeof cached[0] === 'object' && cached[0].schoolName) {
      names = cached.map(item => item.schoolName);
    } else if (Array.isArray(cached)) {
      names = cached;
    }
    if (names.length) {
      this.schoolNames = names;
      uni.showToast({ title: '加载本地数据成功', icon: 'success' });
    }
  },
  methods: {
    refreshPage() {
      this.$forceUpdate();
      uni.showToast({ title: '界面已刷新', icon: 'success' });
    },
    uploadFile() {
      const choose = uni.chooseMessageFile || uni.chooseFile;
      if (choose) {
        this.nativePick(choose);
      } else {
        lemonjkFileSelect.isHaveSecurityScopeGrant({}, res => {
          if (res.result === 'yes') this.pluginPick();
          else lemonjkFileSelect.reqCustomPickerPermission({}, p => {
            if (p.code === 3002) this.pluginPick();
            else uni.showModal({ content: '需要文件访问权限', showCancel: false });
          });
        });
      }
    },
    nativePick(fn) {
      fn({
        count: 1,
        type: 'file',
        extension: ['xlsx', 'xls'],
        success: res => {
          const files = res.tempFiles || res.files || [];
          if (!files.length) return uni.showToast({ title: '未选择文件', icon: 'error' });
          const file = files[0];
          if (file.file && typeof FileReader !== 'undefined') {
            const reader = new FileReader();
            reader.onload = e => this.handleBase64(e.target.result);
            reader.onerror = () => uni.showToast({ title: '读取失败', icon: 'error' });
            reader.readAsDataURL(file.file);
          } else {
            const path = file.path || file.filePath;
            this.readAndParse(path);
          }
        },
        fail: () => uni.showToast({ title: '选择失败', icon: 'error' })
      });
    },
    pluginPick() {
      lemonjkFileSelect.showPicker({
        pathScope: '/Download',
        mimeType: '*/*',
        utisType: ['public.data'],
        filterConfig: { fileExtension: ['xlsx', 'xls'] }
      }, result => {
        if (result.code !== 0 || !result.files || !result.files.length) return uni.showToast({ title: '未获取文件', icon: 'error' });
        this.readAndParse(result.files[0].filePath);
      });
    },
    readAndParse(path) {
      console.log('读取文件路径:', path);
      if (typeof plus !== 'undefined' && plus.io) {
        plus.io.resolveLocalFileSystemURL(path, entry => {
          entry.file(file => {
            const reader = new plus.io.FileReader();
            reader.onloadend = e => this.handleBase64(e.target.result);
            reader.readAsDataURL(file);
          });
        }, () => uni.showToast({ title: '读取失败', icon: 'error' }));
      } else if (uni.getFileSystemManager) {
        uni.getFileSystemManager().readFile({
          filePath: path,
          encoding: 'base64',
          success: r => this.handleBase64('data:;base64,' + r.data),
          fail: () => uni.showToast({ title: '读取失败', icon: 'error' })
        });
      } else uni.showToast({ title: '环境不支持', icon: 'error' });
    },
    handleBase64(dataURL) {
      console.log('解析到 Base64 长度:', dataURL.length);
      const b64 = dataURL.split(',')[1];
      const buffer = this.base64ToArrayBuffer(b64);
      const wb = XLSX.read(buffer, { type: 'array' });
      const ws = wb.Sheets[wb.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(ws);
      console.log('sheet_to_json 数据:', data);
      const names = data
        .map(item => item['学校名称'])
        .filter(name => name)
        .map(name => String(name).trim());
      console.log('提取到的学校名称:', names);
      this.saveData(names);
    },
    base64ToArrayBuffer(b64) {
      const bin = atob(b64), len = bin.length, buf = new Uint8Array(len);
      for (let i = 0; i < len; i++) buf[i] = bin.charCodeAt(i);
      return buf.buffer;
    },
    saveData(names) {
      this.schoolNames = names;
      // 按对象格式存储，便于下个页面读取
      const list = names.map(n => ({ schoolName: n }));
      uni.setStorageSync('schools', list);
      uni.showToast({ title: '保存成功', icon: 'success' });
      // 自动返回上一页，触发第二页面 onShow 更新列表
      setTimeout(() => {
        uni.navigateBack();
      }, 500);
    }
  }
};
</script>

<style scoped>
.container {
  flex: 1;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}
.nav-bar { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; background: #fff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); }
.title { font-size: 20px; font-weight: 600; color: #333; }
.actions { display: flex; }
.btn { margin-left: 12px; padding: 8px 16px; border: none; border-radius: 20px; font-size: 14px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform .2s; }
.btn.upload { background: #4A90E2; color: #fff; }
.btn.refresh { background: #50E3C2; color: #fff; }
.btn:active { transform: translateY(1px); }
.card { margin-top: 20px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 6px 20px rgba(0,0,0,0.05); }
.empty-tip { padding: 20px; text-align: center; color: #888; }
.table-wrapper { width: 100%; }
.table-row { display: flex; padding: 12px 0; }
.table-row.header { background: #f0f4f8; font-weight: 600; }
.odd-row { background: #fff; }
.even-row { background: #f9fbfd; }
.cell { flex: 1; text-align: center; font-size: 14px; color: #555; }
</style>
