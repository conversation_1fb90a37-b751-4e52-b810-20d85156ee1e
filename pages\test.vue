<template>
	<view class="page">
		<!-- 上部：学校选择 + 串口数据 + 操作按钮 -->
		<view class="section top">
			<view class="col select-col">
				<picker mode="selector" :range="schoolNames" @change="onSchoolChange">
					<view class="picker-display">
						<text>{{ selectedSchool || '请选择学校' }}</text>
					</view>
				</picker>
				<button class="btn get-recipes" @click="fetchRecipes">获取食谱</button>
			</view>
			<view class="col data-col">
				<text class="data-text">{{ cardnumber !== null ? formatNumber(cardnumber) + ' Kg' : '--' }}</text>
			</view>
			<view class="col action-col">
				<button class="btn peel" @click="onPeel">去皮</button>
				<button class="btn zero" @click="onZero">置零</button>
			</view>
		</view>

		<!-- 中部：食谱列表 + 摄像头预览 -->
		<view class="section middle">
			<view class="col recipe-col">
				<scroll-view scroll-y class="recipe-list">
					<view v-for="(item, idx) in recipeList" :key="idx" class="recipe-item"
						:class="{selected: selectedFoods.includes(item.foodName)}" @click="toggleFood(item.foodName)">
						<text>{{ item.foodName }}</text>
					</view>
				</scroll-view>
			</view>
			<view class="col camera-col">
				<!-- 摄像头预览容器 -->
				<view class="camera-view"></view>
			</view>
		</view>

		<!-- 下部：拍照 + 预览 + 提交 -->
		<view class="section bottom">
			<button class="btn capture" @click="takePhoto">拍照</button>
			<view class="preview-wrapper">
				<text v-if="!capturedImage">预览区</text>
				<image v-else :src="capturedImage" mode="aspectFill" class="photo-preview" />
			</view>
			<button class="btn submit" @click="onSubmit">提交</button>
		</view>
	</view>
</template>

<script>
	import {
		getUvcCameraImg,
		openUvcCamera,
		closedUvcCamera
	} from '@/uni_modules/mushan-uvccamera';
	const serialPort = uni.requireNativePlugin('Fvv-UniSerialPort');
	export default {
		data() {
			return {
				schools: [],
				schoolNames: [],
				selectedSchool: null,
				recipeList: [],
				selectedFoods: [],
				cardnumber: null,
				capturedImage: '', // 存储本地文件路径或 base64
				vendorId: 3141
			};
		},
		methods: {
			openCamera() {
				closedUvcCamera(this.vendorId, () => {
					uni.getSystemInfo({
						success: info => {
							uni.createSelectorQuery()
								.select('.camera-view')
								.boundingClientRect(rect => {
									const vw = rect.width / info.screenWidth;
									const vh = rect.height / info.screenHeight;
									const top = rect.top / info.screenHeight;
									const left = rect.left / info.screenWidth;
									openUvcCamera({
										vendorId: this.vendorId,
										widthView: vw,
										heightView: vh,
										topMargin: top,
										leftMargin: left,
										quirkFixBandwidth: false,
										textData: [],
										imageData: []
									}, res => {
										const d = JSON.parse(res);
										if (d.msg !== 'ok') uni.showToast({
											title: '摄像头打开失败',
											icon: 'none'
										});
									});
								})
								.exec();
						}
					});
				});
			},
			takePhoto() {
				getUvcCameraImg({
					vendorId: this.vendorId,
					someQuality: 40,
					isBase64: false,
					keepAlive: true
				}, res => {
					const d = JSON.parse(res);
					if (d.msg === 'ok') {
						// 使用本地路径展示
						const path = d.file;
						console.log('photo saved to:', path);
						this.capturedImage = path;
						// 重新启动预览
						setTimeout(() => this.openCamera(), 500);
					} else {
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						});
					}
				});
			},
			onSchoolChange(e) {
				this.selectedSchool = this.schoolNames[e.detail.value];
			},
			fetchRecipes() {
				if (!this.selectedSchool) return uni.showToast({
					title: '请选择学校',
					icon: 'none'
				});
				this.recipeList = (uni.getStorageSync('schoolRecipes') || {})[this.selectedSchool] || [];
				this.selectedFoods = [];
				uni.showToast({
					title: '食谱已加载',
					icon: 'success'
				});
			},
			toggleFood(name) {
				const idx = this.selectedFoods.indexOf(name);
				if (idx >= 0) this.selectedFoods.splice(idx, 1);
				else this.selectedFoods.push(name);
			},
			
			hexToText(hex) {
				return (hex.replace(/\s+/g, '').match(/.{1,2}/g) || []).map(b => String.fromCharCode(parseInt(b, 16)))
					.join('');
			},
			
			
			onMessage(rec) {
				const m = this.hexToText(rec).trim().match(/^[sw][gn](-?\d+\.\d+)kg$/i);
				if (m) this.cardnumber = parseFloat(m[1]);
			},
			
			
			formatNumber(v) {
				return Number(v).toFixed(3);
			},
			onPeel() {
				serialPort.sendBytes([84]);
			},
			onZero() {
				serialPort.sendBytes([90]);
			},
			onSubmit() {
				if (!this.cardnumber || !this.capturedImage || this.selectedFoods.length === 0) {
					return uni.showToast({
						title: '信息不完整，无法提交',
						icon: 'none'
					});
				}
				const d = new Date();
				const rec = {
					time: `${d.getFullYear()}年${d.getMonth() + 1}月${d.getDate()}日${d.getHours()}时`,
					weight: this.formatNumber(this.cardnumber),
					foods: this.selectedFoods,
					image: this.capturedImage
				};
				const all = uni.getStorageSync('submissionRecords') || [];
				all.push(rec);
				uni.setStorageSync('submissionRecords', all);
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				});
			}
		},
		mounted() {
			this.schoolNames = (uni.getStorageSync('schools') || []).map(s => s.schoolName || '');
			serialPort.setPath('/dev/ttyS3');
			serialPort.setBaudRate(9600);
			serialPort.open(res => {
				if (res.status) serialPort.onMessageHex(rec => this.onMessage(rec));
			});
			this.openCamera();
		}
	};
</script>

<style scoped>
	.page {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	.section {
		display: flex;
	}

	.top {
		flex: 1;
		padding: 10px;
	}

	.middle {
		flex: 3;
	}

	.bottom {
		flex: 1;
		padding: 10px;
		justify-content: space-between;
		align-items: center;
	}

	.col {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.select-col {
		flex: 1;
		flex-direction: column;
	}

	.data-col {
		flex: 2;
	}

	.action-col {
		flex: 1;
		flex-direction: column;
	}

	.recipe-col {
		flex: 1;
		background: #f9f9f9;
	}

	.camera-col {
		flex: 2;
		position: relative;
	}

	.preview-wrapper {
		width: 100px;
		height: 100px;
		border: 1px dashed #ccc;
		border-radius: 8px;
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
	}

	.picker-display {
		padding: 10px;
		background: #fff;
		border-radius: 8px;
	}

	.btn {
		padding: 8px 12px;
		border-radius: 8px;
		background: #4A90E2;
		color: #fff;
	}

	.get-recipes {
		background: #50E3C2;
	}

	.data-text {
		font-size: 24px;
	}

	.recipe-list {
		width: 100%;
		height: 100%;
	}

	.recipe-item {
		padding: 10px;
		border-bottom: 1px solid #ddd;
	}

	.recipe-item.selected {
		background: #e6f7ff;
	}

	.camera-view {
		width: 50%;
		height: 50%;
		background: #ffffff;
	}

	.photo-preview {
		width: 100%;
		height: 100%;
	}

	.capture,
	.submit {
		padding: 8px 12px;
	}
</style>