<template>
	<view class="main-bg">
		<!-- 顶部导航 -->
		<view class="nav-bar">
			<view class="nav-left">
				<image src="/static/logo.png" class="logo" mode="aspectFit" />
				<text class="sys-title">智能称重采集系统</text>
			</view>
			<view class="nav-center">
				<text class="nav-time">{{ currentTime }}</text>
			</view>
			<view class="nav-right">
				<view :class="['printer-status-card', printerConnected ? 'connected' : 'disconnected']">
					<text class="printer-status-text">
						打印机{{ printerConnected ? '已连接' : '未连接' }}
					</text>
				</view>
			</view>
		</view>

		<!-- 主操作区 -->
		<view class="main-content">
			<!-- 左侧：学校与食谱选择 -->
			<view class="side-card left-card">
				<view class="card-header">
					<text class="section-title">学校选择</text>
				</view>
				<view class="school-select-container">
					<view class="school-select-box" @click="toggleSchoolList">
						<text class="select-text">{{ selectedSchool || '请选择学校' }}</text>
						<text class="arrow">▼</text>
					</view>
					<view v-if="showSchoolList" class="dropdown-school">
						<scroll-view scroll-y class="dropdown-list">
							<view v-for="(name, idx) in schoolNames" :key="idx" class="dropdown-item"
								@click="onSchoolSelect(idx)">
								<text>{{ name }}</text>
							</view>
						</scroll-view>
					</view>
				</view>
				<button class="btn primary-btn fetch-btn" @click="fetchRecipes">获取食谱</button>

				<view class="card-header" style="margin-top: 2rem;">
					<text class="section-title">食谱选择</text>
				</view>
				<scroll-view scroll-y class="recipe-list-container">
					<view v-for="(item, idx) in recipeList" :key="idx" class="recipe-card"
						:class="{ selected: selectedFood === item.foodName }" @click="selectOnlyFood(item.foodName)">
						<text class="recipe-text">{{ item.foodName }}</text>
					</view>
				</scroll-view>
			</view>

			<!-- 中间：摄像头预览+拍照 -->
			<view class="center-card">
				<view class="camera-section">
					<view class="camera-preview">
						<view class="camera-view"></view>
					</view>
					<view class="photo-preview-container">
						<image v-if="capturedImage1" :src="capturedImage1" mode="aspectFill"
							class="photo-preview-img" />
						<view v-else class="photo-placeholder">
							<text class="placeholder-text">拍照预览区</text>
						</view>
					</view>
				</view>
				<button class="btn primary-btn photo-btn" @click="takePhoto">拍照</button>
			</view>

			<!-- 右侧：称重显示+操作 -->
			<view class="side-card right-card">
				<view class="card-header">
					<text class="section-title">当前重量</text>
				</view>
				<view class="weight-display-container">
					<view class="weight-display">
						<text class="weight-num">{{ cardnumber !== null ? formatNumber(cardnumber) : '--' }}</text>
						<text class="weight-unit">{{ flag === 'kg' ? '公斤' : flag === 'sj' ? '斤' : '' }}</text>
					</view>
				</view>
				<view class="weight-controls">
					<button class="btn accent-btn control-btn" @click="onPeel">去皮</button>
					<button class="btn accent-btn control-btn" @click="onZero">置零</button>
				</view>
			</view>
		</view>

		<!-- 底部操作区 -->
		<view class="footer-bar">
			<button class="btn primary-btn footer-btn" @click="onSubmit">提交数据</button>
			<button class="btn neutral-btn footer-btn" @click="get_data">查看数据</button>
			<button class="btn secondary-btn footer-btn" @click="school">上传学校</button>
			<button class="btn secondary-btn footer-btn" @click="food">上传食谱</button>
		</view>

	</view>
</template>

<script>
	import {
		getUvcCameraImg,
		openUvcCamera,
		closedUvcCamera
	} from '@/uni_modules/mushan-uvccamera';
	const serialPort = uni.requireNativePlugin('Fvv-UniSerialPort');
	import * as KPrinter from '@/uni_modules/kaka-KPrinter';

	export default {
		data() {
			return {
				schoolNames: [],
				selectedSchool: null,
				showSchoolList: false,
				recipeList: [],
				selectedFood: null,
				cardnumber: '1',
				capturedImage1: '',
				vendorId: 6366,
				camerasOpened: false,
				flag: '',
				printData: null,
				printerConnected: false,
				currentTime: '',
			};
		},
		methods: {
			toggleSchoolList() {
				this.showSchoolList = !this.showSchoolList;
			},
			onSchoolSelect(idx) {
				this.selectedSchool = this.schoolNames[idx];
				this.showSchoolList = false;
			},
			fetchRecipes() {
				if (!this.selectedSchool) {
					uni.showToast({
						title: '请选择学校',
						icon: 'none'
					});
					return;
				}
				let raw = uni.getStorageSync('schoolRecipes');
				let map = {};
				try {
					map = typeof raw === 'string' ? JSON.parse(raw) : (raw || {});
				} catch (e) {
					console.error('解析 schoolRecipes 失败', e);
				}
				this.recipeList = map[this.selectedSchool] || [];
				this.selectedFood = null;
				uni.showToast({
					title: '食谱已加载',
					icon: 'success'
				});
			},
			selectOnlyFood(name) {
				this.selectedFood = this.selectedFood === name ? null : name;
			},
			UTSgetUvcCamera() {
				getUvcCamera((res) => {
					let resData = JSON.parse(res)
					console.log(resData);
				})
			},
			openCamera() {
				// this.UTSgetUvcCamera();
				closedUvcCamera(this.vendorId, () => {
					uni.getSystemInfo({
						success: info => {
							uni.createSelectorQuery()
								.select('.camera-view')
								.boundingClientRect(rect => {
									const vw = rect.width / info.screenWidth;
									const vh = rect.height / info.screenHeight;
									const top = rect.top / info.screenHeight;
									const left = rect.left / info.screenWidth;
									openUvcCamera({
										vendorId: this.vendorId,
										widthView: vw,
										heightView: vh,
										topMargin: top,
										leftMargin: left,
										quirkFixBandwidth: true,
										textData: [],
										imageData: [],
									}, res => {
										console.log(res);
									});
								})
								.exec();
						}
					});
				});
			},
			takePhoto() {
				getUvcCameraImg({
					vendorId: this.vendorId,
					someQuality: 40,
					isBase64: false,
					keepAlive: true,
				}, res1 => {
					const d1 = JSON.parse(res1);
					if (d1.msg === 'ok') {
						this.capturedImage1 = d1.file;
					} else {
						uni.showToast({
							title: '摄像头拍照失败',
							icon: 'none'
						});
					}
				});
			},
			onMessage(rec) {
				const text = this.hexToText(rec).trim();
				const m = text.match(/^[sw][gn](-?\d+\.\d+)[ks][gj]$/i);
				let lastTwo = m ? m[0].slice(-2).toLowerCase() : null;
				this.flag = lastTwo;
				if (m) this.cardnumber = parseFloat(m[1]);
			},
			hexToText(hex) {
				return (hex.replace(/\s+/g, '').match(/.{1,2}/g) || [])
					.map(b => String.fromCharCode(parseInt(b, 16)))
					.join('');
			},
			formatNumber(v) {
				return Number(v).toFixed(3);
			},
			onPeel() {
				serialPort.sendBytes([84]);
			},
			onZero() {
				serialPort.sendBytes([90]);
			},
			printRecord(record) {
				if (!this.printerConnected) {
					uni.showToast({
						title: '打印机未连接',
						icon: 'none'
					});
					return;
				}
				KPrinter.tscSize({
					width: 76,
					height: 42
				});
				KPrinter.tscCls();
				KPrinter.tscDensity(8);
				KPrinter.tscDirection({
					directionReverse: false,
					isMirror: false
				});
				KPrinter.tscText({
					x: 200,
					y: 50,
					xScal: 1,
					yScal: 1,
					rotation: 0,
					font: "TSS24.BF2",
					content: `食谱: ${record.food || ''}`,
				});
				KPrinter.tscText({
					x: 200,
					y: 100,
					xScal: 1,
					yScal: 1,
					rotation: 0,
					font: "TSS24.BF2",
					content: `时间: ${record.time || ''}`,
				});
				KPrinter.tscText({
					x: 200,
					y: 150,
					xScal: 1,
					yScal: 1,
					rotation: 0,
					font: "TSS24.BF2",
					content: `重量: ${record.weight || ''} 公斤`,
				});
				KPrinter.tscText({
					x: 200,
					y: 200,
					xScal: 1,
					yScal: 1,
					rotation: 0,
					font: "TSS24.BF2",
					content: `操作人: ${'刘晓宇'}`,
				});
				KPrinter.tscPrint(1);
				KPrinter.writeDataUSB();
			},
			onSubmit() {
				const missing = [];
				if (!this.selectedFood) missing.push('食谱');
				if (!this.capturedImage1) missing.push('照片');
				if (missing.length) {
					uni.showToast({
						title: `缺少${missing.join('、')}`,
						icon: 'none'
					});
					return;
				}
				const d = new Date();
				const timeStr = `${d.getFullYear()}-${d.getMonth()+1}-${d.getDate()} ` +
					`${d.getHours()}:${d.getMinutes()}:${d.getSeconds()}`;
				const record = {
					food: this.selectedFood,
					time: timeStr,
					weight: this.formatNumber(this.cardnumber),
					imagePath1: this.capturedImage1,
				};
				const all = uni.getStorageSync('submissionRecords') || [];
				all.push(record);
				uni.setStorageSync('submissionRecords', all);
				this.printData = {
					food: this.selectedFood,
					time: timeStr,
					weight: this.formatNumber(this.cardnumber),
				};
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				});
				this.printRecord(record);
				this.selectedFood = null;
				this.cardnumber = null;
				this.capturedImage1 = '';
			},
			get_data() {
				closedUvcCamera(this.vendorId, () => {});
				this.camerasOpened = false;
				uni.navigateTo({
					url: '/pages/get_data/get_data'
				});
			},
			school() {
				closedUvcCamera(this.vendorId, () => {});
				this.camerasOpened = false;
				uni.navigateTo({
					url: '/pages/school_updata/school_updata'
				});
			},
			food() {
				closedUvcCamera(this.vendorId, () => {});
				this.camerasOpened = false;
				uni.navigateTo({
					url: '/pages/food_updata/food_updata'
				});
			},
			updateTime() {
				const d = new Date();
				const pad = n => n < 10 ? '0' + n : n;
				this.currentTime =
					`${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
			},
			connectFixedPrinter() {
				// 先获取所有可用USB设备，只尝试以 /dev/bus/usb/005 开头的路径
				KPrinter.getUsbDeviceList((deviceList) => {
					let paths = [];
					if (Array.isArray(deviceList)) {
						paths = deviceList
							.map(d => d.devicePath || d.path || d.deviceName)
							.filter(p => p && p.startsWith('/dev/bus/usb/005'));
					} else if (deviceList && (deviceList.devicePath || deviceList.path || deviceList
							.deviceName)) {
						const p = deviceList.devicePath || deviceList.path || deviceList.deviceName;
						if (p && p.startsWith('/dev/bus/usb/005')) paths = [p];
					}
					if (paths.length === 0) {
						this.printerConnected = false;
						return;
					}
					let connected = false;
					let idx = 0;
					const tryConnect = () => {
						if (connected || idx >= paths.length) return;
						KPrinter.connectUSB(paths[idx]);
					};
					KPrinter.onUSBConnectStateChange({
						onSuccess: () => {
							this.printerConnected = true;
							connected = true;
						},
						onDisconnect: () => {
							this.printerConnected = false;
						},
						onFail: () => {
							idx++;
							tryConnect();
						}
					});
					tryConnect();
				});
			}
		},
		onShow() {
			if (!this.camerasOpened) this.openCamera();
			console.log(this.camerasOpened);
			this.updateTime();
			this._timer = setInterval(this.updateTime, 1000);
			this.connectFixedPrinter();
		},
		onHide() {
			closedUvcCamera(this.vendorId, () => {});
			this.camerasOpened = false;
			clearInterval(this._timer);
		},
		onUnload() {
			closedUvcCamera(this.vendorId, () => {});
			this.camerasOpened = false;
			clearInterval(this._timer);
		},
		mounted() {
			// 学校名初始化
			const schools = uni.getStorageSync('schools') || [];
			this.schoolNames = Array.isArray(schools) ? schools.map(item => item.schoolName || '') : [];
			// 串口初始化
			serialPort.setPath('/dev/ttyS1');
			serialPort.setBaudRate(9600);
			serialPort.open(res => {
				if (res.status) {
					serialPort.onMessageHex(rec => this.onMessage(rec));
				}
			});
		},
	};
</script>

<style scoped>
	/* 3D Soft Dashboard Design System Implementation */
	.main-bg {
		min-height: 100vh;
		background: linear-gradient(135deg, #F5F3F0 0%, #FEFCFA 100%);
		display: flex;
		flex-direction: column;
		font-family: 'Inter', system-ui, sans-serif;
	}

	/* Navigation Bar */
	.nav-bar {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 80px;
		background: linear-gradient(135deg, #FEFCFA 0%, #F0EDE8 100%);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		padding: 0 2rem;
		border-bottom-left-radius: 1.5rem;
		border-bottom-right-radius: 1.5rem;
		margin: 0 1rem;
		margin-top: 1rem;
	}

	.nav-left {
		display: flex;
		align-items: center;
	}

	.logo {
		width: 48px;
		height: 48px;
		margin-right: 1rem;
		border-radius: 0.75rem;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
	}

	.sys-title {
		font-size: 1.5rem;
		font-weight: 600;
		color: #2d3a4b;
	}

	.nav-center {
		flex: 1;
		text-align: center;
	}

	.nav-time {
		font-size: 1.25rem;
		color: #7FB3B3;
		font-weight: 500;
	}

	.nav-right {
		min-width: 180px;
		text-align: right;
	}

	.printer-status-card {
		padding: 0.5rem 1rem;
		border-radius: 0.75rem;
		font-size: 0.875rem;
		font-weight: 500;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		transition: all 0.2s ease-out;
	}

	.printer-status-card.connected {
		background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
		color: #2d3a4b;
	}

	.printer-status-card.disconnected {
		background: linear-gradient(135deg, #FFB8A3 0%, #FF9B85 100%);
		color: #2d3a4b;
	}

	.printer-status-text {
		font-weight: 600;
	}

	/* Main Content */
	.main-content {
		flex: 1;
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 1.5rem;
		padding: 1.5rem;
		margin-bottom: 1rem;
	}

	/* Side Cards */
	.side-card {
		background: linear-gradient(135deg, #FEFCFA 0%, #F0EDE8 100%);
		border-radius: 1.5rem;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
		padding: 2rem;
		display: flex;
		flex-direction: column;
		min-height: 400px;
		transition: all 0.2s ease-out;
	}

	.side-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
	}

	.left-card {
		margin-right: 0;
	}

	.right-card {
		margin-left: 0;
	}

	/* Center Card */
	.center-card {
		background: linear-gradient(135deg, #FEFCFA 0%, #F0EDE8 100%);
		border-radius: 1.5rem;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
		padding: 2rem;
		display: flex;
		flex-direction: column;
		align-items: center;
		min-height: 400px;
		position: relative;
		transition: all 0.2s ease-out;
	}

	.center-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
	}

	/* Card Headers */
	.card-header {
		margin-bottom: 1.5rem;
	}

	.section-title {
		font-size: 1.25rem;
		font-weight: 600;
		color: #2d3a4b;
	}

	/* School Selection */
	.school-select-container {
		position: relative;
		margin-bottom: 1rem;
	}

	.school-select-box {
		background: linear-gradient(135deg, #F5F3F0 0%, #E8E2DB 100%);
		border: 2px solid #7FB3B3;
		border-radius: 0.75rem;
		height: 3rem;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 1rem;
		padding: 0 1rem;
		cursor: pointer;
		position: relative;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		transition: all 0.2s ease-out;
	}

	.school-select-box:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
	}

	.select-text {
		color: #2d3a4b;
		font-weight: 500;
	}

	.arrow {
		font-size: 0.875rem;
		color: #7FB3B3;
		margin-left: 0.5rem;
		transition: transform 0.2s ease-out;
	}

	.school-select-box:hover .arrow {
		transform: translateY(1px);
	}

	.dropdown-school {
		position: absolute;
		z-index: 1000;
		width: 100%;
		background: #FEFCFA;
		border: 1.5px solid #7FB3B3;
		border-radius: 0.75rem;
		max-height: 220px;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
		margin-top: 0.25rem;
		overflow: hidden;
	}

	.dropdown-list {
		width: 100%;
	}

	.dropdown-item {
		padding: 1rem 0.75rem;
		border-bottom: 1px solid #E8E2DB;
		font-size: 0.875rem;
		color: #2d3a4b;
		cursor: pointer;
		transition: background 0.2s ease-out;
	}

	.dropdown-item:hover {
		background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
		color: #2d3a4b;
	}

	.dropdown-item:last-child {
		border-bottom: none;
	}

	/* Recipe List */
	.recipe-list-container {
		width: 100%;
		flex: 1;
		min-height: 200px;
		max-height: 320px;
		margin-top: 0.5rem;
		overflow-y: auto;
	}

	.recipe-card {
		background: linear-gradient(135deg, #F5F3F0 0%, #E8E2DB 100%);
		border-radius: 0.75rem;
		padding: 1rem 0.75rem;
		font-size: 1rem;
		margin-bottom: 0.75rem;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		cursor: pointer;
		transition: all 0.2s ease-out;
		border: 2px solid transparent;
	}

	.recipe-card:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
	}

	.recipe-card.selected {
		background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
		font-weight: 600;
		color: #2d3a4b;
		border-color: #7FB3B3;
		box-shadow: 0 4px 16px rgba(168, 230, 207, 0.3);
	}

	.recipe-text {
		font-weight: 500;
	}

	/* Camera Section */
	.camera-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: stretch;
		align-items: stretch;
		width: 100%;
		gap: 1.125rem;
	}

	.camera-preview {
		flex: 1;
		width: 100%;
		min-height: 0;
		background: linear-gradient(135deg, #E8E2DB 0%, #F0EDE8 100%);
		border-radius: 1rem;
		margin-bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
	}

	.camera-view {
		width: 96%;
		height: 90%;
		min-height: 60px;
		border-radius: 0.75rem;
		position: relative;
		top: 60px;
	}

	.photo-preview-container {
		flex: 1;
		width: 100%;
		min-height: 0;
		background: linear-gradient(135deg, #F5F3F0 0%, #E8E2DB 100%);
		border-radius: 0.75rem;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 0;
		box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
	}

	.photo-preview-img {
		width: 100%;
		height: 100%;
		border-radius: 0.75rem;
	}

	.photo-placeholder {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}

	.placeholder-text {
		color: #7FB3B3;
		font-size: 1.125rem;
		font-weight: 500;
	}

	/* Weight Display */
	.weight-display-container {
		margin: 2rem 0 1.5rem 0;
	}

	.weight-display {
		display: flex;
		align-items: baseline;
		justify-content: center;
	}

	.weight-num {
		font-size: 3rem;
		font-weight: 700;
		color: #7FB3B3;
		margin-right: 0.75rem;
	}

	.weight-unit {
		font-size: 1.5rem;
		color: #7FB3B3;
		font-weight: 500;
	}

	.weight-controls {
		display: flex;
		flex-direction: row;
		gap: 1.125rem;
		justify-content: center;
	}

	/* Buttons */
	.btn {
		border: none;
		border-radius: 0.75rem;
		font-size: 1rem;
		font-weight: 600;
		padding: 0 1.5rem;
		height: 3rem;
		margin: 0 0.25rem;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		cursor: pointer;
		transition: all 0.2s ease-out;
		font-family: 'Inter', system-ui, sans-serif;
	}

	.btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
	}

	.btn:active {
		transform: translateY(1px);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
	}

	.primary-btn {
		background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
		color: #2d3a4b;
	}

	.secondary-btn {
		background: linear-gradient(135deg, #7FB3B3 0%, #6B9B9B 100%);
		color: #FEFCFA;
	}

	.accent-btn {
		background: linear-gradient(135deg, #FFB8A3 0%, #FF9B85 100%);
		color: #2d3a4b;
	}

	.neutral-btn {
		background: linear-gradient(135deg, #E8E2DB 0%, #D8D2CB 100%);
		color: #2d3a4b;
	}

	.fetch-btn {
		width: 100%;
		margin-top: 0.5rem;
	}

	.photo-btn {
		width: 90%;
		height: 3rem;
		font-size: 1.125rem;
		position: absolute;
		left: 5%;
		bottom: 1.5rem;
	}

	.control-btn {
		min-width: 120px;
	}

	/* Footer */
	.footer-bar {
		width: 100%;
		background: linear-gradient(135deg, #FEFCFA 0%, #F0EDE8 100%);
		box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.12);
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		gap: 2rem;
		height: 80px;
		border-top-left-radius: 1.5rem;
		border-top-right-radius: 1.5rem;
		margin: 0 1rem;
		margin-bottom: 1rem;
	}

	.footer-btn {
		min-width: 180px;
		height: 3rem;
		font-size: 1.125rem;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.main-content {
			grid-template-columns: 1fr;
			gap: 1rem;
			padding: 1rem;
		}

		.nav-bar {
			padding: 0 1rem;
			margin: 0 0.5rem;
			margin-top: 0.5rem;
		}

		.footer-bar {
			gap: 1rem;
			padding: 0 1rem;
			margin: 0 0.5rem;
			margin-bottom: 0.5rem;
		}

		.footer-btn {
			min-width: 140px;
			font-size: 1rem;
		}
	}

	/* Focus States for Accessibility */
	.btn:focus {
		outline: 2px solid #A8E6CF;
		outline-offset: 2px;
	}

	.school-select-box:focus {
		outline: 2px solid #A8E6CF;
		outline-offset: 2px;
	}

	.recipe-card:focus {
		outline: 2px solid #A8E6CF;
		outline-offset: 2px;
	}
</style>