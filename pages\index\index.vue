<template>
    <view>

        <button @click="UTSgetUvcCamera">获取当前UVC设备列表</button>
        <button @click="checkAllUSBDevices">检查所有USB设备信息</button>
        <button @click="UTSopenUvcCameraX">打开UVC视频设备</button>
        <button @click="UTSgetUvcCameraImg(3141)">UVC设备拍照</button>
        <button @click="UTSclosedUvcCamera">关闭UVC设备拍照</button>
        <image :src="img" mode="aspectFill"></image>

        <view class="sssAD"></view>

    </view>
</template>

<script setup>
    import {
        onShow,
        onReady,
        onReachBottom,
        onPullDownRefresh,
        onShareAppMessage
    }
    from '@dcloudio/uni-app';
    import {
        getCurrentInstance,
        ref,
        reactive,
        inject,
        computed,
        onMounted
    } from 'vue';

    import {
        getUvcCamera,
        openUvcCamera,
        getUvcCameraImg,
        closedUvcCamera,
        clickUvcCameraImage
    } from "@/uni_modules/mushan-uvccamera";

    //获取当前设备的UVC列表
    /*
        返回参数 getDeviceName，getDeviceId，getProductId，getProductName，getVendorId
        保存好设备的getVendorId，用于打开UVC视频和拍照，自己去测试哪个是可打开的设备
    */
    let getVendorId = ref(null);
    function UTSgetUvcCamera() {
        console.log('=== 开始获取USB设备信息 ===');

        getUvcCamera((res) => {
            let resData = JSON.parse(res)
            console.log('=== UVC摄像头设备列表 ===');
            console.log('原始数据:', res);
            console.log('解析后数据:', resData);

            // 详细输出每个设备信息
            if (Array.isArray(resData)) {
                console.log(`找到 ${resData.length} 个UVC设备:`);
                resData.forEach((device, index) => {
                    console.log(`--- 设备 ${index + 1} ---`);
                    console.log(`设备名称: ${device.getDeviceName || '未知'}`);
                    console.log(`设备ID: ${device.getDeviceId || '未知'}`);
                    console.log(`产品ID: ${device.getProductId || '未知'}`);
                    console.log(`产品名称: ${device.getProductName || '未知'}`);
                    console.log(`供应商ID: ${device.getVendorId || '未知'}`);
                    console.log(`完整设备信息:`, device);
                });
            } else {
                console.log('UVC设备数据格式:', typeof resData);
                console.log('UVC设备内容:', resData);
            }

            // 输出系统USB设备信息（如果可用）
            logSystemUSBInfo();
        })
    }

    // 输出系统USB设备信息的辅助函数
    function logSystemUSBInfo() {
        console.log('=== 系统USB设备信息 ===');

        // 尝试获取系统信息
        uni.getSystemInfo({
            success: function(sysInfo) {
                console.log('系统信息:', {
                    platform: sysInfo.platform,
                    system: sysInfo.system,
                    version: sysInfo.version,
                    model: sysInfo.model,
                    brand: sysInfo.brand
                });
            },
            fail: function(err) {
                console.log('获取系统信息失败:', err);
            }
        });

        // 如果是App环境，尝试获取更多设备信息
        // #ifdef APP-PLUS
        console.log('当前运行环境: App');
        try {
            // 这里可以添加更多原生插件调用来获取USB设备信息
            console.log('可以在这里添加原生插件调用获取更详细的USB设备信息');
        } catch (error) {
            console.log('获取原生设备信息时出错:', error);
        }
        // #endif

        // #ifdef H5
        console.log('当前运行环境: H5');
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
            navigator.mediaDevices.enumerateDevices()
                .then(devices => {
                    console.log('H5媒体设备列表:');
                    devices.forEach((device, index) => {
                        console.log(`设备${index + 1}:`, {
                            kind: device.kind,
                            label: device.label,
                            deviceId: device.deviceId,
                            groupId: device.groupId
                        });
                    });
                })
                .catch(err => {
                    console.log('获取H5媒体设备失败:', err);
                });
        }
        // #endif
    }

    // 检查所有USB设备信息的综合函数
    function checkAllUSBDevices() {
        console.log('=== 开始全面检查USB设备 ===');
        console.log('时间:', new Date().toLocaleString());

        // 1. 首先获取UVC设备列表
        console.log('1. 获取UVC摄像头设备...');
        UTSgetUvcCamera();

        // 2. 获取系统信息
        console.log('2. 获取系统信息...');
        uni.getSystemInfo({
            success: function(sysInfo) {
                console.log('=== 系统详细信息 ===');
                console.log('平台:', sysInfo.platform);
                console.log('系统:', sysInfo.system);
                console.log('版本:', sysInfo.version);
                console.log('设备型号:', sysInfo.model);
                console.log('品牌:', sysInfo.brand);
                console.log('屏幕宽度:', sysInfo.screenWidth);
                console.log('屏幕高度:', sysInfo.screenHeight);
                console.log('状态栏高度:', sysInfo.statusBarHeight);
                console.log('完整系统信息:', sysInfo);
            },
            fail: function(err) {
                console.error('获取系统信息失败:', err);
            }
        });

        // 3. 尝试获取网络信息（可能包含设备连接状态）
        console.log('3. 获取网络信息...');
        uni.getNetworkType({
            success: function(netInfo) {
                console.log('=== 网络信息 ===');
                console.log('网络类型:', netInfo.networkType);
                console.log('完整网络信息:', netInfo);
            },
            fail: function(err) {
                console.error('获取网络信息失败:', err);
            }
        });

        // 4. 检查摄像头权限
        console.log('4. 检查摄像头权限...');
        // #ifdef APP-PLUS
        uni.authorize({
            scope: 'scope.camera',
            success: function() {
                console.log('摄像头权限: 已授权');
            },
            fail: function(err) {
                console.log('摄像头权限: 未授权或检查失败', err);
            }
        });
        // #endif

        // 5. 如果是H5环境，获取媒体设备
        // #ifdef H5
        console.log('5. H5环境 - 获取媒体设备...');
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
            navigator.mediaDevices.enumerateDevices()
                .then(devices => {
                    console.log('=== H5媒体设备详细列表 ===');
                    console.log(`找到 ${devices.length} 个媒体设备:`);

                    const videoDevices = devices.filter(device => device.kind === 'videoinput');
                    const audioDevices = devices.filter(device => device.kind === 'audioinput');
                    const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

                    console.log(`视频输入设备: ${videoDevices.length} 个`);
                    console.log(`音频输入设备: ${audioDevices.length} 个`);
                    console.log(`音频输出设备: ${audioOutputs.length} 个`);

                    devices.forEach((device, index) => {
                        console.log(`--- 媒体设备 ${index + 1} ---`);
                        console.log(`类型: ${device.kind}`);
                        console.log(`标签: ${device.label || '未知设备'}`);
                        console.log(`设备ID: ${device.deviceId}`);
                        console.log(`组ID: ${device.groupId}`);
                        console.log('完整设备信息:', device);
                    });
                })
                .catch(err => {
                    console.error('获取H5媒体设备失败:', err);
                });
        } else {
            console.log('H5环境不支持媒体设备枚举');
        }
        // #endif

        // 6. 输出当前已知的摄像头设备ID
        console.log('6. 当前代码中使用的摄像头设备ID...');
        console.log('代码中硬编码的vendorId: 3141');
        console.log('建议: 使用UTSgetUvcCamera()获取的实际vendorId');

        console.log('=== USB设备检查完成 ===');
    }

    //打开对应的UVC设备
    //简单使用案例
    //vendorId 通过getUvcCamera获取的列表里拿到;
    //widthView 预览显示的宽，按设备整体宽度百分比1为设备的宽。0.5为一半宽
    //heightView 预览显示的高，按设备整体高度百分比1为设备的高。0.5为一半高
    //topMargin 预览与顶部的距离，1为整个设备的高距离，0.5为一半
    //leftMargin 预览与左边的距离，1为整个设备的宽距离，0.5为一半
    //quirkFixBandwidth 是否设置UVC固定带宽模式，为了支持同时一个页面打开多个摄像设备，不是所有的设备都支持该模式

    /**
    textData 为文本数组的显示，不需要时 textData:[] 为空
    textData:[
        {
            content:'文本1',   //文本内容
            textSize:16,    //文本字体
            textColor:'#DC143C',  //文本颜色
            topMargin:50,       //与预览显示的头部距离
            leftMargin:50,      //与预览显示的左边距离
        },
    ]
    **/

    /**
    imageData 为图片数组的显示，不需要时 imageData:[] 为空 
    imageData:[
        {
            url:'static/logo.png',   //图片地址，在static的目录下的图片
            width:50,       //显示的图片宽度
            height:50,      //显示的图片高度
            topMargin:50,  //与预览显示的头部距离
            leftMargin:100,  //与预览显示的左边距离
        },
    ]
    **/

    async function UTSopenUvcCameraS() {
        await UTSopenUvcCamera(3141);
        // await UTSopenUvcCamera(3141);
    }
    async function UTSopenUvcCamera(vendorId) {
        return new Promise((resolve, reject) => {

            let parame = {
                textData:[
                    {
                        content:'文本1',
                        textSize:16,
                        textColor:'#DC143C',
                        topMargin:30,
                        leftMargin:50,
                    },
                    {
                        content:'文本2',
                        textSize:16,
                        textColor:'#DC143C',
                        topMargin:30,
                        leftMargin:100,
                    }
                ],
                imageData:[
                    {
                        url:'static/logo.png',
                        width:50,
                        height:50,
                        topMargin:50,
                        leftMargin:50,
                    },
                    {
                        url:'static/logo.png',
                        width:50,
                        height:50,
                        topMargin:100,
                        leftMargin:200,
                    }
                ],
                vendorId: vendorId,
                widthView: 0.5,
                heightView: 0.5,
                topMargin: 0.5,
                leftMargin: 0,
                quirkFixBandwidth:false,
            }
            openUvcCamera(parame, res => {
                let resData = JSON.parse(res)
                console.log(resData)
                if (resData.msg == 'ok') {

                    //parame的imageData中第1张图片点击的触发
                    clickUvcCameraImage({
                        vendorId: vendorId,
                        imageNum: 0,
                    },(res) => {
                        console.log('点击了第1张图片');
                    })

                    //parame的imageData中第2张图片点击的触发
                    clickUvcCameraImage({
                        vendorId: vendorId,
                        imageNum: 1,
                    },(res) => {
                        console.log('点击了第2张图片');
                    })

                    resolve()
                }else if (resData.msg == '用户拒绝了部分权限') {
                    //这里是拒绝了授权权限的返回
                    UTSopenUvcCamera()
                }
            })
        })
    }

    //根据class使用案例
    //这里是根据某个class获取到view的位置，然后获取到设备宽高大小比例等等可以把预览设定到指定页面
    //假设有某个<view class="cameraClass"></view>可以根据以下获取
    async function UTSopenUvcCameraX() {
        console.log('=== 开始打开UVC摄像头 ===');

        return new Promise((resolve, reject) => {
            uni.getSystemInfo({
                success: function(res) {
                    console.log('屏幕信息:', {
                        screenWidth: res.screenWidth,
                        screenHeight: res.screenHeight
                    });

                    let screenWidth = res.screenWidth
                    let screenHeight = res.screenHeight
                    const query = uni.createSelectorQuery();
                    query.select('.sssAD').boundingClientRect().exec((ret) => {

                        if (!ret || !ret[0]) {
                            console.error('❌ 无法获取预览区域(.sssAD)的位置信息');
                            reject(new Error('无法获取预览区域位置'));
                            return;
                        }

                        let vWidth = (ret[0].width / screenWidth).toFixed(2)
                        let vHeight = (ret[0].height / screenHeight).toFixed(2)
                        let vTop = (ret[0].top / screenHeight).toFixed(2)
                        let vLeft = (ret[0].left / screenWidth).toFixed(2)

                        console.log('预览区域计算:', {
                            原始宽度: ret[0].width,
                            原始高度: ret[0].height,
                            原始top: ret[0].top,
                            原始left: ret[0].left,
                            计算后宽度比例: vWidth,
                            计算后高度比例: vHeight,
                            计算后top比例: vTop,
                            计算后left比例: vLeft
                        });

                        let parame = {
                            textData:[],
                            imageData:[],
                            vendorId: 3141,
                            widthView: vWidth * 1,
                            heightView: vHeight * 1,
                            topMargin: vTop * 1,
                            leftMargin: vLeft * 1,
                            quirkFixBandwidth:false,
                        }

                        console.log('打开摄像头参数:', parame);

                        openUvcCamera(parame, res => {
                            console.log('摄像头打开原始返回:', res);

                            try {
                                let resData = JSON.parse(res)
                                console.log('摄像头打开解析后数据:', resData);

                                if (resData.msg == 'ok') {
                                    console.log('✅ 摄像头打开成功!');
                                    resolve(resData);
                                } else if (resData.msg == '用户拒绝了部分权限') {
                                    console.log('⚠️ 用户拒绝了权限，尝试重新打开...');
                                    UTSopenUvcCamera(3141);
                                    resolve();
                                } else {
                                    console.error('❌ 摄像头打开失败:', resData.msg);
                                    reject(new Error(resData.msg || '摄像头打开失败'));
                                }
                            } catch (error) {
                                console.error('❌ 解析摄像头返回数据失败:', error);
                                console.error('原始数据:', res);
                                reject(error);
                            }
                        })

                    })
                },
                fail: function(err) {
                    console.error('❌ 获取系统信息失败:', err);
                    reject(err);
                }
            });

        })
    }

    //获取uvc设备拍照图片
    /*
        vendorId 通过getUvcCamera获取的列表里拿到;
        someQuality 拍照的图片质量 0 - 100
        isBase64 是否返回base64数据，不返回填false

        返回值 base64 和 图片路径file
    */
    let img = ref('');
    async function UTSgetUvcCameraImg(vendorId){
        console.log(`=== 开始拍照 ===`);
        console.log(`使用的vendorId: ${vendorId}`);

        return new Promise((resolve, reject) => {
            let parame = {
                vendorId:vendorId,
                someQuality:40,
                isBase64:true,
            }

            console.log('拍照参数:', parame);

            getUvcCameraImg(parame,(res)=>{
                console.log('拍照原始返回:', res);

                try {
                    let resData = JSON.parse(res)
                    console.log('拍照解析后数据:', resData);

                    if(resData.msg == 'ok'){
                        console.log('✅ 拍照成功!');
                        console.log('图片文件路径:', resData.file);
                        console.log('Base64数据长度:', resData.base64 ? resData.base64.length : 0);
                        img.value = filterBase64(resData.base64);
                        resolve(resData);
                    } else {
                        console.error('❌ 拍照失败:', resData.msg);
                        console.error('错误详情:', resData);
                        reject(new Error(resData.msg || '拍照失败'));
                    }
                } catch (error) {
                    console.error('❌ 解析拍照返回数据失败:', error);
                    console.error('原始数据:', res);
                    reject(error);
                }
            })
        })
    }
	
	
	
	
    function filterBase64(codeImages) {
        return codeImages.replace(/[\r\n]/g, "");
    }

    //关闭摄像头
    async function UTSclosedUvcCamera(){
        await reClosedCamera(3141);
        // await reClosedCamera(3141);
    }
    function reClosedCamera(vendorId){
        return new Promise((resolve, reject) => {
            closedUvcCamera(vendorId,(res)=>{
                let resData = JSON.parse(res);
                if(resData.msg == 'ok'){
                    resolve();
                }
            })
        })
    }

</script>

<style lang="scss" scoped>
    .sssAD{
        width: 400px;
        height: 400px;
        position: absolute;
        background-color: red;
        top: 200px;
        left: 400px;
    }
</style>