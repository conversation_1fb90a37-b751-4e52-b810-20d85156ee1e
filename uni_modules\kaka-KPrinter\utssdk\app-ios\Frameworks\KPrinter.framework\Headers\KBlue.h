//
//  KBlue.h
//  KPrinter
//
//  Created by kaka on 12/14/24.
//

#import <Foundation/Foundation.h>
#import <CoreBluetooth/CoreBluetooth.h>

@protocol KBlueDelegate <NSObject>

@optional
- (void)deviceDidDiscover:(CBPeripheral *_Nonnull)peripheral;

@optional
- (void)connectStateDidChange:(NSDictionary *_Nonnull)dict; 

@optional
- (void)onReceive:(NSData *_Nullable)data;

@optional
- (void)blueStateChange:(BOOL)isOn;

@optional
- (void)onWriteComplete:(BOOL)isComplete;

@end


@interface KBlue : NSObject

@property (nonatomic, weak) id<KBlueDelegate> delegate;

- (void)configure:(NSString *)str;

- (void)datagramSize:(int)size;

- (BOOL)isConnect;

- (void)startScan;

- (void)stopScan;

- (void)connect:(NSString *_Nullable)deviceId;

- (void)disconnect;

- (void)writeCmd;

- (void)pelar;

@end


