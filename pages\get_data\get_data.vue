<template>
  <view class="container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <text class="title">数据查询</text>
      <view class="actions">
        <button class="btn refresh" @click="loadData">刷新</button>
      </view>
    </view>

    <!-- 筛选条件 -->
    <view class="filter-bar">
      <input
        v-model="filters.recipe"
        class="filter-input"
        placeholder="配方名"
      />
      <picker mode="date" :value="filters.date" @change="onDateChange">
        <view class="date-picker">
          {{ filters.dateLabel || '提交日期' }}
        </view>
      </picker>
      <button class="btn query" @click="applyFilters">查询</button>
    </view>

    <!-- 数据展示卡片 -->
    <scroll-view class="card-list">
      <view
        v-for="(record, idx) in displayed"
        :key="idx"
        class="card"
      >
        <view class="card-header" @click="toggleDetails(idx)">
          <text>配方：{{ record.food }}</text>
          <text>重量：{{ record.weight }} Kg</text>
          <text>提交时间：{{ record.time }}</text>
          <text class="toggle-icon">
            {{ isExpanded(idx) ? '收起 ▲' : '展开 ▼' }}
          </text>
        </view>
        <!-- 可展开详情：展示两张缩略图 -->
        <view v-if="isExpanded(idx)" class="detail-wrapper">
          <text class="detail-label">照片1：</text>
          <image
            v-if="record.imagePath1"
            :src="record.imagePath1"
            mode="aspectFill"
            class="detail-image"
          />
          <text v-else class="no-image">无照片1</text>
          <text class="detail-label">照片2：</text>
          <image
            v-if="record.imagePath2"
            :src="record.imagePath2"
            mode="aspectFill"
            class="detail-image"
          />
          <text v-else class="no-image">无照片2</text>
        </view>
      </view>
    </scroll-view>

    <!-- 无数据提示 -->
    <view v-if="displayed.length === 0" class="empty-state">
      <text class="empty-text">暂无符合条件的数据</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      records: [],
      displayed: [],
      filters: {
        recipe: '',
        date: '',
        dateLabel: ''
      },
      expandedIndices: {}
    };
  },
  methods: {
    loadData() {
      let data = uni.getStorageSync('submissionRecords') || [];
      try {
        data = typeof data === 'string' ? JSON.parse(data) : data;
      } catch {
        data = [];
      }
      this.records = Array.isArray(data) ? data : [];
      this.displayed = this.records;
      this.expandedIndices = {};
      uni.showToast({ title: '数据已加载', icon: 'success' });
    },
    onDateChange(e) {
      const val = e.detail.value; // 格式 "YYYY-MM-DD"
      this.filters.date = val;
      const [y, m, d] = val.split('-');
      this.filters.dateLabel = `${y}-${Number(m)}-${Number(d)}`;
      // 这里 dateLabel 形式与 record.time 的开头格式相匹配
    },
    applyFilters() {
      this.displayed = this.records.filter((r) => {
        const matchRecipe =
          this.filters.recipe === '' || r.food.includes(this.filters.recipe);
        let matchDate = true;
        if (this.filters.date !== '') {
          // record.time 格式如 "2025-6-5 8:53:12"，取开头日期部分
          const datePart = r.time.split(' ')[0];
          matchDate = datePart === this.filters.dateLabel;
        }
        return matchRecipe && matchDate;
      });
      this.expandedIndices = {};
    },
    toggleDetails(idx) {
      this.$set(this.expandedIndices, idx, !this.expandedIndices[idx]);
    },
    isExpanded(idx) {
      return !!this.expandedIndices[idx];
    }
  },
  onLoad() {
    this.loadData();
  }
};
</script>

<style scoped>
.container {
  flex: 1;
  background: #f5f7fa;
  padding: 16px;
}

.nav-bar {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  display: flex;
  margin-bottom: 12px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.actions .btn.refresh {
  background: #50e3c2;
  color: #fff;
  padding: 8px 16px;
  border-radius: 12px;
}

.filter-bar {
  flex-direction: row;
  align-items: center;
  display: flex;
  margin-bottom: 16px;
}

.filter-input {
  flex: 1;
  height: 36px;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 0 12px;
  margin-right: 8px;
  background: #fff;
}

.date-picker {
  height: 36px;
  line-height: 36px;
  padding: 0 12px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-right: 8px;
}

.btn.query {
  background: #0052cc;
  color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
}

.card-list {
  flex: 1;
}

.card {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.card-header {
  flex-direction: row;
  justify-content: space-between;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
}

.toggle-icon {
  font-size: 12px;
  color: #666;
}

.detail-wrapper {
  padding-top: 8px;
}

.detail-label {
  font-size: 14px;
  color: #555;
  margin-top: 4px;
}

.detail-image {
  width: 100%;
  height: 200px;
  margin-top: 4px;
  border-radius: 8px;
}

.no-image {
  font-size: 14px;
  color: #999;
  margin-bottom: 8px;
}

.empty-state {
  margin-top: 20px;
  align-items: center;
  display: flex;
  justify-content: center;
}

.empty-text {
  color: #999;
  font-size: 16px;
}
</style>
