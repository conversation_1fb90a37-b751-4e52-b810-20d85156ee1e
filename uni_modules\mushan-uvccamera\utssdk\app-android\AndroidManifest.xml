<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools" 
  package="io.dcloud.uni_modules.muhsan_uvccamera">
	<uses-feature android:name="android.hardware.usb.host" />
	<uses-permission android:name="android.permission.USB_PERMISSION" />
	<!--创建前台服务权限-->
	<uses-permission android:name="android.permission.CAMERA" />
	<!-- 添加写入外部存储权限 -->
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	
	<application>
		<service android:name="uts.sdk.modules.mushanUvccamera.usbReceiver" />
	</application>
	
</manifest>
