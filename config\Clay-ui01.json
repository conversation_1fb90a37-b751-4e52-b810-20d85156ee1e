{"designSystem": {"name": "3D Soft Dashboard Design System", "version": "1.0.0", "description": "A modern 3D dashboard design system with soft, tactile interface elements", "colorPalette": {"primary": {"mint": "#A8E6CF", "sage": "#88C999", "seafoam": "#7FB3B3"}, "neutral": {"cream": "#F5F3F0", "warmWhite": "#FEFCFA", "softBeige": "#F0EDE8", "lightTan": "#E8E2DB"}, "accent": {"peach": "#FFB8A3", "coral": "#FF9B85", "blush": "#F7C6B7"}, "background": {"primary": "#F5F3F0", "secondary": "#FEFCFA"}}, "typography": {"fontFamily": {"primary": "Inter, system-ui, sans-serif", "secondary": "SF Pro Display, -apple-system, sans-serif"}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem"}, "fontWeight": {"light": "300", "regular": "400", "medium": "500", "semibold": "600", "bold": "700"}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "borderRadius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "2xl": "1.5rem", "3xl": "2rem"}, "shadows": {"soft": "0 2px 8px rgba(0, 0, 0, 0.08)", "medium": "0 4px 16px rgba(0, 0, 0, 0.12)", "elevated": "0 8px 32px rgba(0, 0, 0, 0.15)", "inset": "inset 0 2px 4px rgba(0, 0, 0, 0.06)"}, "layoutStructure": {"containerType": "grid", "gridSystem": {"columns": "repeat(auto-fit, minmax(200px, 1fr))", "gap": "1.5rem", "padding": "2rem"}, "cardLayout": {"aspectRatio": "variable", "minHeight": "120px", "maxHeight": "300px"}}, "componentTypes": {"statsCard": {"description": "Display key metrics with large numbers", "structure": {"header": "Optional title", "value": "Large numeric display", "subtitle": "Optional description or trend"}, "visualStyle": "Clean, minimal with emphasis on the main metric"}, "chartCard": {"description": "Contains data visualizations", "structure": {"header": "Chart title", "chart": "Bar, line, or area chart", "legend": "Optional chart legend"}, "chartTypes": ["bar", "line", "area", "donut"]}, "progressCard": {"description": "Shows progress indicators or completion status", "structure": {"header": "Progress title", "indicator": "Progress bar or circular progress", "percentage": "Completion percentage"}}, "controlCard": {"description": "Interactive controls and settings", "structure": {"header": "Control title", "controls": "Buttons, toggles, or sliders", "status": "Current state indicator"}}, "listCard": {"description": "Display lists of items or menu options", "structure": {"header": "List title", "items": "List items with optional icons", "actions": "Optional item actions"}}}, "3dEffects": {"cardElevation": {"default": "2-4px raised appearance", "hover": "4-8px elevated state", "active": "1-2px pressed state"}, "surfaceTexture": {"description": "Soft, matte finish with subtle depth", "implementation": "Use gradients and shadows to create depth"}, "beveling": {"description": "Subtle rounded edges with soft highlights", "borderRadius": "0.75rem to 1.5rem", "highlight": "Subtle inner light reflection"}}, "interactionStates": {"hover": {"transform": "translateY(-2px)", "shadow": "Increase elevation", "timing": "0.2s ease-out"}, "active": {"transform": "translateY(1px)", "shadow": "Reduce elevation", "timing": "0.1s ease-in"}, "focus": {"outline": "2px solid mint color", "outlineOffset": "2px"}}, "designPrinciples": {"tactileDesign": "Elements should feel touchable and physical", "softMinimalism": "Clean design with warm, approachable colors", "consistentElevation": "Maintain visual hierarchy through consistent depth", "subtleAnimations": "Gentle transitions that enhance user experience", "accessibleContrast": "Ensure sufficient contrast while maintaining softness"}, "implementationGuidelines": {"cssApproach": {"boxShadow": "Use multiple shadow layers for depth", "background": "Linear gradients for subtle surface variation", "borderRadius": "Consistent rounded corners throughout", "transitions": "Smooth animations for state changes"}, "gridImplementation": {"container": "CSS Grid with auto-fit columns", "responsiveness": "Minimum 200px column width", "gaps": "Consistent spacing between cards"}, "colorUsage": {"backgrounds": "Primarily neutral cream/beige tones", "accents": "Mint and sage for interactive elements", "highlights": "Peach and coral for emphasis"}}, "responsiveBreakpoints": {"mobile": "320px - 768px", "tablet": "768px - 1024px", "desktop": "1024px+"}, "accessibility": {"colorContrast": "WCAG AA compliant", "focusIndicators": "Visible focus states", "keyboardNavigation": "Full keyboard accessibility", "screenReader": "Proper ARIA labels and semantic HTML"}}}