//
//  EscCmd.h
//  KPrinter
//
//  Created by kaka on 1/15/25.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface EscCmd : NSObject

- (void)addCommand:(NSString *)command;

/// 打印机初始化，必须是第一个
- (void)initializePrinter;

- (void)justification:(NSString *)p;

- (void)setCharcterSize:(NSString *)str;

- (void)turnEmphasizedMode:(BOOL)on;

/// 换行
- (void)newLine;

/// 设置打印宽度
- (void)printingAreaWidth:(int)width;

/// 设置行间距
- (void)lineSpacing:(int)space;

/// 打印并且走纸多少行，默认为8行，打印完内容后发送
- (void)printAndFeedLines:(int)feedLines;

/// 切纸
- (void)cutPaper;

/// 蜂鸣器
- (void)sound:(NSString *)str;

/// 图片
- (void)image:(NSString *)str;

/// 二维码
- (void)qrCode:(NSString *)str;

/// 条码
- (void)barCode:(NSString *)str;

/// 文本
- (void)text:(NSString *)str;

- (NSString *)escTwoText58:(NSString *)str;

- (NSString *)escThreeText58:(NSString *)str;

- (NSString *)escFourText58:(NSString *)str;

- (NSString *)escTwoText80:(NSString *)str;

- (NSString *)escThreeText80:(NSString *)str;

- (NSString *)escFourText80:(NSString *)str;

@end

NS_ASSUME_NONNULL_END
