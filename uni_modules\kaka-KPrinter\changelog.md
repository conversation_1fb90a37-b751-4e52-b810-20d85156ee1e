## 1.3.5（2025-06-30）
- 优化已知问题;
## 1.3.4（2025-06-16）
- 安卓端扫描设备区分已配对/未配对;
## 1.3.3（2025-06-14）
- 图片打印添加抖动效果;
## 1.3.2（2025-05-28）
- 优化 Tsc/Tspl 图片打印;
## 1.3.1（2025-05-16）
- 添加 CPCL 对齐、矩形指令;
- 调整 ESC 文字缩放指令;
## 1.3.0（2025-05-08）
- 添加 Android、iOS 以太网打印功能;
- 支持 iOS 端设置分包大小;
- 优化某些机型 TSC 图片下发速度;
## 1.2.3（2025-04-29）
- 优化某些机型 CPCL 图片下发速度;
## 1.2.2（2025-04-22）
- 优化示例程序;
## 1.2.1（2025-04-22）
- 新增支持写入自定义 ESC 字符串、字节数组指令;
- 新增支持写入自定义 TSPL 字符串、字节数组指令;
- 新增支持写入自定义 CPCL 字符串、字节数组指令;
## 1.2.0（2025-04-21）
- 优化 Android USB 打印;
- 调整 iOS ESC 指令;
- 添加 TSPL 段落、区域反白等指令;
## 1.1.3（2025-04-16）
- 调整 TSPL 指令;
## 1.1.2（2025-04-16）
- 调整 ESC 指令;
- 添加 USB 打印;
## 1.1.1（2025-04-14）
- 添加一些 ESC 指令;
## 1.1.0（2025-04-08）
- 优化安卓端;
## 1.0.9（2025-03-24）
- 优化安卓端;
## 1.0.8（2025-03-17）
- 添加一些 tsc/tspl 指令;
- 优化 Android 权限请求;
## 1.0.7（2025-03-11）
- 添加 Android stopScan 方法;
## 1.0.6（2025-02-11）
- 修复 iOS 蓝牙连接;
- 优化示例项目;
## 1.0.5（2025-02-07）
- 修复 iOS 数据写入bug;
## 1.0.4（2025-01-23）
- BlueDevice 添加 isConnect 属性;
- 添加数据写入是否完成监听 onWriteComplete;
- 添加 uvue demo(见readme)、优化示例项目;
## 1.0.3（2025-01-16）
- 修复部分安卓手机扫描不出设备问题;
- 优化示例项目;
## 1.0.2（2025-01-15）
- 添加 ESC 指令;
## 1.0.1（2025-01-13）
- iOS蓝牙;
- TSC/TSPL CPCL指令完善;
## 1.0.0（2025-01-08）
- 安卓蓝牙;
- TSC/TSPL CPCL指令支持;
