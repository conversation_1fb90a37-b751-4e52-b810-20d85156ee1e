
const { registerUTSInterface, initUTSProxyClass, initUTSProxyFunction, initUTSPackageName, initUTSIndexClassName, initUTSClassName } = uni
const name = 'mushanUvccamera'
const moduleName = 'UVCAndroid设备拍照，usb摄像头外接设备预览和拍照，返回路径和base64'
const moduleType = ''
const errMsg = ``
const is_uni_modules = true
const pkg = /*#__PURE__*/ initUTSPackageName(name, is_uni_modules)
const cls = /*#__PURE__*/ initUTSIndexClassName(name, is_uni_modules)

const exports = { __esModule: true }



exports.myApi = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'myApiByJs', keepAlive: false, params: [{"name":"options","type":"MyApiOptions"}], return: ""})
exports.myApiSync = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'myApiSyncByJs', keepAlive: false, params: [{"name":"paramA","type":"boolean"}], return: ""})
exports.clickUvcCameraImage = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'clickUvcCameraImageByJs', keepAlive: false, params: [{"name":"parameData","type":"UTSJSONObject"},{"name":"callback","type":"UTSCallback"}], return: ""})
exports.openUvcCamera = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'openUvcCameraByJs', keepAlive: false, params: [{"name":"parameData","type":"UTSJSONObject"},{"name":"callback","type":"UTSCallback"}], return: ""})
exports.getUvcCameraImg = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'getUvcCameraImgByJs', keepAlive: false, params: [{"name":"parameData","type":"UTSJSONObject"},{"name":"callback","type":"UTSCallback"}], return: ""})
exports.closedUvcCamera = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'closedUvcCameraByJs', keepAlive: false, params: [{"name":"vendorIdx","type":"Int"},{"name":"callback","type":"UTSCallback"}], return: ""})
exports.getUvcCamera = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'getUvcCameraByJs', keepAlive: false, params: [{"name":"callback","type":"UTSCallback"}], return: ""})
uni.registerUTSPlugin('uni_modules/mushan-uvccamera', exports)
