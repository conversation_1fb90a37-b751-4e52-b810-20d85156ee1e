if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((r=>t.resolve(e()).then((()=>r))),(r=>t.resolve(e()).then((()=>{throw r}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>Int64<PERSON>rray,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";const t=(e,t)=>{const r=e.__vccOpts||e;for(const[a,n]of t)r[a]=n;return r};const r=t({data:()=>({username:"user01",password:"123456",users:[],rememberMe:!1,usernameFocus:!1,passwordFocus:!1}),onLoad(){const e=uni.getStorageSync("users");Array.isArray(e)&&(this.users=e)},methods:{onLogin(){if(this.username&&this.password)return"admin"===this.username&&"admin"===this.password?(uni.showToast({title:"上传学校信息",icon:"success"}),void uni.navigateTo({url:"/pages/school_updata/school_updata"})):"updata_food"===this.username&&"123456"===this.password?(uni.showToast({title:"上传食谱信息",icon:"success"}),void uni.navigateTo({url:"/pages/food_updata/food_updata"})):void("user01"===this.username&&"123456"===this.password?(uni.showToast({title:"用户登录成功",icon:"success"}),uni.navigateTo({url:"/pages/Module/Module"})):uni.showToast({title:"账号或密码错误",icon:"none"}));uni.showToast({title:"请输入账号和密码",icon:"none"})},onForgot(){uni.showToast({title:"请联系管理员重置密码",icon:"none"})},onRegister(){uni.showToast({title:"请联系管理员注册",icon:"none"})},onFocus(e){"username"===e&&(this.usernameFocus=!0),"password"===e&&(this.passwordFocus=!0)},onBlur(e){"username"===e&&(this.usernameFocus=!1),"password"===e&&(this.passwordFocus=!1)}}},[["render",function(t,r,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"background-gradient"}),e.createElementVNode("view",{class:"login-card"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"logo-area"},[e.createElementVNode("view",{class:"logo-icon"},[e.createElementVNode("text",{class:"logo-text"},"首衡")])]),e.createElementVNode("text",{class:"app-title"},"称重系统"),e.createElementVNode("text",{class:"app-subtitle"},"欢迎回来，请登录您的账户")]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("view",{class:"input-field"},[e.createElementVNode("view",{class:"input-icon"},[e.createElementVNode("text",{class:"icon"},"👤")]),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[0]||(r[0]=e=>s.username=e),type:"text",placeholder:"请输入账号",class:"form-input",onBlur:r[1]||(r[1]=e=>i.onBlur("username")),onFocus:r[2]||(r[2]=e=>i.onFocus("username"))},null,544),[[e.vModelText,s.username,void 0,{trim:!0}]])])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("view",{class:"input-field"},[e.createElementVNode("view",{class:"input-icon"},[e.createElementVNode("text",{class:"icon"},"🔒")]),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[3]||(r[3]=e=>s.password=e),type:"password",placeholder:"请输入密码",class:"form-input",onBlur:r[4]||(r[4]=e=>i.onBlur("password")),onFocus:r[5]||(r[5]=e=>i.onFocus("password"))},null,544),[[e.vModelText,s.password,void 0,{trim:!0}]])])]),e.createElementVNode("view",{class:"options-row"},[e.createElementVNode("view",{class:"checkbox-group"},[e.createElementVNode("label",{class:"checkbox-label"},[e.withDirectives(e.createElementVNode("input",{type:"checkbox","onUpdate:modelValue":r[6]||(r[6]=e=>s.rememberMe=e),class:"checkbox-input"},null,512),[[e.vModelCheckbox,s.rememberMe]]),e.createElementVNode("view",{class:"checkbox-custom"}),e.createElementVNode("text",{class:"checkbox-text"},"记住我")])]),e.createElementVNode("text",{class:"forgot-link",onClick:r[7]||(r[7]=(...e)=>i.onForgot&&i.onForgot(...e))},"忘记密码？")]),e.createElementVNode("button",{class:"login-button",onClick:r[8]||(r[8]=(...e)=>i.onLogin&&i.onLogin(...e))},[e.createElementVNode("text",{class:"button-text"},"登录")]),e.createElementVNode("view",{class:"register-section"},[e.createElementVNode("text",{class:"register-text"},"还没有账户？"),e.createElementVNode("text",{class:"register-link",onClick:r[9]||(r[9]=(...e)=>i.onRegister&&i.onRegister(...e))},"立即注册")])])])])}],["__scopeId","data-v-0b3b3708"]]);function a(e){return weex.requireModule(e)}function n(e,t,...r){uni.__log__?uni.__log__(e,t,...r):console[e].apply(console,[...r,t])}const s=uni.requireUTSPlugin("uni_modules/mushan-uvccamera"),i=t({__name:"test",setup(t){function r(){s.getUvcCamera((e=>{n("log","at pages/text/test.vue:50",JSON.parse(e))}))}async function a(e){return new Promise(((t,r)=>{let i={textData:[{content:"文本1",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:50},{content:"文本2",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:100}],imageData:[{url:"static/logo.png",width:50,height:50,topMargin:50,leftMargin:50},{url:"static/logo.png",width:50,height:50,topMargin:100,leftMargin:200}],vendorId:e,widthView:.5,heightView:.5,topMargin:.5,leftMargin:0,quirkFixBandwidth:!1};s.openUvcCamera(i,(r=>{let i=JSON.parse(r);n("log","at pages/text/test.vue:138",i),"ok"==i.msg?(s.clickUvcCameraImage({vendorId:e,imageNum:0},(e=>{n("log","at pages/text/test.vue:146","点击了第1张图片")})),s.clickUvcCameraImage({vendorId:e,imageNum:1},(e=>{n("log","at pages/text/test.vue:154","点击了第2张图片")})),t()):"用户拒绝了部分权限"==i.msg&&a()}))}))}async function i(){return new Promise(((e,t)=>{uni.getSystemInfo({success:function(t){let r=t.screenWidth,i=t.screenHeight;uni.createSelectorQuery().select(".sssAD").boundingClientRect().exec((t=>{let c={textData:[],imageData:[],vendorId:3141,widthView:1*(t[0].width/r).toFixed(2),heightView:1*(t[0].height/i).toFixed(2),topMargin:1*(t[0].top/i).toFixed(2),leftMargin:1*(t[0].left/r).toFixed(2),quirkFixBandwidth:!1};s.openUvcCamera(c,(t=>{let r=JSON.parse(t);n("log","at pages/text/test.vue:195",r),"ok"==r.msg?e():"用户拒绝了部分权限"==r.msg&&a(),e()}))}))}})}))}e.ref(null);let c=e.ref("");async function o(){var e;await(e=3034,new Promise(((t,r)=>{s.closedUvcCamera(e,(e=>{"ok"==JSON.parse(e).msg&&t()}))})))}return(t,a)=>(e.openBlock(),e.createElementBlock("view",null,[e.createElementVNode("button",{onClick:r},"获取当前UVC设备列表"),e.createElementVNode("button",{onClick:i},"打开UVC视频设备"),e.createElementVNode("button",{onClick:a[0]||(a[0]=e=>async function(e){return new Promise(((t,r)=>{let a={vendorId:e,someQuality:40,isBase64:!0};s.getUvcCameraImg(a,(e=>{let t=JSON.parse(e);"ok"==t.msg&&(n("log","at pages/text/test.vue:230",t),n("log","at pages/text/test.vue:231",t.file),c.value=t.base64.replace(/[\r\n]/g,""))}))}))}(4487))},"UVC设备拍照"),e.createElementVNode("button",{onClick:o},"关闭UVC设备拍照"),e.createElementVNode("image",{src:e.unref(c),mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"sssAD"})]))}},[["__scopeId","data-v-09b72bfd"]]),c=t({__name:"index",setup(t){function r(){s.getUvcCamera((e=>{n("log","at pages/index/index.vue:50",JSON.parse(e))}))}async function a(e){return new Promise(((t,r)=>{let i={textData:[{content:"文本1",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:50},{content:"文本2",textSize:16,textColor:"#DC143C",topMargin:30,leftMargin:100}],imageData:[{url:"static/logo.png",width:50,height:50,topMargin:50,leftMargin:50},{url:"static/logo.png",width:50,height:50,topMargin:100,leftMargin:200}],vendorId:e,widthView:.5,heightView:.5,topMargin:.5,leftMargin:0,quirkFixBandwidth:!1};s.openUvcCamera(i,(r=>{let i=JSON.parse(r);n("log","at pages/index/index.vue:138",i),"ok"==i.msg?(s.clickUvcCameraImage({vendorId:e,imageNum:0},(e=>{n("log","at pages/index/index.vue:146","点击了第1张图片")})),s.clickUvcCameraImage({vendorId:e,imageNum:1},(e=>{n("log","at pages/index/index.vue:154","点击了第2张图片")})),t()):"用户拒绝了部分权限"==i.msg&&a()}))}))}async function i(){return new Promise(((e,t)=>{uni.getSystemInfo({success:function(t){let r=t.screenWidth,i=t.screenHeight;uni.createSelectorQuery().select(".sssAD").boundingClientRect().exec((t=>{let c={textData:[],imageData:[],vendorId:3141,widthView:1*(t[0].width/r).toFixed(2),heightView:1*(t[0].height/i).toFixed(2),topMargin:1*(t[0].top/i).toFixed(2),leftMargin:1*(t[0].left/r).toFixed(2),quirkFixBandwidth:!1};s.openUvcCamera(c,(t=>{let r=JSON.parse(t);n("log","at pages/index/index.vue:195",r),"ok"==r.msg?e():"用户拒绝了部分权限"==r.msg&&a(),e()}))}))}})}))}e.ref(null);let c=e.ref("");async function o(){var e;await(e=3141,new Promise(((t,r)=>{s.closedUvcCamera(e,(e=>{"ok"==JSON.parse(e).msg&&t()}))})))}return(t,a)=>(e.openBlock(),e.createElementBlock("view",null,[e.createElementVNode("button",{onClick:r},"获取当前UVC设备列表"),e.createElementVNode("button",{onClick:i},"打开UVC视频设备"),e.createElementVNode("button",{onClick:a[0]||(a[0]=e=>async function(e){return new Promise(((t,r)=>{let a={vendorId:e,someQuality:40,isBase64:!0};s.getUvcCameraImg(a,(e=>{let t=JSON.parse(e);"ok"==t.msg&&(n("log","at pages/index/index.vue:231",t),n("log","at pages/index/index.vue:232",t.file),c.value=t.base64.replace(/[\r\n]/g,""))}))}))}(3141))},"UVC设备拍照"),e.createElementVNode("button",{onClick:o},"关闭UVC设备拍照"),e.createElementVNode("image",{src:e.unref(c),mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"sssAD"})]))}},[["__scopeId","data-v-58d91a0a"]]);var o=1252,l=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],f={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},h=function(e){-1!=l.indexOf(e)&&(o=f[0]=e)};var u=function(e){h(e)};function d(){u(1200),h(1252)}function p(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function m(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var g,v=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?m(e.slice(2)):65279==t?e.slice(1):e},b=function(e){return String.fromCharCode(e)},T=function(e){return String.fromCharCode(e)},E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function w(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0,o=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,c=(15&a)<<2|(n=e.charCodeAt(l++))>>6,o=63&n,isNaN(a)?c=o=64:isNaN(n)&&(o=64),t+=E.charAt(s)+E.charAt(i)+E.charAt(c)+E.charAt(o);return t}function S(e){var t="",r=0,a=0,n=0,s=0,i=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)r=E.indexOf(e.charAt(o++))<<2|(s=E.indexOf(e.charAt(o++)))>>4,t+=String.fromCharCode(r),a=(15&s)<<4|(i=E.indexOf(e.charAt(o++)))>>2,64!==i&&(t+=String.fromCharCode(a)),n=(3&i)<<6|(c=E.indexOf(e.charAt(o++))),64!==c&&(t+=String.fromCharCode(n));return t}var k=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),A=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function y(e){return k?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function x(e){return k?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var C=function(e){return k?A(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function _(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function N(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return N(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var O=k?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:A(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var R=/\u0000/g,I=/[\u0001-\u0006]/g;function D(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function F(e,t){var r=""+e;return r.length>=t?r:ze("0",t-r.length)+r}function P(e,t){var r=""+e;return r.length>=t?r:ze(" ",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:r+ze(" ",t-r.length)}var M=Math.pow(2,32);function B(e,t){return e>M||e<-M?function(e,t){var r=""+Math.round(e);return r.length>=t?r:ze("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:ze("0",t-r.length)+r}(Math.round(e),t)}function U(e,t){return t=t||0,e.length>=7+t&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var V=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],H=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var W={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},z={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},G={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function j(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,c=0,o=1,l=0,f=0,h=Math.floor(n);l<t&&(c=(h=Math.floor(n))*i+s,f=h*l+o,!(n-h<5e-8));)n=1/(n-h),s=i,i=c,o=l,l=f;if(f>t&&(l>t?(f=o,c=s):(f=l,c=i)),!r)return[0,a*c,f];var u=Math.floor(a*c/f);return[u,a*c-u*f,f]}function X(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],c={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),t&&t.date1904&&(a+=1462),c.u>.9999&&(c.u=0,86400==++n&&(c.T=n=0,++a,++c.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var o=new Date(1900,0,1);o.setDate(o.getDate()+a-1),i=[o.getFullYear(),o.getMonth()+1,o.getDate()],s=o.getDay(),a<60&&(s=(s+6)%7),r&&(s=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(o,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=n%60,n=Math.floor(n/60),c.M=n%60,n=Math.floor(n/60),c.H=n,c.q=s,c}var Y=new Date(1899,11,31,0,0,0),K=Y.getTime(),J=new Date(1900,2,1,0,0,0);function q(e,t){var r=e.getTime();return t?r-=1262304e5:e>=J&&(r+=864e5),(r-(K+6e4*(e.getTimezoneOffset()-Y.getTimezoneOffset())))/864e5}function Z(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Q(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=Z(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=Z(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),Z(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function ee(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):Q(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return we(14,q(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function te(e,t,r,a){var n,s="",i=0,c=0,o=r.y,l=0;switch(e){case 98:o=r.y+543;case 121:switch(t.length){case 1:case 2:n=o%100,l=2;break;default:n=o%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return H[r.m-1][1];case 5:return H[r.m-1][0];default:return H[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return V[r.q][0];default:return V[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(c=a>=2?3===a?1e3:100:1===a?10:1,(i=Math.round(c*(r.S+r.u)))>=60*c&&(i=0),"s"===t?0===i?"0":""+i/c:(s=F(i,2+a),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):F(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":n=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=o,l=1}return l>0?F(n,l):""}function re(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var ae=/%/g;function ne(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+ne(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).indexOf("e")){var i=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var se=/# (\?+)( ?)\/( ?)(\d+)/;var ie=/^#*0*\.([0#]+)/,ce=/\).*[0#]/,oe=/\(###\) ###\\?-####/;function le(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function fe(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function he(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function ue(e,t,r){if(40===e.charCodeAt(0)&&!t.match(ce)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ue("n",a,r):"("+ue("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return me(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(ae,""),n=t.length-a.length;return me(e,a,r*Math.pow(10,2*n))+ze("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return ne(t,r);if(36===t.charCodeAt(0))return"$"+ue(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,c,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+B(o,t.length);if(t.match(/^[#?]+$/))return"0"===(n=B(r,0))&&(n=""),n.length>t.length?n:le(t.substr(0,t.length-n.length))+n;if(s=t.match(se))return function(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,c=a;return r+(0===s?"":""+s)+" "+(0===i?ze(" ",e[1].length+1+e[4].length):P(i,e[1].length)+e[2]+"/"+e[3]+F(c,e[4].length))}(s,o,l);if(t.match(/^#+0+$/))return l+B(o,t.length-t.indexOf("0"));if(s=t.match(ie))return n=fe(r,s[1].length).replace(/^([^\.]+)$/,"$1."+le(s[1])).replace(/\.$/,"."+le(s[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+ze("0",le(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+fe(o,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+re(B(o,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ue(e,t,-r):re(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,s[1].length)))+"."+F(he(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return ue(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=D(ue(e,t.replace(/[\\-]/g,""),r)),i=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(oe))return"("+(n=ue(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=j(o,Math.pow(10,i)-1,!1),n=""+l," "==(f=me("n",s[1],c[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=L(c[2],i)).length<s[4].length&&(f=le(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((c=j(o,Math.pow(10,i)-1,!0))[0]||(c[1]?"":"0"))+" "+(c[1]?P(c[1],i)+s[2]+"/"+s[3]+L(c[2],i):ze(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=B(r,0),t.length<=n.length?n:le(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return le(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=he(r,s[1].length),r<0?"-"+ue(e,t,-r):re(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?F(0,3-e.length):"")+e}))+"."+F(i,s[1].length);switch(t){case"###,##0.00":return ue(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=re(B(o,0));return"0"!==d?l+d:"";case"###,###.00":return ue(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return ue(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function de(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+de(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),!(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function pe(e,t,r){if(40===e.charCodeAt(0)&&!t.match(ce)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?pe("n",a,r):"("+pe("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return me(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(ae,""),n=t.length-a.length;return me(e,a,r*Math.pow(10,2*n))+ze("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return de(t,r);if(36===t.charCodeAt(0))return"$"+pe(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,c,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+F(o,t.length);if(t.match(/^[#?]+$/))return n=""+r,0===r&&(n=""),n.length>t.length?n:le(t.substr(0,t.length-n.length))+n;if(s=t.match(se))return function(e,t,r){return r+(0===t?"":""+t)+ze(" ",e[1].length+2+e[4].length)}(s,o,l);if(t.match(/^#+0+$/))return l+F(o,t.length-t.indexOf("0"));if(s=t.match(ie))return n=(n=(""+r).replace(/^([^\.]+)$/,"$1."+le(s[1])).replace(/\.$/,"."+le(s[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+ze("0",le(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+re(""+o);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+pe(e,t,-r):re(""+r)+"."+ze("0",s[1].length);if(s=t.match(/^#,#*,#0/))return pe(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=D(pe(e,t.replace(/[\\-]/g,""),r)),i=0,D(D(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(oe))return"("+(n=pe(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=j(o,Math.pow(10,i)-1,!1),n=""+l," "==(f=me("n",s[1],c[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=L(c[2],i)).length<s[4].length&&(f=le(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((c=j(o,Math.pow(10,i)-1,!0))[0]||(c[1]?"":"0"))+" "+(c[1]?P(c[1],i)+s[2]+"/"+s[3]+L(c[2],i):ze(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:le(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return le(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+pe(e,t,-r):re(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?F(0,3-e.length):"")+e}))+"."+F(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=re(""+o);return"0"!==d?l+d:"";default:if(t.match(/\.[0#?]*$/))return pe(e,t.slice(0,t.lastIndexOf(".")),r)+le(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function me(e,t,r){return(0|r)===r?pe(e,t,r):ue(e,t,r)}var ge=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ve(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":U(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(ge))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var be=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Te(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function Ee(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(be),c=r[1].match(be);return Te(t,i)?[a,r[0]]:Te(t,c)?[a,r[1]]:[a,r[null!=i&&null!=c?2:1]]}return[a,s]}function we(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:W)[e])&&(a=r.table&&r.table[z[e]]||W[z[e]]),null==a&&(a=G[e]||"General")}if(U(a,0))return ee(t,r);t instanceof Date&&(t=q(t,r.date1904));var n=Ee(a,t);if(U(n[1]))return ee(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,c=[],o="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!U(e,l))throw new Error("unrecognized character "+f+" in "+e);c[c.length]={t:"G",v:"General"},l+=7;break;case'"':for(o="";34!==(i=e.charCodeAt(++l))&&l<e.length;)o+=String.fromCharCode(i);c[c.length]={t:"t",v:o},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";c[c.length]={t:p,v:d},++l;break;case"_":c[c.length]={t:"t",v:" "},l+=2;break;case"@":c[c.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=X(t,r,"2"===e.charAt(l+1))))return"";c[c.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==n&&null==(n=X(t,r)))return"";for(o=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)o+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),c[c.length]={t:f,v:o},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=X(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";c[c.length]=m,h=f;break;case"[":for(o=f;"]"!==e.charAt(l++)&&l<e.length;)o+=e.charAt(l);if("]"!==o.slice(-1))throw'unterminated "[" block: |'+o+"|";if(o.match(ge)){if(null==n&&null==(n=X(t,r)))return"";c[c.length]={t:"Z",v:o.toLowerCase()},h=o.charAt(1)}else o.indexOf("$")>-1&&(o=(o.match(/\$([^-\[\]]*)/)||[])[1]||"$",ve(e)||(c[c.length]={t:"t",v:o}));break;case".":if(null!=n){for(o=f;++l<e.length&&"0"===(f=e.charAt(l));)o+=f;c[c.length]={t:"s",v:o};break}case"0":case"#":for(o=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)o+=f;c[c.length]={t:"n",v:o};break;case"?":for(o=f;e.charAt(++l)===f;)o+=f;c[c.length]={t:f,v:o},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":c[c.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(o=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)o+=e.charAt(l);c[c.length]={t:"D",v:o};break;case" ":c[c.length]={t:f,v:f},++l;break;case"$":c[c.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);c[c.length]={t:"t",v:f},++l}var g,v=0,b=0;for(l=c.length-1,h="t";l>=0;--l)switch(c[l].t){case"h":case"H":c[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=c[l].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=c[l].t;break;case"m":"s"===h&&(c[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&c[l].v.match(/[Hh]/)&&(v=1),v<2&&c[l].v.match(/[Mm]/)&&(v=2),v<3&&c[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var T,E="";for(l=0;l<c.length;++l)switch(c[l].t){case"t":case"T":case" ":case"D":break;case"X":c[l].v="",c[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":c[l].v=te(c[l].t.charCodeAt(0),c[l].v,n,b),c[l].t="t";break;case"n":case"?":for(T=l+1;null!=c[T]&&("?"===(f=c[T].t)||"D"===f||(" "===f||"t"===f)&&null!=c[T+1]&&("?"===c[T+1].t||"t"===c[T+1].t&&"/"===c[T+1].v)||"("===c[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===c[T].v||" "===c[T].v&&null!=c[T+1]&&"?"==c[T+1].t));)c[l].v+=c[T].v,c[T]={v:"",t:";"},++T;E+=c[l].v,l=T-1;break;case"G":c[l].t="t",c[l].v=ee(t,r)}var w,S,k="";if(E.length>0){40==E.charCodeAt(0)?(w=t<0&&45===E.charCodeAt(0)?-t:t,S=me("n",E,w)):(S=me("n",E,w=t<0&&a>1?-t:t),w<0&&c[0]&&"t"==c[0].t&&(S=S.substr(1),c[0].v="-"+c[0].v)),T=S.length-1;var A=c.length;for(l=0;l<c.length;++l)if(null!=c[l]&&"t"!=c[l].t&&c[l].v.indexOf(".")>-1){A=l;break}var y=c.length;if(A===c.length&&-1===S.indexOf("E")){for(l=c.length-1;l>=0;--l)null!=c[l]&&-1!=="n?".indexOf(c[l].t)&&(T>=c[l].v.length-1?(T-=c[l].v.length,c[l].v=S.substr(T+1,c[l].v.length)):T<0?c[l].v="":(c[l].v=S.substr(0,T+1),T=-1),c[l].t="t",y=l);T>=0&&y<c.length&&(c[y].v=S.substr(0,T+1)+c[y].v)}else if(A!==c.length&&-1===S.indexOf("E")){for(T=S.indexOf(".")-1,l=A;l>=0;--l)if(null!=c[l]&&-1!=="n?".indexOf(c[l].t)){for(s=c[l].v.indexOf(".")>-1&&l===A?c[l].v.indexOf(".")-1:c[l].v.length-1,k=c[l].v.substr(s+1);s>=0;--s)T>=0&&("0"===c[l].v.charAt(s)||"#"===c[l].v.charAt(s))&&(k=S.charAt(T--)+k);c[l].v=k,c[l].t="t",y=l}for(T>=0&&y<c.length&&(c[y].v=S.substr(0,T+1)+c[y].v),T=S.indexOf(".")+1,l=A;l<c.length;++l)if(null!=c[l]&&(-1!=="n?(".indexOf(c[l].t)||l===A)){for(s=c[l].v.indexOf(".")>-1&&l===A?c[l].v.indexOf(".")+1:0,k=c[l].v.substr(0,s);s<c[l].v.length;++s)T<S.length&&(k+=S.charAt(T++));c[l].v=k,c[l].t="t",y=l}}}for(l=0;l<c.length;++l)null!=c[l]&&"n?".indexOf(c[l].t)>-1&&(w=a>1&&t<0&&l>0&&"-"===c[l-1].v?-t:t,c[l].v=me(c[l].t,c[l].v,w),c[l].t="t");var x="";for(l=0;l!==c.length;++l)null!=c[l]&&(x+=c[l].v);return x}(n[1],t,r,n[0])}function Se(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=W[r]){if(W[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return W[t]=e,t}function ke(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',W=e}var Ae={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},ye=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var xe=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],c=r[4],o=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[255&(a^e.charCodeAt(n++))];return~a},e.buf=function(e,r){for(var b=-1^r,T=e.length-15,E=0;E<T;)b=v[e[E++]^255&b]^g[e[E++]^b>>8&255]^m[e[E++]^b>>16&255]^p[e[E++]^b>>>24]^d[e[E++]]^u[e[E++]]^h[e[E++]]^f[e[E++]]^l[e[E++]]^o[e[E++]]^c[e[E++]]^i[e[E++]]^s[e[E++]]^n[e[E++]]^a[e[E++]]^t[e[E++]];for(T+=15;E<T;)b=b>>>8^t[255&(b^e[E++])];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,c=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[255&(a^i)]:i<2048?a=(a=a>>>8^t[255&(a^(192|i>>6&31))])>>>8^t[255&(a^(128|63&i))]:i>=55296&&i<57344?(i=64+(1023&i),c=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[255&(a^(240|i>>8&7))])>>>8^t[255&(a^(128|i>>2&63))])>>>8^t[255&(a^(128|c>>6&15|(3&i)<<4))])>>>8^t[255&(a^(128|63&c))]):a=(a=(a=a>>>8^t[255&(a^(224|i>>12&15))])>>>8^t[255&(a^(128|i>>6&63))])>>>8^t[255&(a^(128|63&i))];return~a},e}(),Ce=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function s(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}function i(e){gr(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};if(21589===a)1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));e.l=s,t[a]=i}return t}function c(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return me(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=D(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",a=(k&&Buffer.isBuffer(e)?e.toString("binary"):D(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var c="--"+(i[1]||""),o={FileIndex:[],FullPaths:[]};d(o);var l,f=0;for(n=0;n<a.length;++n){var h=a[n];h!==c&&h!==c+"--"||(f++&&we(o,a.slice(l,n),r),l=n)}return o}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,a,n,s,i,c,o=512,p=[],m=e.slice(0,512);gr(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(T,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:o=512;break;case 4:o=4096;break;case 0:if(0==g[1])return me(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==o&&gr(m=e.slice(0,o),28);var v=e.slice(0,o);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var E=m.read_shift(4,"i");if(3===r&&0!==E)throw new Error("# Directory Sectors: Expected 0 saw "+E);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),i=m.read_shift(4,"i"),a=m.read_shift(4,"i"),c=m.read_shift(4,"i"),n=m.read_shift(4,"i");for(var w=-1,S=0;S<109&&!((w=m.read_shift(4,"i"))<0);++S)p[S]=w;var A=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,o);f(c,n,A,o,p);var y=function(e,t,r,a){var n=e.length,s=[],i=[],c=[],o=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(c=[],(u=f+t)>=n&&(u-=n),!i[u]){o=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,c[c.length]=h,o.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(p[h=fr(e[m],d)])break}s[u]={nodes:c,data:Vt([o])}}return s}(A,s,p,o);y[s].name="!Directory",a>0&&i!==b&&(y[i].name="!MiniFAT"),y[p[0]].name="!FAT",y.fat_addrs=p,y.ssz=o;var x=[],C=[],_=[];!function(e,t,r,a,n,s,i,c){for(var o,f=0,d=a.length?2:0,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);gr(v,64),g=v.read_shift(2),o=Wt(v,0,g-d),a.push(o);var T={name:o,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=u(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=u(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=b,T.name=""),5===T.type?(f=T.start,n>0&&f!==b&&(t[f].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===t[T.start]&&(t[T.start]=h(r,T.start,t.fat_addrs,t.ssz)),t[T.start].name=T.name,T.content=t[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:f!==b&&T.start!==b&&t[f]&&(T.content=l(T,t[f].data,(t[c]||{}).data))),T.content&&gr(T.content,0),s[o]=T,i.push(T)}}(s,y,A,x,a,{},C,i),function(e,t,r){for(var a=0,n=0,s=0,i=0,c=0,o=r.length,l=[],f=[];a<o;++a)l[a]=f[a]=a,t[a]=r[a];for(;c<f.length;++c)n=e[a=f[c]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<c&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<c&&f.push(s));for(a=1;a<o;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<o;++a)if(0!==e[a].type){if((c=a)!=l[c])do{c=l[c],t[a]=t[c]+"/"+t[a]}while(0!==c&&-1!==l[c]&&c!=l[c]);l[a]=-1}for(t[0]+="/",a=1;a<o;++a)2!==e[a].type&&(t[a]+="/")}(C,_,x),x.shift();var N={FileIndex:C,FullPaths:_};return t&&t.raw&&(N.raw={header:v,sectors:A}),N}function l(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*v,i*v+v)),n-=v,i=fr(r,4*i);return 0===s.length?br(0):O(s).slice(0,e.size)}function f(e,t,r,a,n){var s=b;if(e===b){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=r[e],c=(a>>>2)-1;if(!i)return;for(var o=0;o<c&&(s=fr(i,4*o))!==b;++o)n.push(s);f(fr(i,a-4),t-1,r,a,n)}}function h(e,t,r,a,n){var s=[],i=[];n||(n=[]);var c=a-1,o=0,l=0;for(o=t;o>=0;){n[o]=!0,s[s.length]=o,i.push(e[o]);var f=r[Math.floor(4*o/a)];if(a<4+(l=4*o&c))throw new Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=fr(e[f],l)}return{nodes:s,data:Vt([i])}}function u(e,t){return new Date(1e3*(lr(e,t+4)/1e7*Math.pow(2,32)+lr(e,t)/1e7-11644473600))}function d(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="Sh33tJ5";if(Ce.find(e,"/"+t))return;var r=br(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),p(e)}(e)}function p(e,t){d(e);for(var n=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var c=e.FileIndex[i];switch(c.type){case 0:s?n=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(c.R*c.L*c.C)&&(n=!0),c.R>-1&&c.L>-1&&c.R==c.L&&(n=!0);break;default:n=!0}}if(n||t){var o=new Date(1987,1,19),l=0,f=Object.create?Object.create(null):{},h=[];for(i=0;i<e.FullPaths.length;++i)f[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&h.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<h.length;++i){var u=r(h[i][0]);(s=f[u])||(h.push([u,{name:a(u).replace("/",""),type:1,clsid:_,ct:o,mt:o,content:null}]),f[u]=!0)}for(h.sort((function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],i=0;i<h.length;++i)e.FullPaths[i]=h[i][0],e.FileIndex[i]=h[i][1];for(i=0;i<h.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||_,0===i)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<h.length&&r(e.FullPaths[l])!=m;++l);for(p.C=l>=h.length?-1:l,l=i+1;l<h.length&&r(e.FullPaths[l])!=r(m);++l);p.R=l>=h.length?-1:l,p.type=1}else r(e.FullPaths[i+1]||"")==r(m)&&(p.R=i+1),p.type=2}}}function m(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,c=e.FileIndex[0],o=1;o<e.FullPaths.length;++o)if(i=e.FullPaths[o].slice(s.length),(c=e.FileIndex[o]).size&&c.content&&"Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=c.content,f=k&&Buffer.isBuffer(l)?l.toString("binary"):D(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+be(c,i)),n.push(""),n.push(m?Ee(f):Te(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,r);if(p(e),"zip"===r.fileType)return function(e,t){var r=t||{},a=[],n=[],i=br(1),c=r.compression?8:0,o=0,l=0,f=0,h=0,u=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],g=[],v=0;for(l=1;l<e.FullPaths.length;++l)if(p=e.FullPaths[l].slice(d.length),(m=e.FileIndex[l]).size&&m.content&&"Sh33tJ5"!=p){var b=h,T=br(p.length);for(f=0;f<p.length;++f)T.write_shift(1,127&p.charCodeAt(f));T=T.slice(0,T.l),g[u]=xe.buf(m.content,0);var E=m.content;8==c&&(E=F(E)),(i=br(30)).write_shift(4,67324752),i.write_shift(2,20),i.write_shift(2,o),i.write_shift(2,c),m.mt?s(i,m.mt):i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,E.length),i.write_shift(4,m.content.length),i.write_shift(2,T.length),i.write_shift(2,0),h+=i.length,a.push(i),h+=T.length,a.push(T),h+=E.length,a.push(E),(i=br(46)).write_shift(4,33639248),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,o),i.write_shift(2,c),i.write_shift(4,0),i.write_shift(-4,g[u]),i.write_shift(4,E.length),i.write_shift(4,m.content.length),i.write_shift(2,T.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,b),v+=i.l,n.push(i),v+=T.length,n.push(T),++u}return(i=br(22)).write_shift(4,101010256),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,u),i.write_shift(2,u),i.write_shift(4,v),i.write_shift(4,h),i.write_shift(2,0),O([O(a),O(n),i])}(e,r);var a=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,c=t+127>>7,o=(t+7>>3)+r+i+c,l=o+127>>7,f=l<=109?0:Math.ceil((l-109)/127);o+l+f+127>>7>l;)f=++l<=109?0:Math.ceil((l-109)/127);var h=[1,f,l,c,i,r,t,0];return e.FileIndex[0].size=t<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),n=br(a[7]<<9),i=0,c=0;for(i=0;i<8;++i)n.write_shift(1,E[i]);for(i=0;i<8;++i)n.write_shift(2,0);for(n.write_shift(2,62),n.write_shift(2,3),n.write_shift(2,65534),n.write_shift(2,9),n.write_shift(2,6),i=0;i<3;++i)n.write_shift(2,0);for(n.write_shift(4,0),n.write_shift(4,a[2]),n.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),n.write_shift(4,0),n.write_shift(4,4096),n.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:b),n.write_shift(4,a[3]),n.write_shift(-4,a[1]?a[0]-1:b),n.write_shift(4,a[1]),i=0;i<109;++i)n.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(c=0;c<a[1];++c){for(;i<236+127*c;++i)n.write_shift(-4,i<a[2]?a[1]+i:-1);n.write_shift(-4,c===a[1]-1?b:c+1)}var o=function(e){for(c+=e;i<c-1;++i)n.write_shift(-4,i+1);e&&(++i,n.write_shift(-4,b))};for(c=i=0,c+=a[1];i<c;++i)n.write_shift(-4,N.DIFSECT);for(c+=a[2];i<c;++i)n.write_shift(-4,N.FATSECT);o(a[3]),o(a[4]);for(var l=0,f=0,h=e.FileIndex[0];l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&((f=h.content.length)<4096||(h.start=c,o(f+511>>9)));for(o(a[6]+7>>3);511&n.l;)n.write_shift(-4,N.ENDOFCHAIN);for(c=i=0,l=0;l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&(!(f=h.content.length)||f>=4096||(h.start=c,o(f+63>>6)));for(;511&n.l;)n.write_shift(-4,N.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(u&&0!==u.length){h=e.FileIndex[i],0===i&&(h.start=h.size?h.start-1:b);var d=0===i&&r.root||h.name;if(f=2*(d.length+1),n.write_shift(64,d,"utf16le"),n.write_shift(2,f),n.write_shift(1,h.type),n.write_shift(1,h.color),n.write_shift(-4,h.L),n.write_shift(-4,h.R),n.write_shift(-4,h.C),h.clsid)n.write_shift(16,h.clsid,"hex");else for(l=0;l<4;++l)n.write_shift(4,0);n.write_shift(4,h.state||0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,h.start),n.write_shift(4,h.size),n.write_shift(4,0)}else{for(l=0;l<17;++l)n.write_shift(4,0);for(l=0;l<3;++l)n.write_shift(4,-1);for(l=0;l<12;++l)n.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>=4096)if(n.l=h.start+1<<9,k&&Buffer.isBuffer(h.content))h.content.copy(n,n.l,0,h.size),n.l+=h.size+511&-512;else{for(l=0;l<h.size;++l)n.write_shift(1,h.content[l]);for(;511&l;++l)n.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>0&&h.size<4096)if(k&&Buffer.isBuffer(h.content))h.content.copy(n,n.l,0,h.size),n.l+=h.size+63&-64;else{for(l=0;l<h.size;++l)n.write_shift(1,h.content[l]);for(;63&l;++l)n.write_shift(1,0)}if(k)n.l=n.length;else for(;n.l<n.length;)n.write_shift(1,0);return n}t.version="1.2.1";var g,v=64,b=-2,T="d0cf11e0a1b11ae1",E=[208,207,17,224,161,177,26,225],_="00000000000000000000000000000000",N={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:b,FREESECT:-1,HEADER_SIGNATURE:T,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:_,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function D(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function F(e){return g?g.deflateRawSync(e):ie(e)}var P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],L=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],M=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var B,U,V="undefined"!=typeof Uint8Array,H=V?new Uint8Array(256):[],W=0;W<256;++W)H[W]=(U=void 0,255&((U=139536&((B=W)<<1|B<<11)|558144&(B<<5|B<<15))>>16|U>>8|U));function z(e,t){var r=H[255&e];return t<=8?r>>>8-t:(r=r<<8|H[e>>8&255],t<=16?r>>>16-t:(r=r<<8|H[e>>16&255])>>>24-t)}function G(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}function $(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function j(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function X(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function Y(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a?i&s:(i|=e[n+1]<<8-a,r<16-a?i&s:(i|=e[n+2]<<16-a,r<24-a?i&s:(i|=e[n+3]<<24-a)&s))}function K(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function J(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function q(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function Z(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function Q(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(k){var s=x(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(V){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function ee(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function te(e,t,r){var a=1,n=0,s=0,i=0,c=0,o=e.length,l=V?new Uint16Array(32):ee(32);for(s=0;s<32;++s)l[s]=0;for(s=o;s<r;++s)e[s]=0;o=e.length;var f=V?new Uint16Array(o):ee(o);for(s=0;s<o;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=c=c+l[s-1]<<1;for(s=0;s<o;++s)0!=(c=e[s])&&(f[s]=l[c+16]++);var h=0;for(s=0;s<o;++s)if(0!=(h=e[s]))for(c=z(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[c|i<<h]=15&h|s<<4;return a}var re=V?new Uint16Array(512):ee(512),ae=V?new Uint16Array(32):ee(32);if(!V){for(var ne=0;ne<512;++ne)re[ne]=0;for(ne=0;ne<32;++ne)ae[ne]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);te(e,ae,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);te(r,re,288)}();var se=function(){for(var e=V?new Uint8Array(32768):[],t=0,r=0;t<M.length-1;++t)for(;r<M[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=V?new Uint8Array(259):[];for(t=0,r=0;t<L.length-1;++t)for(;r<L[t+1];++r)a[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var a=Math.min(65535,e.length-r),n=r+a==e.length;for(t.write_shift(1,+n),t.write_shift(2,a),t.write_shift(2,65535&~a);a-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var n=0,s=0,i=V?new Uint16Array(32768):[];s<t.length;){var c=Math.min(65535,t.length-s);if(c<10){for(7&(n=K(r,n,+!(s+c!=t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,c),r.write_shift(2,65535&~c);c-- >0;)r[r.l++]=t[s++];n=8*r.l}else{n=K(r,n,+!(s+c!=t.length)+2);for(var o=0;c-- >0;){var l=t[s],f=-1,h=0;if((f=i[o=32767&(o<<5^l)])&&((f|=-32768&s)>s&&(f-=32768),f<s))for(;t[f+h]==t[s+h]&&h<250;)++h;if(h>2){(l=a[h])<=22?n=q(r,n,H[l+1]>>1)-1:(q(r,n,3),q(r,n+=5,H[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(Z(r,n,h-L[l]),n+=u),l=e[s-f],n=q(r,n,H[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(Z(r,n,s-f-M[l]),n+=d);for(var p=0;p<h;++p)i[o]=32767&s,o=32767&(o<<5^t[s]),++s;c-=h-1}else l<=143?l+=48:n=J(r,n,1),n=q(r,n,H[l]),i[o]=32767&s,++s}n=q(r,n,0)-1}}return r.l=(n+7)/8|0,r.l}(t,r)}}();function ie(e){var t=br(50+Math.floor(1.1*e.length)),r=se(e,t);return t.slice(0,r)}var ce=V?new Uint16Array(32768):ee(32768),oe=V?new Uint16Array(32768):ee(32768),le=V?new Uint16Array(128):ee(128),fe=1,he=1;function ue(e,t){var r=j(e,t)+257,a=j(e,t+=5)+1,n=function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=4?0:e[a+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var s=0,i=V?new Uint8Array(19):ee(19),c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=1,l=V?new Uint8Array(8):ee(8),f=V?new Uint8Array(8):ee(8),h=i.length,u=0;u<n;++u)i[P[u]]=s=$(e,t),o<s&&(o=s),l[s]++,t+=3;var d=0;for(l[0]=0,u=1;u<=o;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(c[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(0!=(p=i[u])){d=H[c[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)le[d|m<<p]=7&p|u<<3}var g=[];for(o=1;g.length<r+a;)switch(t+=7&(d=le[X(e,t)]),d>>>=3){case 16:for(s=3+G(e,t),t+=2,d=g[g.length-1];s-- >0;)g.push(d);break;case 17:for(s=3+$(e,t),t+=3;s-- >0;)g.push(0);break;case 18:for(s=11+X(e,t),t+=7;s-- >0;)g.push(0);break;default:g.push(d),o<d&&(o=d)}var v=g.slice(0,r),b=g.slice(r);for(u=r;u<286;++u)v[u]=0;for(u=a;u<30;++u)b[u]=0;return fe=te(v,ce,286),he=te(b,oe,30),t}function de(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[y(t),2];for(var r=0,a=0,n=x(t||1<<18),s=0,i=n.length>>>0,c=0,o=0;0==(1&a);)if(a=$(e,r),r+=3,a>>>1!=0)for(a>>1==1?(c=9,o=5):(r=ue(e,r),c=fe,o=he);;){!t&&i<s+32767&&(i=(n=Q(n,s+32767)).length);var l=Y(e,r,c),f=a>>>1==1?re[l]:ce[l];if(r+=15&f,0==((f>>>=4)>>>8&255))n[s++]=f;else{if(256==f)break;var h=(f-=257)<8?0:f-4>>2;h>5&&(h=0);var u=s+L[f];h>0&&(u+=Y(e,r,h),r+=h),l=Y(e,r,o),r+=15&(f=a>>>1==1?ae[l]:oe[l]);var d=(f>>>=4)<4?0:f-2>>1,p=M[f];for(d>0&&(p+=Y(e,r,d),r+=d),!t&&i<u&&(i=(n=Q(n,u+100)).length);s<u;)n[s]=n[s-p],++s}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0)for(!t&&i<s+m&&(i=(n=Q(n,s+m)).length);m-- >0;)n[s++]=e[r>>>3],r+=8}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function pe(e,t){if(!e)throw new Error(t);"undefined"!=typeof console&&n("error","at node_modules/xlsx/xlsx.mjs:2670",t)}function me(e,t){var r=e;gr(r,0);var a={FileIndex:[],FullPaths:[]};d(a,{root:t.root});for(var n=r.length-4;(80!=r[n]||75!=r[n+1]||5!=r[n+2]||6!=r[n+3])&&n>=0;)--n;r.l=n+4,r.l+=4;var s=r.read_shift(2);r.l+=6;var c=r.read_shift(4);for(r.l=c,n=0;n<s;++n){r.l+=20;var o=r.read_shift(4),l=r.read_shift(4),f=r.read_shift(2),h=r.read_shift(2),u=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=i(r.slice(r.l+f,r.l+f+h));r.l+=f+h+u;var g=r.l;r.l=p+4,ge(r,o,l,a,m),r.l=g}return a}function ge(e,t,r,a,n){e.l+=2;var s=e.read_shift(2),c=e.read_shift(2),o=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),a=new Date,n=31&r,s=15&(r>>>=5);r>>>=4,a.setMilliseconds(0),a.setFullYear(r+1980),a.setMonth(s-1),a.setDate(n);var i=31&t,c=63&(t>>>=5);return t>>>=6,a.setHours(t),a.setMinutes(c),a.setSeconds(i<<1),a}(e);if(8257&s)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);if(u){var m=i(e.slice(e.l,e.l+u));(m[21589]||{}).mt&&(o=m[21589].mt),((n||{})[21589]||{}).mt&&(o=n[21589].mt)}e.l+=u;var v=e.slice(e.l,e.l+l);switch(c){case 8:v=function(e,t){if(!g)return de(e,t);var r=new(0,g.InflateRaw),a=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,a}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+c)}var b=!1;8&s&&(134695760==e.read_shift(4)&&(e.read_shift(4),b=!0),l=e.read_shift(4),f=e.read_shift(4)),l!=t&&pe(b,"Bad compressed size: "+t+" != "+l),f!=r&&pe(b,"Bad uncompressed size: "+r+" != "+f),Se(a,d,v,{unsafe:!0,mt:o})}var ve={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function be(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&ve[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&ve[a[1]]?ve[a[1]]:"application/octet-stream"}function Te(e){for(var t=w(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}function Ee(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],a=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var c=76,o=s.slice(i,i+c);"="==o.charAt(c-1)?c--:"="==o.charAt(c-2)?c-=2:"="==o.charAt(c-3)&&(c-=3),o=s.slice(i,i+c),(i+=c)<s.length&&(o+="="),r.push(o)}else r.push("")}return r.join("\r\n")}function we(e,t,r){for(var a,n="",s="",i="",c=0;c<10;++c){var o=t[c];if(!o||o.match(/^\s*$/))break;var l=o.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++c,s.toLowerCase()){case"base64":a=C(S(t.slice(c).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return C(t.join("\r\n"))}(t.slice(c));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=Se(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}function Se(e,t,r,n){var s=n&&n.unsafe;s||d(e);var i=!s&&Ce.find(e,t);if(!i){var c=e.FullPaths[0];t.slice(0,c.length)==c?c=t:("/"!=c.slice(-1)&&(c+="/"),c=(c+t).replace("//","/")),i={name:a(t),type:2},e.FileIndex.push(i),e.FullPaths.push(c),s||Ce.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),a=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var c=!s.match(I);for(s=s.replace(R,""),c&&(s=s.replace(I,"!")),i=0;i<r.length;++i){if((c?r[i].replace(I,"!"):r[i]).replace(R,"")==s)return e.FileIndex[i];if((c?a[i].replace(I,"!"):a[i]).replace(R,"")==s)return e.FileIndex[i]}return null},t.read=function(t,r){var a=r&&r.type;switch(a||k&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return function(t,r){return c(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(C(S(t)),r);case"binary":return o(C(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var a=m(t,r);switch(r&&r.type||"buffer"){case"file":return c(),e.writeFileSync(r.filename,a),a;case"binary":return"string"==typeof a?a:D(a);case"base64":return w("string"==typeof a?a:D(a));case"buffer":if(k)return Buffer.isBuffer(a)?a:A(a);case"array":return"string"==typeof a?C(a):a}return a},t.writeFile=function(t,r,a){c();var n=m(t,a);e.writeFileSync(r,n)},t.utils={cfb_new:function(e){var t={};return d(t,e),t},cfb_add:Se,cfb_del:function(e,t){d(e);var r=Ce.find(e,t);if(r)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1},cfb_mov:function(e,t,r){d(e);var n=Ce.find(e,t);if(n)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(r),e.FullPaths[s]=r,!0;return!1},cfb_gc:function(e){p(e,!0)},ReadShift:ur,CheckField:mr,prep_blob:gr,bconcat:O,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");g=e}catch(r){n("error","at node_modules/xlsx/xlsx.mjs:2211","cannot use native zlib: "+(r.message||r))}},_deflateRaw:ie,_inflateRaw:de,consts:N},t}();function _e(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function Ne(e){for(var t=[],r=_e(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}var Oe=new Date(1899,11,30,0,0,0);function Re(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(Oe.getTime()+6e4*(e.getTimezoneOffset()-Oe.getTimezoneOffset())))/864e5}var Ie=new Date,De=Oe.getTime()+6e4*(Ie.getTimezoneOffset()-Oe.getTimezoneOffset()),Fe=Ie.getTimezoneOffset();function Pe(e){var t=new Date;return t.setTime(24*e*60*60*1e3+De),t.getTimezoneOffset()!==Fe&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-Fe)),t}function Le(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");r*=60}t+=r*parseInt(n[s],10)}return t}var Me=new Date("2017-02-19T19:06:09.000Z"),Be=isNaN(Me.getFullYear())?new Date("2/19/17"):Me,Ue=2017==Be.getFullYear();function Ve(e,t){var r=new Date(e);if(Ue)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==Be.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function He(e,t){if(k&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return xt(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return xt(m(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return xt(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return xt(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(s){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function We(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=We(e[r]));return t}function ze(e,t){for(var r="";r.length<t;)r+=e;return r}function Ge(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(a))?(a=a.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(a))?t:t/r):t/r}var $e=["january","february","march","april","may","june","july","august","september","october","november","december"];function je(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==$e.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}var Xe=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function Ye(e){return e?e.content&&e.type?He(e.content,!0):e.data?v(e.data):e.asNodeBuffer&&k?v(e.asNodeBuffer().toString("binary")):e.asBinary?v(e.asBinary()):e._data&&e._data.getContent?v(He(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Ke(e){if(!e)return null;if(e.data)return p(e.data);if(e.asNodeBuffer&&k)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?p(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Je(e,t){for(var r=e.FullPaths||_e(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function qe(e,t){var r=Je(e,t);if(null==r)throw new Error("Cannot find file "+t+" in zip");return r}function Ze(e,t,r){if(!r)return(a=qe(e,t))&&".bin"===a.name.slice(-4)?Ke(a):Ye(a);var a;if(!t)return null;try{return Ze(e,t)}catch(n){return null}}function Qe(e,t,r){if(!r)return Ye(qe(e,t));if(!t)return null;try{return Qe(e,t)}catch(a){return null}}function et(e,t,r){if(!r)return Ke(qe(e,t));if(!t)return null;try{return et(e,t)}catch(a){return null}}function tt(e){for(var t=e.FullPaths||_e(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function rt(e,t,r){if(e.FullPaths){var a;if("string"==typeof r)return a=k?A(r):function(e){for(var t=[],r=0,a=e.length+250,n=y(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var c=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|c>>6&15|(3&i)<<4,n[r++]=128|63&c}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=y(65535),a=65530)}return t.push(n.slice(0,r)),O(t)}(r),Ce.utils.cfb_add(e,t,a);Ce.utils.cfb_add(e,t,r)}else e.file(t,r)}function at(e,t){switch(t.type){case"base64":return Ce.read(e,{type:"base64"});case"binary":return Ce.read(e,{type:"binary"});case"buffer":case"array":return Ce.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function nt(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var st='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',it=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,ct=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,ot=st.match(ct)?ct:/<[^>]*>/g,lt=/<\w*:/,ft=/<(\/?)\w+:/;function ht(e,t,r){for(var a={},n=0,s=0;n!==e.length&&(32!==(s=e.charCodeAt(n))&&10!==s&&13!==s);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(it),c=0,o="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l],s=0;s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,o=h.slice(s+1+u,h.length-u),c=0;c!=f.length&&58!==f.charCodeAt(c);++c);if(c===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=o,r||(a[f.toLowerCase()]=o);else{var d=(5===c&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(c+1);if(a[d]&&"ext"==f.slice(c-3,c))continue;a[d]=o,r||(a[d.toLowerCase()]=o)}}return a}function ut(e){return e.replace(ft,"<$1")}var dt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},pt=Ne(dt),mt=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,t=/_x([\da-fA-F]{4})_/gi;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,t){return dt[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e})).replace(t,(function(e,t){return String.fromCharCode(parseInt(t,16))}));var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),gt=/[&<>'"]/g,vt=/[\u0000-\u001f]/g;function bt(e){return(e+"").replace(gt,(function(e){return pt[e]})).replace(/\n/g,"<br/>").replace(vt,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}var Tt=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function Et(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function wt(e){for(var t="",r=0,a=0,n=0,s=0,i=0,c=0;r<e.length;)(a=e.charCodeAt(r++))<128?t+=String.fromCharCode(a):(n=e.charCodeAt(r++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,t+=String.fromCharCode(i)):(s=e.charCodeAt(r++),a<240?t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(c=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&(i=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(c>>>10&1023)),t+=String.fromCharCode(56320+(1023&c)))));return t}function St(e){var t,r,a,n=y(2*e.length),s=1,i=0,c=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=64*(31&a)+(63&e.charCodeAt(r+1)),s=2):a<240?(t=4096*(15&a)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),s=3):(s=4,t=262144*(7&a)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),c=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==c&&(n[i++]=255&c,n[i++]=c>>>8,c=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function kt(e){return A(e,"binary").toString("utf8")}var At="foo bar bazâð£",yt=k&&(kt(At)==wt(At)&&kt||St(At)==wt(At)&&St)||wt,xt=k?function(e){return A(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},Ct=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),_t=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),Nt=function(){var e={};return function(t){return void 0!==e[t]?e[t]:e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),Ot=/<\/?(?:vt:)?variant>/g,Rt=/<(?:vt:)([^>]*)>([\s\S]*)</;function It(e,t){var r=ht(e),a=e.match(Nt(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach((function(e){var t=e.replace(Ot,"").match(Rt);t&&n.push({v:yt(t[2]),t:t[1]})})),n}var Dt=/(^\s|\s$|\n)/;function Ft(e,t,r){return"<"+e+(null!=r?function(e){return _e(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}(r):"")+(null!=t?(t.match(Dt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Pt(e){if(k&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return yt(_(N(e)));throw new Error("Bad input format: expected Buffer or string")}var Lt=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm,Mt="http://schemas.openxmlformats.org/package/2006/content-types",Bt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];var Ut=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},Vt=k?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:A(e)}))):Ut(e)}:Ut,Ht=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(cr(e,n)));return a.join("").replace(R,"")},Wt=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(R,""):Ht(e,t,r)}:Ht,zt=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Gt=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):zt(e,t,r)}:zt,$t=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(ir(e,n)));return a.join("")},jt=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):$t(e,t,r)}:$t,Xt=function(e,t){var r=lr(e,t);return r>0?jt(e,t+4,t+4+r-1):""},Yt=Xt,Kt=function(e,t){var r=lr(e,t);return r>0?jt(e,t+4,t+4+r-1):""},Jt=Kt,qt=function(e,t){var r=2*lr(e,t);return r>0?jt(e,t+4,t+4+r-1):""},Zt=qt,Qt=function(e,t){var r=lr(e,t);return r>0?Wt(e,t+4,t+4+r):""},er=Qt,tr=function(e,t){var r=lr(e,t);return r>0?jt(e,t+4,t+4+r):""},rr=tr,ar=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?r*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}(e,t)},nr=ar,sr=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};k&&(Yt=function(e,t){if(!Buffer.isBuffer(e))return Xt(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Jt=function(e,t){if(!Buffer.isBuffer(e))return Kt(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Zt=function(e,t){if(!Buffer.isBuffer(e))return qt(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},er=function(e,t){if(!Buffer.isBuffer(e))return Qt(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},rr=function(e,t){if(!Buffer.isBuffer(e))return tr(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},nr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):ar(e,t)},sr=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array});var ir=function(e,t){return e[t]},cr=function(e,t){return 256*e[t+1]+e[t]},or=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},lr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},fr=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},hr=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function ur(e,t){var r,a,n,s,i,c,o="",l=[];switch(t){case"dbcs":if(c=this.l,k&&Buffer.isBuffer(this))o=this.slice(this.l,this.l+2*e).toString("utf16le");else for(i=0;i<e;++i)o+=String.fromCharCode(cr(this,c)),c+=2;e*=2;break;case"utf8":o=jt(this,this.l,this.l+e);break;case"utf16le":e*=2,o=Wt(this,this.l,this.l+e);break;case"wstr":return ur.call(this,e,"dbcs");case"lpstr-ansi":o=Yt(this,this.l),e=4+lr(this,this.l);break;case"lpstr-cp":o=Jt(this,this.l),e=4+lr(this,this.l);break;case"lpwstr":o=Zt(this,this.l),e=4+2*lr(this,this.l);break;case"lpp4":e=4+lr(this,this.l),o=er(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+lr(this,this.l),o=rr(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,o="";0!==(n=ir(this,this.l+e++));)l.push(b(n));o=l.join("");break;case"_wstr":for(e=0,o="";0!==(n=cr(this,this.l+e));)l.push(b(n)),e+=2;e+=2,o=l.join("");break;case"dbcs-cont":for(o="",c=this.l,i=0;i<e;++i){if(this.lens&&-1!==this.lens.indexOf(c))return n=ir(this,c),this.l=c+1,s=ur.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(b(cr(this,c))),c+=2}o=l.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(o="",c=this.l,i=0;i!=e;++i){if(this.lens&&-1!==this.lens.indexOf(c))return n=ir(this,c),this.l=c+1,s=ur.call(this,e-i,n?"dbcs-cont":"sbcs-cont"),l.join("")+s;l.push(b(ir(this,c))),c+=1}o=l.join("");break;default:switch(e){case 1:return r=ir(this,this.l),this.l++,r;case 2:return r=("i"===t?or:cr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0==(128&this[this.l+3])?(r=(e>0?fr:hr)(this,this.l),this.l+=4,r):(a=lr(this,this.l),this.l+=4,a);case 8:case-8:if("f"===t)return a=8==e?nr(this,this.l):nr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:o=Gt(this,this.l,e)}}return this.l+=e,o}var dr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function pr(e,t,r){var a=0,n=0;if("dbcs"===r){for(n=0;n!=t.length;++n)dr(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if("sbcs"===r){for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=255&t.charCodeAt(n);a=t.length}else{if("hex"===r){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}if("utf16le"===r){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var i=t.charCodeAt(n);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255}(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var c=0;c<=5;++c,s/=256)e[r+c]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case-4:a=4,function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255}(this,t,this.l)}}return this.l+=a,this}function mr(e,t){var r=Gt(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function gr(e,t){e.l=t,e.read_shift=ur,e.chk=mr,e.write_shift=pr}function vr(e,t){e.l+=t}function br(e){var t=y(e);return gr(t,0),t}function Tr(e,t,r){if(e){var a,n,s;gr(e,e.l||0);for(var i=e.length,c=0,o=0;e.l<i;){128&(c=e.read_shift(1))&&(c=(127&c)+((127&e.read_shift(1))<<7));var l=yc[c]||yc[65535];for(s=127&(a=e.read_shift(1)),n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;o=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=o,t(f,l,c))return}}}function Er(){var e=[],t=k?256:2048,r=function(e){var t=br(e);return gr(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),O(e)},_bufs:e}}function wr(e,t,r){var a=We(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Sr(e,t,r){var a=We(e);return a.s=wr(a.s,t.s,r),a.e=wr(a.e,t.s,r),a}function kr(e,t){if(e.cRel&&e.c<0)for(e=We(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=We(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Or(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function Ar(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?kr(e.s,t.biff)+":"+kr(e.e,t.biff):(e.s.rRel?"":"$")+xr(e.s.r)+":"+(e.e.rRel?"":"$")+xr(e.e.r):(e.s.cRel?"":"$")+_r(e.s.c)+":"+(e.e.cRel?"":"$")+_r(e.e.c)}function yr(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function xr(e){return""+(e+1)}function Cr(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function _r(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Nr(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function Or(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Rr(e){var t=e.indexOf(":");return-1==t?{s:Nr(e),e:Nr(e)}:{s:Nr(e.slice(0,t)),e:Nr(e.slice(t+1))}}function Ir(e,t){return void 0===t||"number"==typeof t?Ir(e.s,e.e):("string"!=typeof e&&(e=Or(e)),"string"!=typeof t&&(t=Or(t)),e==t?e:e+":"+t)}function Dr(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function Fr(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=we(e.z,r?Re(t):t)}catch(a){}try{return e.w=we((e.XF||{}).numFmtId||(r?14:0),r?Re(t):t)}catch(a){return""+t}}function Pr(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?oa[e.v]||e.v:Fr(e,null==t?e.v:t))}function Lr(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function Mr(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,c=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var o="string"==typeof a.origin?Nr(a.origin):a.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=Dr(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=c+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||W[14],a.cellDates?(d.t="d",d.w=we(d.z,Re(d.v))):(d.t="n",d.v=Re(d.v),d.w=we(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=Or({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=Ir(l)),s}function Br(e,t){return Mr(null,e,t)}function Ur(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function Vr(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Hr(e,t){var r=e.l,a=e.read_shift(1),n=Ur(e),s=[],i={t:n,h:n};if(0!=(1&a)){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push(Vr(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var Wr=Hr;function zr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Gr(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}var $r=Ur;function jr(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}var Xr=Ur,Yr=jr;function Kr(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?nr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):fr(t,0)>>2;return r?n/100:n}function Jr(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var qr=Jr;function Zr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Qr(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(r>400)throw new Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var ea=80,ta=[ea,81],ra={1:{n:"CodePage",t:2},2:{n:"Category",t:ea},3:{n:"PresentationFormat",t:ea},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:ea},15:{n:"Company",t:ea},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:ea},27:{n:"ContentStatus",t:ea},28:{n:"Language",t:ea},29:{n:"Version",t:ea},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},aa={1:{n:"CodePage",t:2},2:{n:"Title",t:ea},3:{n:"Subject",t:ea},4:{n:"Author",t:ea},5:{n:"Keywords",t:ea},6:{n:"Comments",t:ea},7:{n:"Template",t:ea},8:{n:"LastAuthor",t:ea},9:{n:"RevNumber",t:ea},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:ea},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},na={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},sa=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function ia(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var ca=We(ia([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),oa={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},la={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},fa={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};var ha={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ua(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function da(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(ot)||[]).forEach((function(e){var n=ht(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:nt(n.Target,t);r[i]=s,a[n.Id]=s}})),r["!id"]=a,r}var pa=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],ma=function(){for(var e=new Array(pa.length),t=0;t<pa.length;++t){var r=pa[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function ga(e){var t={};e=yt(e);for(var r=0;r<pa.length;++r){var a=pa[r],n=e.match(ma[r]);null!=n&&n.length>0&&(t[a[1]]=mt(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=Ve(t[a[1]]))}return t}var va=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function ba(e,t,r,a){var n=[];if("string"==typeof e)n=It(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof t?It(t,a).map((function(e){return e.v})):t,c=0,o=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(o=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=o,r.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=o,r.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":r.Chartsheets=o,r.ChartNames=i.slice(c,c+o)}c+=o}}var Ta=/<[^>]+>[^<]*/g;var Ea,wa={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Sa(e,t,r){Ea||(Ea=Ne(wa)),e[t=Ea[t]||t]=r}function ka(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date(1e3*(r/1e7*Math.pow(2,32)+t/1e7-11644473600)).toISOString().replace(/\.000/,"")}function Aa(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function ya(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function xa(e,t,r){return 31===t?ya(e):Aa(e,0,r)}function Ca(e,t,r){return xa(e,t,!1===r?0:4)}function _a(e){var t=e.l,r=Ra(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,Ra(e,3)]}function Na(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(R,"").replace(I,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Oa(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function Ra(e,t,r){var a,n=e.read_shift(2),s=r||{};if(e.l+=2,12!==t&&n!==t&&-1===ta.indexOf(t)&&(4126!=(65534&t)||4126!=(65534&n)))throw new Error("Expected type "+t+" saw "+n);switch(12===t?n:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return a=e.read_shift(4);case 30:return Aa(e,0,4).replace(R,"");case 31:return ya(e);case 64:return ka(e);case 65:return Oa(e);case 71:return function(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}(e);case 80:return Ca(e,n,!s.raw).replace(R,"");case 81:return function(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return xa(e,t,0)}(e,n).replace(R,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(_a(e));return r}(e);case 4126:case 4127:return 4127==n?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(R,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(R,"");return r}(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+n)}}function Ia(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,c=0,o=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort((function(e,t){return e[1]-t[1]}));var d={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var m=t[s[i][0]];if(d[m.n]=Ra(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:u(c=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===s[i][0]){if(c=d.CodePage=Ra(e,2),u(c),-1!==o){var g=e.l;e.l=s[o][1],l=Na(e,c),e.l=g}}else if(0===s[i][0]){if(0===c){o=i,e.l=s[i+1][1];continue}l=Na(e,c)}else{var v,b=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=Oa(e);break;case 30:case 31:e.l+=4,v=Ca(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=Pa(e,4);break;case 64:e.l+=4,v=Ve(ka(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[b]=v}}return e.l=r+a,d}function Da(e,t,r){var a=e.content;if(!a)return{};gr(a,0);var n,s,i,c,o=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==Ce.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(1!==(n=a.read_shift(4))&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),c=a.read_shift(4),1===n&&c!==a.l)throw new Error("Length mismatch: "+c+" !== "+a.l);2===n&&(i=a.read_shift(16),o=a.read_shift(4));var h,u=Ia(a,t),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(o-a.l==2&&(a.l+=2),a.l!==o)throw new Error("Length mismatch 2: "+a.l+" !== "+o);try{h=Ia(a,null)}catch(m){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function Fa(e,t){return e.read_shift(t),null}function Pa(e,t){return 1===e.read_shift(t)}function La(e){return e.read_shift(2,"u")}function Ma(e,t){return function(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}(e,t,La)}function Ba(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont";(r&&r.biff,r&&8!=r.biff)?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont");return r.biff>=2&&r.biff<=5&&(n="cpstr"),a?e.read_shift(a,n):""}function Ua(e){var t,r=e.read_shift(2),a=e.read_shift(1),n=4&a,s=8&a,i=1+(1&a),c=0,o={};s&&(c=e.read_shift(2)),n&&(t=e.read_shift(4));var l=2==i?"dbcs-cont":"sbcs-cont",f=0===r?"":e.read_shift(r,l);return s&&(e.l+=4*c),n&&(e.l+=t),o.t=f,s||(o.raw="<t>"+o.t+"</t>",o.r=o.t),o}function Va(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function Ha(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):Va(e,a,r)}function Wa(e,t,r){if(r.biff>5)return Ha(e,0,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function za(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return function(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(R,"");return a&&(e.l+=24),n}(e);case"0303000000000000c000000000000046":return function(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return r+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");return r+e.read_shift(n>>1,"utf16le").replace(R,"")}(e);default:throw new Error("Unsupported Moniker "+r)}}function Ga(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(R,""):""}function $a(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function ja(e,t){var r=$a(e);return r[3]=0,r}function Xa(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function Ya(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}function Ka(e){return[e.read_shift(2),Kr(e)]}function Ja(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function qa(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}var Za=qa;function Qa(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function en(e){e.l+=2,e.l+=e.read_shift(2)}var tn={0:en,4:en,5:en,6:en,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:en,9:en,10:en,11:en,12:en,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:en,15:en,16:en,17:en,18:en,19:en,20:en,21:Qa};function rn(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function an(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}var nn=Wa;function sn(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(2),o=e.read_shift(2);return e.l=a,{s:{r:s,c:c},e:{r:i,c:o}}}function cn(e,t,r){var a=Xa(e);2!=r.biff&&9!=t||++e.l;var n=function(e){var t=e.read_shift(1);return 1===e.read_shift(1)?t:1===t}(e);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}var on=function(e,t,r){return 0===t?"":Wa(e,0,r)};function ln(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Ba(e,0,r),s=e.read_shift(2);if(s!==(a-=e.l))throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}var fn=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function hn(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(r&&2==r.biff?1:2),o=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),o=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var l=Va(e,i,r);32&n&&(l=fn[l.charCodeAt(0)]);var f=a-e.l;r&&2==r.biff&&--f;var h=a!=e.l&&0!==c&&f>0?function(e,t,r,a){var n,s=e.l+t,i=qs(e,a,r);s!==e.l&&(n=Js(e,s-e.l,i,r));return[i,n]}(e,f,r,c):[];return{chKey:s,Name:l,itab:o,rgce:h}}function un(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var a=Ba(e,0,r);return 3==a.charCodeAt(0)?a.slice(1):a}(e,0,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);0!=s--;)a.push(Ya(e,r.biff,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function dn(e,t,r){var a=Za(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,ai(e,t,r)]}var pn={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function mn(e,t,r){if(!r.cellStyles)return vr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(a),o=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:c,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}var gn=Xa,vn=Ma,bn=Ha;var Tn=[2,3,48,49,131,139,140,245],En=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Ne({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var s=Br(function(t,r){var a=[],s=y(1);switch(r.type){case"base64":s=C(S(t));break;case"binary":s=C(t);break;case"buffer":case"array":s=t}gr(s,0);var i=s.read_shift(1),c=!!(136&i),o=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,c=!0;break;case 140:l=!0;break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],m={},v=Math.min(s.length,2==i?521:h-10-(o?264:0)),b=l?32:11;s.l<v&&13!=s[s.l];)switch((m={}).name=g.utils.decode(d,s.slice(s.l,s.l+b)).replace(/[\u0000\r\n].*$/g,""),s.l+=b,m.type=String.fromCharCode(s.read_shift(1)),2==i||l||(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=l?13:14),m.type){case"B":o&&8==m.len||!r.WTF||n("log","at node_modules/xlsx/xlsx.mjs:7656","Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&n("log","at node_modules/xlsx/xlsx.mjs:7660","Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw new Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,E=0;for(a[0]=[],E=0;E!=p.length;++E)a[0][E]=p[E].name;for(;f-- >0;)if(42!==s[s.l])for(++s.l,a[++T]=[],E=0,E=0;E!=p.length;++E){var w=s.slice(s.l,s.l+p[E].len);s.l+=p[E].len,gr(w,0);var k=g.utils.decode(d,w);switch(p[E].type){case"C":k.trim().length&&(a[T][E]=k.replace(/\s+$/,""));break;case"D":8===k.length?a[T][E]=new Date(+k.slice(0,4),+k.slice(4,6)-1,+k.slice(6,8)):a[T][E]=k;break;case"F":a[T][E]=parseFloat(k.trim());break;case"+":case"I":a[T][E]=l?2147483648^w.read_shift(-4,"i"):w.read_shift(4,"i");break;case"L":switch(k.trim().toUpperCase()){case"Y":case"T":a[T][E]=!0;break;case"N":case"F":a[T][E]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+k+"|")}break;case"M":if(!c)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));a[T][E]="##MEMO##"+(l?parseInt(k.trim(),10):w.read_shift(4));break;case"N":(k=k.replace(/\u0000/g,"").trim())&&"."!=k&&(a[T][E]=+k||0);break;case"@":a[T][E]=new Date(w.read_shift(-8,"f")-621356832e5);break;case"T":a[T][E]=new Date(864e5*(w.read_shift(4)-2440588)+w.read_shift(4));break;case"Y":a[T][E]=w.read_shift(4,"i")/1e4+w.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":a[T][E]=-w.read_shift(-8,"f");break;case"B":if(o&&8==p[E].len){a[T][E]=w.read_shift(8,"f");break}case"G":case"P":w.l+=p[E].len;break;case"0":if("_NullFlags"===p[E].name)break;default:throw new Error("DBF Unsupported data type "+p[E].type)}}else s.l+=u;if(2!=i&&s.l<s.length&&26!=s[s.l++])throw new Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return s["!cols"]=a.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete a.DBF,s}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return Lr(r(e,t),t)}catch(a){if(t&&t.WTF)throw a}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&u(+n.codepage),"string"==n.type)throw new Error("Cannot write DBF to JS string");var s=Er(),i=fo(e,{header:1,raw:!0,cellDates:!0}),c=i[0],l=i.slice(1),f=e["!cols"]||[],h=0,d=0,p=0,m=1;for(h=0;h<c.length;++h)if(((f[h]||{}).DBF||{}).name)c[h]=f[h].DBF.name,++p;else if(null!=c[h]){if(++p,"number"==typeof c[h]&&(c[h]=c[h].toString(10)),"string"!=typeof c[h])throw new Error("DBF Invalid column name "+c[h]+" |"+typeof c[h]+"|");if(c.indexOf(c[h])!==h)for(d=0;d<1024;++d)if(-1==c.indexOf(c[h]+"_"+d)){c[h]+="_"+d;break}}var g=Dr(e["!ref"]),v=[],b=[],T=[];for(h=0;h<=g.e.c-g.s.c;++h){var E="",w="",S=0,k=[];for(d=0;d<l.length;++d)null!=l[d][h]&&k.push(l[d][h]);if(0!=k.length&&null!=c[h]){for(d=0;d<k.length;++d){switch(typeof k[d]){case"number":w="B";break;case"string":default:w="C";break;case"boolean":w="L";break;case"object":w=k[d]instanceof Date?"D":"C"}S=Math.max(S,String(k[d]).length),E=E&&E!=w?"C":w}S>250&&(S=250),"C"==(w=((f[h]||{}).DBF||{}).type)&&f[h].DBF.len>S&&(S=f[h].DBF.len),"B"==E&&"N"==w&&(E="N",T[h]=f[h].DBF.dec,S=f[h].DBF.len),b[h]="C"==E||"N"==w?S:a[E]||0,m+=b[h],v[h]=E}else v[h]="?"}var A=s.next(32);for(A.write_shift(4,318902576),A.write_shift(4,l.length),A.write_shift(2,296+32*p),A.write_shift(2,m),h=0;h<4;++h)A.write_shift(4,0);for(A.write_shift(4,0|(+t[o]||3)<<8),h=0,d=0;h<c.length;++h)if(null!=c[h]){var y=s.next(32),x=(c[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,x,"sbcs"),y.write_shift(1,"?"==v[h]?"C":v[h],"sbcs"),y.write_shift(4,d),y.write_shift(1,b[h]||a[v[h]]||0),y.write_shift(1,T[h]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),d+=b[h]||a[v[h]]||0}var C=s.next(264);for(C.write_shift(4,13),h=0;h<65;++h)C.write_shift(4,0);for(h=0;h<l.length;++h){var _=s.next(m);for(_.write_shift(1,0),d=0;d<c.length;++d)if(null!=c[d])switch(v[d]){case"L":_.write_shift(1,null==l[h][d]?63:l[h][d]?84:70);break;case"B":_.write_shift(8,l[h][d]||0,"f");break;case"N":var N="0";for("number"==typeof l[h][d]&&(N=l[h][d].toFixed(T[d]||0)),p=0;p<b[d]-N.length;++p)_.write_shift(1,32);_.write_shift(1,N,"sbcs");break;case"D":l[h][d]?(_.write_shift(4,("0000"+l[h][d].getFullYear()).slice(-4),"sbcs"),_.write_shift(2,("00"+(l[h][d].getMonth()+1)).slice(-2),"sbcs"),_.write_shift(2,("00"+l[h][d].getDate()).slice(-2),"sbcs")):_.write_shift(8,"00000000","sbcs");break;case"C":var O=String(null!=l[h][d]?l[h][d]:"").slice(0,b[d]);for(_.write_shift(1,O,"sbcs"),p=0;p<b[d]-O.length;++p)_.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),wn=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+_e(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?T(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:T(a)};function n(e,n){var s,i=e.split(/[\n\r]+/),c=-1,o=-1,l=0,f=0,h=[],d=[],p=null,m={},g=[],v=[],b=[],T=0;for(+n.codepage>=0&&u(+n.codepage);l!==i.length;++l){T=0;var E,w=i[l].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),S=w.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),k=S[0];if(w.length>0)switch(k){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==S[1].charAt(0)&&d.push(w.slice(3).replace(/;;/g,";"));break;case"C":var A=!1,y=!1,x=!1,C=!1,_=-1,N=-1;for(f=1;f<S.length;++f)switch(S[f].charAt(0)){case"A":case"G":break;case"X":o=parseInt(S[f].slice(1))-1,y=!0;break;case"Y":for(c=parseInt(S[f].slice(1))-1,y||(o=0),s=h.length;s<=c;++s)h[s]=[];break;case"K":'"'===(E=S[f].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(Ge(E))?isNaN(je(E).getDate())||(E=Ve(E)):(E=Ge(E),null!==p&&ve(p)&&(E=Pe(E))),A=!0;break;case"E":C=!0;var O=ys(S[f].slice(1),{r:c,c:o});h[c][o]=[h[c][o],O];break;case"S":x=!0,h[c][o]=[h[c][o],"S5S"];break;case"R":_=parseInt(S[f].slice(1))-1;break;case"C":N=parseInt(S[f].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}if(A&&(h[c][o]&&2==h[c][o].length?h[c][o][0]=E:h[c][o]=E,p=null),x){if(C)throw new Error("SYLK shared formula cannot have own formula");var R=_>-1&&h[_][N];if(!R||!R[1])throw new Error("SYLK shared formula cannot find base");h[c][o][1]=_s(R[1],{r:c-_,c:o-N})}break;case"F":var I=0;for(f=1;f<S.length;++f)switch(S[f].charAt(0)){case"X":o=parseInt(S[f].slice(1))-1,++I;break;case"Y":for(c=parseInt(S[f].slice(1))-1,s=h.length;s<=c;++s)h[s]=[];break;case"M":T=parseInt(S[f].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(S[f].slice(1))];break;case"W":for(b=S[f].slice(1).split(" "),s=parseInt(b[0],10);s<=parseInt(b[1],10);++s)T=parseInt(b[2],10),v[s-1]=0===T?{hidden:!0}:{wch:T},as(v[s-1]);break;case"C":v[o=parseInt(S[f].slice(1))-1]||(v[o]={});break;case"R":g[c=parseInt(S[f].slice(1))-1]||(g[c]={}),T>0?(g[c].hpt=T,g[c].hpx=ss(T)):0===T&&(g[c].hidden=!0);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}I<1&&(p=null);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+w)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),n&&n.sheetRows&&(h=h.slice(0,n.sheetRows)),[h,m]}function s(e,t){var r=function(e,t){switch(t.type){case"base64":return n(S(e),t);case"binary":return n(e,t);case"buffer":return n(k&&Buffer.isBuffer(e)?e.toString("binary"):_(e),t);case"array":return n(He(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),a=r[0],s=r[1],i=Br(a,t);return _e(s).forEach((function(e){i[e]=s[e]})),i}function i(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+Cs(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}return e["|"]=254,{to_workbook:function(e,t){return Lr(s(e,t),t)},to_sheet:s,from_sheet:function(e,t){var r,a,n=["ID;PWXL;N;E"],s=[],c=Dr(e["!ref"]),o=Array.isArray(e),l="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&(a=n,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=Zn(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=Qn(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var a="F;";t.hidden?a+="M0;":t.hpt?a+="M"+20*t.hpt+";":t.hpx&&(a+="M"+20*ns(t.hpx)+";"),a.length>2&&e.push(a+"R"+(r+1))}))}(n,e["!rows"]),n.push("B;Y"+(c.e.r-c.s.r+1)+";X"+(c.e.c-c.s.c+1)+";D"+[c.s.c,c.s.r,c.e.c,c.e.r].join(" "));for(var f=c.s.r;f<=c.e.r;++f)for(var h=c.s.c;h<=c.e.c;++h){var u=Or({r:f,c:h});(r=o?(e[f]||[])[h]:e[u])&&(null!=r.v||r.f&&!r.F)&&s.push(i(r,0,f,h))}return n.join(l)+l+s.join(l)+l+"E"+l}}}(),Sn=function(){function e(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(a<0)){for(var c=r[s].trim().split(","),o=c[0],l=c[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+o){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(Ge(l))?isNaN(je(l).getDate())?i[a][n]=l:i[a][n]=Ve(l):i[a][n]=Ge(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}else i[++a]=[],n=0;return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function t(t,r){return Br(function(t,r){switch(r.type){case"base64":return e(S(t),r);case"binary":return e(t,r);case"buffer":return e(k&&Buffer.isBuffer(t)?t.toString("binary"):_(t),r);case"array":return e(He(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return Lr(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)};return function(r){var a,n=[],s=Dr(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var c=s.s.r;c<=s.e.r;++c){t(n,-1,0,"BOT");for(var o=s.s.c;o<=s.e.c;++o){var l=Or({r:c,c:o});if(a=i?(r[c]||[])[o]:r[l])switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?a.f&&!a.F?t(n,1,0,"="+a.f):t(n,1,0,""):t(n,0,f,"V");break;case"b":t(n,0,a.v?1:0,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=we(a.z||W[14],Re(Ve(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}else t(n,1,0,"")}}t(n,-1,0,"EOD");return n.join("\r\n")}}()}}(),kn=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return Br(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var c=r[s].trim().split(":");if("cell"===c[0]){var o=Nr(c[1]);if(i.length<=o.r)for(a=i.length;a<=o.r;++a)i[a]||(i[a]=[]);switch(a=o.r,n=o.c,c[2]){case"t":i[a][n]=c[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+c[3];break;case"vtf":var l=c[c.length-1];case"vtc":"nl"===c[3]?i[a][n]=!!+c[4]:i[a][n]=+c[4],"vtf"==c[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",n=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function i(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=Rr(t["!ref"]),c=Array.isArray(t),o=i.s.r;o<=i.e.r;++o)for(var l=i.s.c;l<=i.e.c;++l)if(s=Or({r:o,c:l}),(r=c?(t[o]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=Re(Ve(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||we(r.z||W[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}return{to_workbook:function(e,r){return Lr(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,a,n,a,i(e),s].join("\n")}}}(),An=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(Ge(e))?isNaN(je(e).getDate())?t[r][a]=e:t[r][a]=Ve(e):t[r][a]=Ge(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[i.pop()[1]]||44}function n(e,t){var r=t||{},n="",s=r.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=r&&r.FS?r.FS:a(e.slice(0,1024));var c=0,o=0,l=0,f=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g,v,b=null!=r.dateNF?(g=r.dateNF,v=(v="number"==typeof g?W[g]:g).replace(ye,"(\\d+)"),new RegExp("^"+v+"$")):null;function T(){var t=e.slice(f,h),a={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)a.t="z";else if(r.raw)a.t="s",a.v=t;else if(0===t.trim().length)a.t="s",a.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(a.t="s",a.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(a.t="n",a.f=t.slice(1)):(a.t="s",a.v=t);else if("TRUE"==t)a.t="b",a.v=!0;else if("FALSE"==t)a.t="b",a.v=!1;else if(isNaN(l=Ge(t)))if(!isNaN(je(t).getDate())||b&&t.match(b)){a.z=r.dateNF||W[14];var n=0;b&&t.match(b)&&(t=function(e,t,r){var a=-1,n=-1,s=-1,i=-1,c=-1,o=-1;(t.match(ye)||[]).forEach((function(e,t){var l=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":o=l;break;case"m":i>=0?c=l:n=l}})),o>=0&&-1==c&&n>=0&&(c=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return-1==i&&-1==c&&-1==o?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}(0,r.dateNF,t.match(b)||[]),n=1),r.cellDates?(a.t="d",a.v=Ve(t,n)):(a.t="n",a.v=Re(Ve(t,n))),!1!==r.cellText&&(a.w=we(a.z,a.v instanceof Date?Re(a.v):a.v)),r.cellNF||delete a.z}else a.t="s",a.v=t;else a.t="n",!1!==r.cellText&&(a.w=t),a.v=l;if("z"==a.t||(r.dense?(s[c]||(s[c]=[]),s[c][o]=a):s[Or({c:o,r:c})]=a),f=h+1,m=e.charCodeAt(f),i.e.c<o&&(i.e.c=o),i.e.r<c&&(i.e.r=c),p==u)++o;else if(o=0,++c,r.sheetRows&&r.sheetRows<=c)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&T())break e}return h-f>0&&T(),s["!ref"]=Ir(i),s}function s(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?n(t,r):Br(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var c=10,o=0,l=0;l<=i;++l)-1==(o=s[l].indexOf(" "))?o=s[l].length:o++,c=Math.max(c,o);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,c).trim(),n,l,f,a),f=1;f<=(s[l].length-c)/10+1;++f)e(s[l].slice(c+10*(f-1),c+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(t,r),r):n(t,r)}function i(e,t){var r="",a="string"==t.type?[0,0,0,0]:so(e,t);switch(t.type){case"base64":r=S(e);break;case"binary":case"string":r=e;break;case"buffer":65001==t.codepage?r=e.toString("utf8"):(t.codepage,r=k&&Buffer.isBuffer(e)?e.toString("binary"):_(e));break;case"array":r=He(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]?r=yt(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=yt(r):t.type,"socialcalc:version:"==r.slice(0,19)?kn.to_sheet("string"==t.type?r:yt(r),t):s(r,t)}return{to_workbook:function(e,t){return Lr(i(e,t),t)},to_sheet:i,from_sheet:function(e){for(var t,r=[],a=Dr(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],c=a.s.c;c<=a.e.c;++c){var o=Or({r:s,c:c});if((t=n?(e[s]||[])[c]:e[o])&&null!=t.v){for(var l=(t.w||(Pr(t),t.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===c?" ":""))}else i.push("          ")}r.push(i.join(""))}return r.join("\n")}}}();var yn=function(){function e(e,t,r){if(e){gr(e,e.l||0);for(var a=r.Enum||T;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),c=e.l+i,o=s.f&&s.f(e,i,r);if(e.l=c,t(o,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{},n=a.dense?[]:{},s="Sheet1",i="",c=0,o={},l=[],f=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=T,e(t,(function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:h=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||W[14],a.cellDates&&(e[1].t="d",e[1].v=Pe(e[1].v))),a.qpro&&e[3]>c&&(n["!ref"]=Ir(h),o[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s=i||"Sheet"+(c+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[Or(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Or(e[0])]=e[1]}}),a);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);a.Enum=E,14==t[2]&&(a.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>c&&(n["!ref"]=Ir(h),o[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s="Sheet"+(c+1)),u>0&&e[0].r>=u)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Or(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==c&&(s=e[1])}}),a)}if(n["!ref"]=Ir(h),o[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:o};for(var d={},p=[],m=0;m<f.length;++m)o[l[m]]?(p.push(f[m]||l[m]),d[f[m]]=o[f[m]]||o[l[m]]):(p.push(f[m]),d[f[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,0,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function s(e,t,r){var a=br(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function i(e,t,r){var a=br(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}function c(e,t,r){var a=br(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}function o(e,t,r){var a=32768&t;return t=(a?e:0)+((t&=-32769)>=8192?t-16384:t),(a?"":"$")+(r?_r(t):xr(t))}var l={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},f=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function d(e,t,r,a){var n=br(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function p(e,t){var r=h(e),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(r[1].t="e",r[1].v=15):0===a&&3489660928===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function m(e,t,r,a){var n=br(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s,i=0,c=0,o=0;return a<0&&(i=1,a=-a),c=0|Math.log2(a),0==(2147483648&(o=(a/=Math.pow(2,c-31))>>>0))&&(++c,o=(a/=2)>>>0),a-=o,o|=2147483648,o>>>=0,s=(a*=Math.pow(2,32))>>>0,n.write_shift(4,s),n.write_shift(4,o),c+=16383+(i?32768:0),n.write_shift(2,c),n}function g(e,t){var r=h(e),a=e.read_shift(8,"f");return r[1].v=a,r}function v(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function b(e,t){var r=br(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}var T={0:{n:"BOF",f:La},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var s=e.l+t,i=r(e,0,a);if(i[1].v=e.read_shift(8,"f"),a.qpro)e.l=s;else{var c=e.read_shift(2);!function(e,t){gr(e,0);var r=[],a=0,s="",i="",c="",h="";for(;e.l<e.length;){var u=e[e.l++];switch(u){case 0:r.push(e.read_shift(8,"f"));break;case 1:i=o(t[0].c,e.read_shift(2),!0),s=o(t[0].r,e.read_shift(2),!1),r.push(i+s);break;case 2:var d=o(t[0].c,e.read_shift(2),!0),p=o(t[0].r,e.read_shift(2),!1);i=o(t[0].c,e.read_shift(2),!0),s=o(t[0].r,e.read_shift(2),!1),r.push(d+p+":"+i+s);break;case 3:if(e.l<e.length)return void n("error","at node_modules/xlsx/xlsx.mjs:8967","WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var m="";u=e[e.l++];)m+=String.fromCharCode(u);r.push('"'+m.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:h=r.pop(),c=r.pop(),r.push(["AND","OR"][u-20]+"("+c+","+h+")");break;default:if(u<32&&f[u])h=r.pop(),c=r.pop(),r.push(c+f[u]+h);else{if(!l[u])return u<=7?n("error","at node_modules/xlsx/xlsx.mjs:8998","WK1 invalid opcode "+u.toString(16)):u<=24?n("error","at node_modules/xlsx/xlsx.mjs:8999","WK1 unsupported op "+u.toString(16)):u<=30?n("error","at node_modules/xlsx/xlsx.mjs:9000","WK1 invalid opcode "+u.toString(16)):u<=115?n("error","at node_modules/xlsx/xlsx.mjs:9001","WK1 unsupported function opcode "+u.toString(16)):n("error","at node_modules/xlsx/xlsx.mjs:9003","WK1 unrecognized opcode "+u.toString(16));if(69==(a=l[u][1])&&(a=e[e.l++]),a>r.length)return void n("error","at node_modules/xlsx/xlsx.mjs:8993","WK1 bad formula parse 0x"+u.toString(16)+":|"+r.join("|")+"|");var g=r.slice(-a);r.length-=a,r.push(l[u][0]+"("+g.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:n("error","at node_modules/xlsx/xlsx.mjs:9007","WK1 bad formula parse |"+r.join("|")+"|")}(e.slice(e.l,e.l+c),i),e.l+=c}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:v},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},E={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=h(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:p},24:{n:"NUMBER18",f:function(e,t){var r=h(e);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=p(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=h(e),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:g},40:{n:"FORMULA28",f:function(e,t){var r=g(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:v},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&u(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var a,n,o=Er(),l=Dr(e["!ref"]),f=Array.isArray(e),h=[];Cc(o,0,(a=1030,(n=br(2)).write_shift(2,a),n)),Cc(o,6,function(e){var t=br(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(l));for(var d=Math.min(l.e.r,8191),p=l.s.r;p<=d;++p)for(var m=xr(p),g=l.s.c;g<=l.e.c;++g){p===l.s.r&&(h[g]=_r(g));var v=h[g]+m,b=f?(e[p]||[])[g]:e[v];if(b&&"z"!=b.t)if("n"==b.t)(0|b.v)==b.v&&b.v>=-32768&&b.v<=32767?Cc(o,13,i(p,g,b.v)):Cc(o,14,c(p,g,b.v));else Cc(o,15,s(p,g,Pr(b).slice(0,239)))}return Cc(o,1),o.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&u(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var a=Er();Cc(a,0,function(e){var t=br(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],c=e.Sheets[i];if(c&&c["!ref"]){++n;var o=Rr(c["!ref"]);r<o.e.r&&(r=o.e.r),a<o.e.c&&(a=o.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&Cc(a,27,b(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var o=Dr(c["!ref"]),l=Array.isArray(c),f=[],h=Math.min(o.e.r,8191),p=o.s.r;p<=h;++p)for(var g=xr(p),v=o.s.c;v<=o.e.c;++v){p===o.s.r&&(f[v]=_r(v));var T=f[v]+g,E=l?(c[p]||[])[v]:c[T];if(E&&"z"!=E.t)if("n"==E.t)Cc(a,23,m(p,v,i,E.v));else Cc(a,22,d(p,v,i,Pr(E).slice(0,239)))}++i}}return Cc(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(C(S(e)),r);case"binary":return t(C(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}();var xn=function(){var e=Ct("t"),t=Ct("rPr");function r(r){var a=r.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:mt(a[1])},s=r.match(t);return s&&(n.s=function(e){var t={},r=e.match(ot),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=ht(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;t.cp=f[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw new Error("Unrecognized rich format "+s[0])}}return t}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(r).filter((function(e){return e.v}))}}(),Cn=function(){var e=/(\r\n|\n)/g;function t(t){var r=[[],t.v,[]];return t.v?(t.s&&function(e,t,r){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),t.push('<span style="'+a.join("")+'">'),e.b&&(t.push("<b>"),r.push("</b>")),e.i&&(t.push("<i>"),r.push("</i>")),e.strike&&(t.push("<s>"),r.push("</s>"));var n=e.valign||"";"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(t.push("<"+n+">"),r.push("</"+n+">")),r.push("</span>")}(t.s,r[0],r[2]),r[0].join("")+r[1].replace(e,"<br/>")+r[2].join("")):""}return function(e){return e.map(t).join("")}}(),_n=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Nn=/<(?:\w+:)?r>/,On=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Rn(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=mt(yt(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=yt(e),r&&(a.h=bt(a.t))):e.match(Nn)&&(a.r=yt(e),a.t=mt(yt((e.replace(On,"").match(_n)||[]).join("").replace(ot,""))),r&&(a.h=Cn(xn(a.r)))),a):{t:""}}var In=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Dn=/<(?:\w+:)?(?:si|sstItem)>/g,Fn=/<\/(?:\w+:)?(?:si|sstItem)>/;function Pn(e){for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function Ln(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function Mn(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function Bn(e){var t=function(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=Ln(e,4),t.U=Ln(e,4),t.W=Ln(e,4),t}(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return t}function Un(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function Vn(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function Hn(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:Un(e,t),v:Vn(e,e.length-e.l)}}function Wn(){throw new Error("File is password-protected: ECMA-376 Extensible")}function zn(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(ot,(function(e){var r=ht(e);switch(ut(r[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":t.forEach((function(e){a[e]=r[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=r.encryptedHmacKey,a.encryptedHmacValue=r.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"<keyEncryptor":a.uri=r.uri;break;case"<encryptedKey":a.encs.push(r);break;default:throw r[0]}})),a}var Gn=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){return 255&((r=e^t)/2|128*r);var r};return function(n){for(var s,i,c,o=Pn(n),l=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],c=0;7!=c;++c)64&i&&(a^=r[n]),i*=2,--n;return a}(o),f=o.length,h=y(16),u=0;16!=u;++u)h[u]=0;for(1==(1&f)&&(s=l>>8,h[f]=a(e[0],s),--f,s=255&l,i=o[o.length-1],h[f]=a(i,s));f>0;)s=l>>8,h[--f]=a(o[f],s),s=255&l,h[--f]=a(o[f],s);for(f=15,c=15-o.length;c>0;)s=l>>8,h[f]=a(e[c],s),--c,s=255&l,h[--f]=a(o[f],s),--f,--c;return h}}(),$n=function(e){var t=0,r=Gn(e);return function(e){var a=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=Gn(e)),s=0;s!=t.length;++s)i=t[s],i=255&((i^=a[r])>>5|i<<3),n[s]=i,++r;return[n,r,a]}("",e,t,r);return t=a[1],a[0]}};function jn(e,t,r,a){var n={key:La(e),verificationBytes:La(e)};return r.password&&(n.verifier=function(e){var t,r,a=0,n=Pn(e),s=n.length+1;for((t=y(s))[0]=n.length,r=1;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((0==(16384&a)?0:1)|a<<1&32767)^t[r];return 52811^a}(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=$n(r.password)),n}function Xn(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var t={},r=t.EncryptionVersionInfo=Ln(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e):a.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=Ln(e,4);if(t-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=Un(e,n),t-=n,r.EncryptionVerifier=Vn(e,t),r}(e,t),a}var Yn=function(){function e(e,r){switch(r.type){case"base64":return t(S(e),r);case"binary":return t(e,r);case"buffer":return t(k&&Buffer.isBuffer(e)?e.toString("binary"):_(e),r);case"array":return t(He(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,c=-1;a=s.exec(e);){if("\\cell"===a[0]){var o=e.slice(i,s.lastIndex-a[0].length);if(" "==o[0]&&(o=o.slice(1)),++c,o.length){var l={v:o,t:"s"};Array.isArray(r)?r[t][c]=l:r[Or({r:t,c:c})]=l}}i=s.lastIndex}c>n.e.c&&(n.e.c=c)})),r["!ref"]=Ir(n),r}return{to_workbook:function(t,r){return Lr(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=Dr(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var c=Or({r:s,c:i});(t=n?(e[s]||[])[i]:e[c])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(Pr(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function Kn(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function Jn(e,t){if(0===t)return e;var r,a,n=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var c,o=0,l=n+s;switch(c=i/(l>1?2-l:l),n){case t:o=((r-a)/i+6)%6;break;case r:o=(a-t)/i+2;break;case a:o=(t-r)/i+4}return[o/6,c,l/2]}((a=(r=e).slice("#"===r[0]?1:0).slice(0,6),[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]));return n[2]=t<0?n[2]*(1+t):1-(1-n[2])*(1-t),Kn(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,c=[i,i,i],o=6*r;if(0!==a)switch(0|o){case 0:case 6:t=s*o,c[0]+=s,c[1]+=t;break;case 1:t=s*(2-o),c[0]+=t,c[1]+=s;break;case 2:t=s*(o-2),c[1]+=s,c[2]+=t;break;case 3:t=s*(4-o),c[1]+=t,c[2]+=s;break;case 4:t=s*(o-4),c[2]+=s,c[0]+=t;break;case 5:t=s*(6-o),c[2]+=t,c[0]+=s}for(var l=0;3!=l;++l)c[l]=Math.round(255*c[l]);return c}(n))}var qn=6;function Zn(e){return Math.floor((e+Math.round(128/qn)/256)*qn)}function Qn(e){return Math.floor((e-5)/qn*100+.5)/100}function es(e){return Math.round((e*qn+5)/qn*256)/256}function ts(e){return es(Qn(Zn(e)))}function rs(e){var t=Math.abs(e-ts(e)),r=qn;if(t>.005)for(qn=1;qn<15;++qn)Math.abs(e-ts(e))<=t&&(t=Math.abs(e-ts(e)),r=qn);qn=r}function as(e){e.width?(e.wpx=Zn(e.width),e.wch=Qn(e.wpx),e.MDW=qn):e.wpx?(e.wch=Qn(e.wpx),e.width=es(e.wch),e.MDW=qn):"number"==typeof e.wch&&(e.width=es(e.wch),e.wpx=Zn(e.width),e.MDW=qn),e.customWidth&&delete e.customWidth}function ns(e){return 96*e/96}function ss(e){return 96*e/96}var is={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};var cs=["numFmtId","fillId","fontId","borderId","xfId"],os=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];var ls=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,c){var o,l={};return s?((o=(s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,t,r){t.NumberFmt=[];for(var a=_e(W),n=0;n<a.length;++n)t.NumberFmt[a[n]]=W[a[n]];var s=e[0].match(ot);if(s)for(n=0;n<s.length;++n){var i=ht(s[n]);switch(ut(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var c=mt(yt(i.formatCode)),o=parseInt(i.numFmtId,10);if(t.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&null!=t.NumberFmt[o];--o);t.NumberFmt[o]=c}Se(c,o)}break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(o,l,c),(o=s.match(a))&&function(e,t,r,a){t.Fonts=[];var n={},s=!1;(e[0].match(ot)||[]).forEach((function(e){var i=ht(e);switch(ut(i[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":i.val&&(n.name=yt(i.val));break;case"<b":n.bold=i.val?Et(i.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=i.val?Et(i.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(i.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=i.val?Et(i.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=i.val?Et(i.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=i.val?Et(i.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=i.val?Et(i.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=i.val?Et(i.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":i.val&&(n.sz=+i.val);break;case"<vertAlign":i.val&&(n.vertAlign=i.val);break;case"<family":i.val&&(n.family=parseInt(i.val,10));break;case"<scheme":i.val&&(n.scheme=i.val);break;case"<charset":if("1"==i.val)break;i.codepage=f[parseInt(i.val,10)];break;case"<color":if(n.color||(n.color={}),i.auto&&(n.color.auto=Et(i.auto)),i.rgb)n.color.rgb=i.rgb.slice(-6);else if(i.indexed){n.color.index=parseInt(i.indexed,10);var c=ca[n.color.index];81==n.color.index&&(c=ca[1]),c||(c=ca[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else i.theme&&(n.color.theme=parseInt(i.theme,10),i.tint&&(n.color.tint=parseFloat(i.tint)),i.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=Jn(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<AlternateContent":case"<ext":s=!0;break;case"</AlternateContent>":case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+i[0]+" in fonts")}}))}(o,l,i,c),(o=s.match(r))&&function(e,t,r,a){t.Fills=[];var n={},s=!1;(e[0].match(ot)||[]).forEach((function(e){var r=ht(e);switch(ut(r[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":r.patternType&&(n.patternType=r.patternType);break;case"<bgColor":n.bgColor||(n.bgColor={}),r.indexed&&(n.bgColor.indexed=parseInt(r.indexed,10)),r.theme&&(n.bgColor.theme=parseInt(r.theme,10)),r.tint&&(n.bgColor.tint=parseFloat(r.tint)),r.rgb&&(n.bgColor.rgb=r.rgb.slice(-6));break;case"<fgColor":n.fgColor||(n.fgColor={}),r.theme&&(n.fgColor.theme=parseInt(r.theme,10)),r.tint&&(n.fgColor.tint=parseFloat(r.tint)),null!=r.rgb&&(n.fgColor.rgb=r.rgb.slice(-6));break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in fills")}}))}(o,l,0,c),(o=s.match(n))&&function(e,t,r,a){t.Borders=[];var n={},s=!1;(e[0].match(ot)||[]).forEach((function(e){var r=ht(e);switch(ut(r[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":n={},r.diagonalUp&&(n.diagonalUp=Et(r.diagonalUp)),r.diagonalDown&&(n.diagonalDown=Et(r.diagonalDown)),t.Borders.push(n);break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in borders")}}))}(o,l,0,c),(o=s.match(t))&&function(e,t,r){var a;t.CellXf=[];var n=!1;(e[0].match(ot)||[]).forEach((function(e){var s=ht(e),i=0;switch(ut(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(delete(a=s)[0],i=0;i<cs.length;++i)a[cs[i]]&&(a[cs[i]]=parseInt(a[cs[i]],10));for(i=0;i<os.length;++i)a[os[i]]&&(a[os[i]]=Et(a[os[i]]));if(t.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[i]){a.numFmtId=i;break}t.CellXf.push(a);break;case"<alignment":case"<alignment/>":var c={};s.vertical&&(c.vertical=s.vertical),s.horizontal&&(c.horizontal=s.horizontal),null!=s.textRotation&&(c.textRotation=s.textRotation),s.indent&&(c.indent=s.indent),s.wrapText&&(c.wrapText=Et(s.wrapText)),a.alignment=c;break;case"<AlternateContent":case"<ext":n=!0;break;case"</AlternateContent>":case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}(o,l,c),l):l}}();var fs=vr;var hs=vr;var us=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function ds(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(ot)||[]).forEach((function(e){var n=ht(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[us.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}function ps(){}function ms(){}var gs=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,vs=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,bs=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;var Ts=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Es(e,t){var r;e&&0!==e.length||(e=function(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[st];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}());var a={};if(!(r=e.match(Ts)))throw new Error("themeElements not found in theme");return function(e,t,r){var a;t.themeElements={},[["clrScheme",gs,ds],["fontScheme",vs,ps],["fmtScheme",bs,ms]].forEach((function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)}))}(r[0],a,t),a.raw=e,a}function ws(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){return vr(e,t)}(e,4);break;case 2:t.xclrValue=$a(e);break;case 3:t.xclrValue=function(e){return e.read_shift(4)}(e)}return e.l+=8,t}function Ss(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=ws(e);break;case 6:a[1]=function(e,t){return vr(e,t)}(e,r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function ks(e,t,r,a){var n,s=Array.isArray(e);t.forEach((function(t){var i=Nr(t.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[t.ref]=n;var c=Dr(e["!ref"]||"BDWGO1000001:A1");c.s.r>i.r&&(c.s.r=i.r),c.e.r<i.r&&(c.e.r=i.r),c.s.c>i.c&&(c.s.c=i.c),c.e.c<i.c&&(c.e.c=i.c);var o=Ir(c);o!==e["!ref"]&&(e["!ref"]=o)}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}))}var As=Ur;var ys=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var c=a.length>0?0|parseInt(a,10):0,o=n.length>0?0|parseInt(n,10):0;return s?o+=t.c:--o,i?c+=t.r:--c,r+(s?"":"$")+_r(o)+(i?"":"$")+xr(c)}return function(a,n){return t=n,a.replace(e,r)}}(),xs=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Cs=function(){return function(e,t){return e.replace(xs,(function(e,r,a,n,s,i){var c=Cr(n)-(a?0:t.c),o=yr(i)-(s?0:t.r);return r+"R"+(0==o?"":s?o+1:"["+o+"]")+"C"+(0==c?"":a?c+1:"["+c+"]")}))}}();function _s(e,t){return e.replace(xs,(function(e,r,a,n,s,i){return r+("$"==a?a+n:_r(Cr(n)+t.c))+("$"==s?s+i:xr(yr(i)+t.r))}))}function Ns(e,t,r){var a=Rr(t).s,n=Nr(r);return _s(e,{r:n.r-a.r,c:n.c-a.c})}function Os(e){return e.replace(/_xlfn\./g,"")}function Rs(e){e.l+=1}function Is(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Ds(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Fs(e);12==r.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=Is(e,2),c=Is(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Fs(e){var t=Is(e,2),r=Is(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Ps(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=Is(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var a=e.read_shift(r&&12==r.biff?4:2),n=Is(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Ls(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function Ms(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function Bs(e){return[e.read_shift(1),e.read_shift(1)]}function Us(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=Pa(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=oa[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Zr(e);break;case 2:r[1]=Wa(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Vs(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?qr:Ja)(e));return n}function Hs(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var c=0;c!=n;++c)i[s][c]=Us(e,r.biff);return i}function Ws(e,t,r){return e.l+=2,[Ls(e)]}function zs(e){return e.l+=6,[]}function Gs(e){return e.l+=2,[La(e),1&e.read_shift(2)]}var $s=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var js={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:vr},3:{n:"PtgAdd",f:Rs},4:{n:"PtgSub",f:Rs},5:{n:"PtgMul",f:Rs},6:{n:"PtgDiv",f:Rs},7:{n:"PtgPower",f:Rs},8:{n:"PtgConcat",f:Rs},9:{n:"PtgLt",f:Rs},10:{n:"PtgLe",f:Rs},11:{n:"PtgEq",f:Rs},12:{n:"PtgGe",f:Rs},13:{n:"PtgGt",f:Rs},14:{n:"PtgNe",f:Rs},15:{n:"PtgIsect",f:Rs},16:{n:"PtgUnion",f:Rs},17:{n:"PtgRange",f:Rs},18:{n:"PtgUplus",f:Rs},19:{n:"PtgUminus",f:Rs},20:{n:"PtgPercent",f:Rs},21:{n:"PtgParen",f:Rs},22:{n:"PtgMissArg",f:Rs},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Ba(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,oa[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Zr(e)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[di[n],ui[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[88==a?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[n,(0===s[0]?ui:hi)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,Ps(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,Ds(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:vr},40:{n:"PtgMemNoMem",f:vr},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=function(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),a=(32768&t)>>15,n=(16384&t)>>14;return t&=16383,1==a&&t>=8192&&(t-=16384),1==n&&r>=128&&(r-=256),{r:t,c:r,cRel:n,rRel:a}}(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,c=(32768&s)>>15;if(s&=16383,1==c)for(;n>524287;)n-=1048576;if(1==i)for(;s>8191;)s-=16384;return{r:n,c:s,cRel:i,rRel:c}}(e,0,r);return[a,n]}},45:{n:"PtgAreaN",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=function(e,t,r){if(r.biff<8)return Fs(e);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=Is(e,2),i=Is(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,0,r);return[a,n]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,Ps(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12,6;break;case 12:12}return[a,n,Ds(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},Xs={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Ys={1:{n:"PtgElfLel",f:Gs},2:{n:"PtgElfRw",f:Ws},3:{n:"PtgElfCol",f:Ws},6:{n:"PtgElfRwV",f:Ws},7:{n:"PtgElfColV",f:Ws},10:{n:"PtgElfRadical",f:Ws},11:{n:"PtgElfRadicalS",f:zs},13:{n:"PtgElfColS",f:zs},15:{n:"PtgElfColSV",f:zs},16:{n:"PtgElfRadicalLel",f:Gs},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2);return{ixti:t,coltype:3&r,rt:$s[r>>2&31],idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Ks={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Ms},33:{n:"PtgAttrBaxcel",f:Ms},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),Bs(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),Bs(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function Js(e,t,r,a){if(a.biff<8)return vr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=Hs(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=Vs(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&12==a.biff&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return 0!==(t=n-e.l)&&s.push(vr(e,t)),s}function qs(e,t,r){for(var a,n,s=e.l+t,i=[];s!=e.l;)t=s-e.l,n=e[e.l],a=js[n]||js[Xs[n]],24!==n&&25!==n||(a=(24===n?Ys:Ks)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,t,r)]):vr(e,t);return i}function Zs(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];if(i)if(2===i[0])n.push('"'+i[1].replace(/"/g,'""')+'"');else n.push(i[1]);else n.push("")}t.push(n.join(","))}return t.join(";")}var Qs={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function ei(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function ti(e,t,r){var a=ei(e,t,r);return"#REF"==a?a:function(e,t){if(!(e||t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function ri(e,t,r,a,n){var s,i,c,o,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var T=e[0][v];switch(T[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=ze(" ",e[0][m][1][1]);break;case 1:g=ze("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+Qs[T[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":c=wr(T[1][1],f,n),h.push(kr(c,l));break;case"PtgRefN":c=r?wr(T[1][1],r,n):T[1][1],h.push(kr(c,l));break;case"PtgRef3d":u=T[1][1],c=wr(T[1][2],f,n),p=ti(a,u,n),h.push(p+"!"+kr(c,l));break;case"PtgFunc":case"PtgFuncVar":var E=T[1][0],w=T[1][1];E||(E=0);var S=0==(E&=127)?[]:h.slice(-E);h.length-=E,"User"===w&&(w=S.shift()),h.push(w+"("+S.join(",")+")");break;case"PtgBool":h.push(T[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(T[1]);break;case"PtgNum":h.push(String(T[1]));break;case"PtgStr":h.push('"'+T[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":o=Sr(T[1][1],r?{s:r}:f,n),h.push(Ar(o,n));break;case"PtgArea":o=Sr(T[1][1],f,n),h.push(Ar(o,n));break;case"PtgArea3d":u=T[1][1],o=T[1][2],p=ti(a,u,n),h.push(p+"!"+Ar(o,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=T[1][2];var k=(a.names||[])[d-1]||(a[0]||[])[d],A=k?k.Name:"SH33TJSNAME"+String(d);A&&"_xlfn."==A.slice(0,6)&&!n.xlfn&&(A=A.slice(6)),h.push(A);break;case"PtgNameX":var y,x=T[1][1];if(d=T[1][2],!(n.biff<=5)){var C="";if(14849==((a[x]||[])[0]||[])[0]||(1025==((a[x]||[])[0]||[])[0]?a[x][d]&&a[x][d].itab>0&&(C=a.SheetNames[a[x][d].itab-1]+"!"):C=a.SheetNames[d-1]+"!"),a[x]&&a[x][d])C+=a[x][d].Name;else if(a[0]&&a[0][d])C+=a[0][d].Name;else{var _=(ei(a,x,n)||"").split(";;");_[d-1]?C=_[d-1]:C+="SH33TJSERRX"}h.push(C);break}x<0&&(x=-x),a[x]&&(y=a[x][d]),y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var N="(",O=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:N=ze(" ",e[0][m][1][1])+N;break;case 3:N=ze("\r",e[0][m][1][1])+N;break;case 4:O=ze(" ",e[0][m][1][1])+O;break;case 5:O=ze("\r",e[0][m][1][1])+O;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(N+h.pop()+O);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":c={c:T[1][1],r:T[1][0]};var R={c:r.c,r:r.r};if(a.sharedf[Or(c)]){var I=a.sharedf[Or(c)];h.push(ri(I,f,R,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(c.c<i[0].s.c||c.c>i[0].e.c||c.r<i[0].s.r||c.r>i[0].e.r)){h.push(ri(i[1],f,R,a,n)),D=!0;break}D||h.push(T[1])}break;case"PtgArray":h.push("{"+Zs(T[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+T[1].idx+"[#"+T[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(T))}if(3!=n.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var F=!0;switch((T=e[0][m])[1][0]){case 4:F=!1;case 0:g=ze(" ",T[1][1]);break;case 5:F=!1;case 1:g=ze("\r",T[1][1]);break;default:if(g="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+T[1][0])}h.push((F?g:"")+h.pop()+(F?"":g)),m=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return h[0]}function ai(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],vr(e,t-2)];var c=qs(e,i,r);return t!==i+s&&(a=Js(e,t-i-s,c,r)),e.l=n,[c,a]}function ni(e,t,r){var a,n=e.l+t,s=e.read_shift(2),i=qs(e,s,r);return 65535==s?[[],vr(e,t-2)]:(t!==s+2&&(a=Js(e,n-s-2,i,r)),[i,a])}function si(e,t,r){var a=e.l+t,n=Xa(e);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==cr(e,e.l+6))return[Zr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=function(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],vr(e,t-2)];var c=qs(e,i,r);return t!==i+s&&(a=Js(e,t-i-s,c,r)),e.l=n,[c,a]}(e,a-e.l,r);return{cell:n,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function ii(e,t,r){var a=e.read_shift(4),n=qs(e,a,r),s=e.read_shift(4);return[n,s>0?Js(e,s,n,r):null]}var ci=ii,oi=ii,li=ii,fi=ii,hi={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},ui={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},di={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function pi(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,t){return t.replace(/\./g,"")}))).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function mi(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var gi={},vi={};function bi(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function Ti(e,t,r,a,n,s){try{a.cellNF&&(e.z=W[t])}catch(c){if(a.WTF)throw c}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=Ve(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==W[t]&&Se(Ae[t]||"General",t),"e"===e.t)e.w=e.w||oa[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v);else if("d"===e.t){var i=Re(e.v);e.w=(0|i)===i?i.toString(10):Q(i)}else{if(void 0===e.v)return"";e.w=ee(e.v,vi)}else"d"===e.t?e.w=we(t,Re(e.v),vi):e.w=we(t,e.v,vi)}catch(c){if(a.WTF)throw c}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Jn(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Jn(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(a.WTF&&s.Fills)throw c}}}var Ei=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,wi=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,Si=/<(?:\w:)?hyperlink [^>]*>/gm,ki=/"(\w*:\w*)"/,Ai=/<(?:\w:)?col\b[^>]*[\/]?>/g,yi=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,xi=/<(?:\w:)?pageMargins[^>]*\/>/g,Ci=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,_i=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Ni=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Oi(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var c=t.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(wi);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(Ci);u?Ri(u[0],c,n,r):(u=l.match(_i))&&function(e,t,r,a,n){Ri(e.slice(0,e.indexOf(">")),r,a,n)}(u[0],u[1],c,n,r);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(ki);p&&function(e,t){var r=Dr(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=Ir(r))}(c,p[1])}var m=l.match(Ni);m&&m[1]&&function(e,t){t.Views||(t.Views=[{}]);(e.match(Ii)||[]).forEach((function(e,r){var a=ht(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),Et(a.rightToLeft)&&(t.Views[r].RTL=!0)}))}(m[1],n);var g=[];if(t.cellStyles){var v=l.match(Ai);v&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=ht(t[a],!0);n.hidden&&(n.hidden=Et(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,rs(n.width)),as(n);s<=i;)e[s++]=We(n)}}(g,v)}h&&Di(h[1],c,t,o,s,i);var b=f.match(yi);b&&(c["!autofilter"]=function(e){return{ref:(e.match(/ref="([^"]*)"/)||[])[1]}}(b[0]));var T=[],E=f.match(Ei);if(E)for(d=0;d!=E.length;++d)T[d]=Dr(E[d].slice(E[d].indexOf('"')+1));var w=f.match(Si);w&&function(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=ht(yt(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+mt(s.location))):(s.Target="#"+mt(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=Dr(s.ref),o=c.s.r;o<=c.e.r;++o)for(var l=c.s.c;l<=c.e.c;++l){var f=Or({c:l,r:o});a?(e[o]||(e[o]=[]),e[o][l]||(e[o][l]={t:"z",v:void 0}),e[o][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(c,w,a);var S,k,A=f.match(xi);if(A&&(c["!margins"]=(S=ht(A[0]),k={},["left","right","top","bottom","header","footer"].forEach((function(e){S[e]&&(k[e]=parseFloat(S[e]))})),k)),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=Ir(o)),t.sheetRows>0&&c["!ref"]){var y=Dr(c["!ref"]);t.sheetRows<=+y.e.r&&(y.e.r=t.sheetRows-1,y.e.r>o.e.r&&(y.e.r=o.e.r),y.e.r<y.s.r&&(y.s.r=y.e.r),y.e.c>o.e.c&&(y.e.c=o.e.c),y.e.c<y.s.c&&(y.s.c=y.e.c),c["!fullref"]=c["!ref"],c["!ref"]=Ir(y))}return g.length>0&&(c["!cols"]=g),T.length>0&&(c["!merges"]=T),c}function Ri(e,t,r,a){var n=ht(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=mt(yt(n.codeName)))}var Ii=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;var Di=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=Ct("v"),i=Ct("f");return function(c,o,l,f,h,u){for(var d,p,m,g,v,b=0,T="",E=[],w=[],S=0,k=0,A=0,y="",x=0,C=0,_=0,N=0,O=Array.isArray(u.CellXf),R=[],I=[],D=Array.isArray(o),F=[],P={},L=!1,M=!!l.sheetStubs,B=c.split(t),U=0,V=B.length;U!=V;++U){var H=(T=B[U].trim()).length;if(0!==H){var z=0;e:for(b=0;b<H;++b)switch(T[b]){case">":if("/"!=T[b-1]){++b;break e}if(l&&l.cellStyles){if(x=null!=(p=ht(T.slice(z,b),!0)).r?parseInt(p.r,10):x+1,C=-1,l.sheetRows&&l.sheetRows<x)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=ss(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[x-1]=P)}break;case"<":z=b}if(z>=b)break;if(x=null!=(p=ht(T.slice(z,b),!0)).r?parseInt(p.r,10):x+1,C=-1,!(l.sheetRows&&l.sheetRows<x)){f.s.r>x-1&&(f.s.r=x-1),f.e.r<x-1&&(f.e.r=x-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=ss(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[x-1]=P)),E=T.slice(b).split(e);for(var G=0;G!=E.length&&"<"==E[G].trim().charAt(0);++G);for(E=E.slice(G),b=0;b!=E.length;++b)if(0!==(T=E[b].trim()).length){if(w=T.match(r),S=b,k=0,A=0,T="<c "+("<"==T.slice(0,1)?">":"")+T,null!=w&&2===w.length){for(S=0,y=w[1],k=0;k!=y.length&&!((A=y.charCodeAt(k)-64)<1||A>26);++k)S=26*S+A;C=--S}else++C;for(k=0;k!=T.length&&62!==T.charCodeAt(k);++k);if(++k,(p=ht(T.slice(0,k),!0)).r||(p.r=Or({r:x-1,c:C})),d={t:""},null!=(w=(y=T.slice(k)).match(s))&&""!==w[1]&&(d.v=mt(w[1])),l.cellFormula){if(null!=(w=y.match(i))&&""!==w[1]){if(d.f=mt(yt(w[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=Os(d.f)),w[0].indexOf('t="array"')>-1)d.F=(y.match(n)||[])[1],d.F.indexOf(":")>-1&&R.push([Dr(d.F),d.F]);else if(w[0].indexOf('t="shared"')>-1){g=ht(w[0]);var $=mt(yt(w[1]));l.xlfn||($=Os($)),I[parseInt(g.si,10)]=[g,$,p.r]}}else(w=y.match(/<f[^>]*\/>/))&&I[(g=ht(w[0])).si]&&(d.f=Ns(I[g.si][1],I[g.si][2],p.r));var j=Nr(p.r);for(k=0;k<R.length;++k)j.r>=R[k][0].s.r&&j.r<=R[k][0].e.r&&j.c>=R[k][0].s.c&&j.c<=R[k][0].e.c&&(d.F=R[k][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>C&&(f.s.c=C),f.e.c<C&&(f.e.c=C),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!M)continue;d.t="z"}else m=gi[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?yt(d.v):"",l.cellHTML&&(d.h=bt(d.v));break;case"inlineStr":w=y.match(a),d.t="s",null!=w&&(m=Rn(w[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=Et(d.v);break;case"d":l.cellDates?d.v=Ve(d.v,1):(d.v=Re(Ve(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=la[d.v]}if(_=N=0,v=null,O&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(_=v.numFmtId),l.cellStyles&&null!=v.fillId&&(N=v.fillId)),Ti(d,_,N,l,h,u),l.cellDates&&O&&"n"==d.t&&ve(W[_])&&(d.t="d",d.v=Pe(d.v)),p.cm&&l.xlmeta){var X=(l.xlmeta.Cell||[])[+p.cm-1];X&&"XLDAPR"==X.type&&(d.D=!0)}if(D){var Y=Nr(p.r);o[Y.r]||(o[Y.r]=[]),o[Y.r][Y.c]=d}else o[p.r]=d}}}}F.length>0&&(o["!rows"]=F)}}();var Fi=qr;function Pi(e){return[Gr(e),Zr(e),"n"]}var Li=qr;var Mi=["left","right","top","bottom","header","footer"];function Bi(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach((function(e){var t=function(e){var t,r=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach((function(e){var t=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}));var n=mt((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach((function(e){t=e.replace(/<.*?>/g,"")})),[r,n,t]}(e);f.s.r=f.s.c=0,f.e.c=c,l=_r(c),t[0].forEach((function(e,r){i[l+xr(r)]={t:"n",v:e,z:t[1]},o=r})),f.e.r<o&&(f.e.r=o),++c})),c>0&&(i["!ref"]=Ir(f)),i}var Ui=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Vi=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Hi=[],Wi=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function zi(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=Et(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function Gi(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=Et(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function $i(e){Gi(e.WBProps,Ui),Gi(e.CalcPr,Wi),zi(e.WBView,Vi),zi(e.Sheets,Hi),vi.date1904=Et(e.WBProps.date1904)}var ji="][*?/\\".split("");var Xi=/<\w+:workbook/;function Yi(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function Ki(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],yc[16]={n:"BrtFRTArchID$",f:Yi},Tr(e,(function(e,c,o){switch(o){case 156:i.SheetNames.push(e.name),r.Sheets.push(e);break;case 153:r.WBProps=e;break;case 39:null!=e.Sheet&&(t.SID=e.Sheet),e.Ref=ri(e.Ptg,0,null,i,t),delete t.SID,delete e.Ptg,s.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([o,e]):i[0]=[o,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 35:case 37:a.push(o),n=!0;break;case 36:case 38:a.pop(),n=!1;break;default:if(c.T);else if(!n||t.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}}),t),$i(r),r.Names=s,r.supbooks=i,r}(e,r):function(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(ot,(function(c,o){var l=ht(c);switch(ut(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":c.match(Xi)&&(n="xmlns"+c.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":Ui.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=Et(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(r.WBProps.CodeName=yt(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=mt(yt(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=yt(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),Et(l.hidden||"0")&&(s.Hidden=!0),i=o+c.length;break;case"</definedName>":s.Ref=mt(yt(e.slice(i,o))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return c})),-1===Bt.indexOf(r.xmlns))throw new Error("Unknown Namespace: "+r.xmlns);return $i(r),r}(e,r)}function Ji(e,t,r,a,n,s,i,c){return".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var c=t||{};a||(a={"!id":{}});var o,l,f,h,u,d,p,m,g,v,b=c.dense?[]:{},T={s:{r:2e6,c:2e6},e:{r:0,c:0}},E=!1,w=!1,S=[];c.biff=12,c["!row"]=0;var k=0,A=!1,y=[],x={},C=c.supbooks||n.supbooks||[[]];if(C.sharedf=x,C.arrayf=y,C.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!c.supbooks&&(c.supbooks=C,n.Names))for(var _=0;_<n.Names.length;++_)C[0][_+1]=n.Names[_];var N,O=[],R=[],I=!1;if(yc[16]={n:"BrtShortReal",f:Pi},Tr(e,(function(e,t,_){if(!w)switch(_){case 148:o=e;break;case 0:l=e,c.sheetRows&&c.sheetRows<=l.r&&(w=!0),g=xr(u=l.r),c["!row"]=l.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=ss(e.hpt)),R[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(f={t:e[2]},e[2]){case"n":f.v=e[1];break;case"s":m=gi[e[1]],f.v=m.t,f.r=m.r;break;case"b":f.v=!!e[1];break;case"e":f.v=e[1],!1!==c.cellText&&(f.w=oa[f.v]);break;case"str":f.t="s",f.v=e[1];break;case"is":f.t="s",f.v=e[1].t}if((h=i.CellXf[e[0].iStyleRef])&&Ti(f,h.numFmtId,null,c,s,i),d=-1==e[0].c?d+1:e[0].c,c.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[_r(d)+g]=f,c.cellFormula){for(A=!1,k=0;k<y.length;++k){var D=y[k];l.r>=D[0].s.r&&l.r<=D[0].e.r&&d>=D[0].s.c&&d<=D[0].e.c&&(f.F=Ir(D[0]),A=!0)}!A&&e.length>3&&(f.f=e[3])}if(T.s.r>l.r&&(T.s.r=l.r),T.s.c>d&&(T.s.c=d),T.e.r<l.r&&(T.e.r=l.r),T.e.c<d&&(T.e.c=d),c.cellDates&&h&&"n"==f.t&&ve(W[h.numFmtId])){var F=X(f.v);F&&(f.t="d",f.v=new Date(F.y,F.m-1,F.d,F.H,F.M,F.S,F.u))}N&&("XLDAPR"==N.type&&(f.D=!0),N=void 0);break;case 1:case 12:if(!c.sheetStubs||E)break;f={t:"z",v:void 0},d=-1==e[0].c?d+1:e[0].c,c.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[_r(d)+g]=f,T.s.r>l.r&&(T.s.r=l.r),T.s.c>d&&(T.s.c=d),T.e.r<l.r&&(T.e.r=l.r),T.e.c<d&&(T.e.c=d),N&&("XLDAPR"==N.type&&(f.D=!0),N=void 0);break;case 176:S.push(e);break;case 49:N=((c.xlmeta||{}).Cell||[])[e-1];break;case 494:var P=a["!id"][e.relId];for(P?(e.Target=P.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=P):""==e.relId&&(e.Target="#"+e.loc),u=e.rfx.s.r;u<=e.rfx.e.r;++u)for(d=e.rfx.s.c;d<=e.rfx.e.c;++d)c.dense?(b[u]||(b[u]=[]),b[u][d]||(b[u][d]={t:"z",v:void 0}),b[u][d].l=e):(p=Or({c:d,r:u}),b[p]||(b[p]={t:"z",v:void 0}),b[p].l=e);break;case 426:if(!c.cellFormula)break;y.push(e),(v=c.dense?b[u][d]:b[_r(d)+g]).f=ri(e[1],0,{r:l.r,c:d},C,c),v.F=Ir(e[0]);break;case 427:if(!c.cellFormula)break;x[Or(e[0].s)]=e[1],(v=c.dense?b[u][d]:b[_r(d)+g]).f=ri(e[1],0,{r:l.r,c:d},C,c);break;case 60:if(!c.cellStyles)break;for(;e.e>=e.s;)O[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},I||(I=!0,rs(e.w/256)),as(O[e.e+1]);break;case 161:b["!autofilter"]={ref:Ir(e)};break;case 476:b["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(b["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:case 37:E=!0;break;case 36:case 38:E=!1;break;default:if(t.T);else if(!E||c.WTF)throw new Error("Unexpected record 0x"+_.toString(16))}}),c),delete c.supbooks,delete c["!row"],!b["!ref"]&&(T.s.r<2e6||o&&(o.e.r>0||o.e.c>0||o.s.r>0||o.s.c>0))&&(b["!ref"]=Ir(o||T)),c.sheetRows&&b["!ref"]){var D=Dr(b["!ref"]);c.sheetRows<=+D.e.r&&(D.e.r=c.sheetRows-1,D.e.r>T.e.r&&(D.e.r=T.e.r),D.e.r<D.s.r&&(D.s.r=D.e.r),D.e.c>T.e.c&&(D.e.c=T.e.c),D.e.c<D.s.c&&(D.s.c=D.e.c),b["!fullref"]=b["!ref"],b["!ref"]=Ir(D))}return S.length>0&&(b["!merges"]=S),O.length>0&&(b["!cols"]=O),R.length>0&&(b["!rows"]=R),b}(e,a,r,n,s,i,c):Oi(e,a,r,n,s,i,c)}function qi(e,t,r,a,n,s,i,c){return".bin"===t.slice(-4)?function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return Tr(e,(function(e,a,c){switch(c){case 550:s["!rel"]=e;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(a.T>0);else if(a.T<0);else if(!i||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}(e,a,r,n,s):function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},c=e.match(Ci);return c&&Ri(c[0],0,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}(e,0,r,n,s)}function Zi(e,t,r,a){return".bin"===t.slice(-4)?function(e,t,r){var a={NumberFmt:[]};for(var n in W)a.NumberFmt[n]=W[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return Tr(e,(function(e,n,c){switch(c){case 44:a.NumberFmt[e[0]]=e[1],Se(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&t&&t.themeElements&&t.themeElements.clrScheme&&(e.color.rgb=Jn(t.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(c),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(c);else if(n.T<0)s.pop();else if(!i||r.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}})),a}(e,r,a):ls(e,r,a)}function Qi(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=!1;return Tr(e,(function(e,n,s){switch(s){case 159:r.Count=e[0],r.Unique=e[1];break;case 19:r.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||t.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),r}(e,r):function(e,t){var r=[],a="";if(!e)return r;var n=e.match(In);if(n){a=n[2].replace(Dn,"").split(Fn);for(var s=0;s!=a.length;++s){var i=Rn(a[s].trim(),t);null!=i&&(r[r.length]=i)}n=ht(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}(e,r)}function ec(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=[],n={},s=!1;return Tr(e,(function(e,i,c){switch(c){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:case 37:case 38:break;case 35:s=!0;break;case 36:s=!1;break;default:if(i.T);else if(!s||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}})),r}(e,r):function(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^>]*>(.*)/);t&&r.push(t[1])}}));var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(n){var s=ht(n[0]),i={author:s.authorId&&r[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},c=Nr(s.ref);if(!(t.sheetRows&&t.sheetRows<=c.r)){var o=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!o&&!!o[1]&&Rn(o[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}(e,r)}function tc(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a=[];return Tr(e,(function(e,t,r){if(63===r)a.push(e);else if(!t.T)throw new Error("Unexpected record 0x"+r.toString(16))})),a}(e):function(e){var t=[];if(!e)return t;var r=1;return(e.match(ot)||[]).forEach((function(e){var a=ht(e);switch(a[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?r=a.i:a.i=r,t.push(a)}})),t}(e)}function rc(e,t,r,a){if(".bin"===r.slice(-4))return function(e,t,r,a){if(!e)return e;var n=a||{},s=!1;Tr(e,(function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(t.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+r.toString(16))}}),n)}(e,0,0,a)}function ac(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,c=2;return Tr(e,(function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==c?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==c&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:c=e?1:0;break;case 338:c=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+r.toString(16))}})),a}(e,0,r):function(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(ot,(function(e){var t=ht(e);switch(ut(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":a.Types.push({name:t.name});break;case"<futureMetadata":for(var c=0;c<a.Types.length;++c)a.Types[c].name==t.name&&(n=a.Types[c]);break;case"<rc":1==i?a.Cell.push({type:a.Types[t.t-1].name,index:+t.v}):0==i&&a.Value.push({type:a.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":i=1;break;case"</cellMetadata>":case"</valueMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+t.i);break;default:if(!s&&r.WTF)throw new Error("unrecognized "+t[0]+" in metadata")}return e})),a}(e,0,r)}var nc,sc=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,ic=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function cc(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,c=e.match(sc);if(c)for(i=0;i!=c.length;++i)-1===(s=(n=c[i].match(ic))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function oc(e){var t={};if(1===e.split(/\s+/).length)return t;var r,a,n,s=e.match(sc);if(s)for(n=0;n!=s.length;++n)-1===(a=(r=s[n].match(ic))[1].indexOf(":"))?t[r[1]]=r[2].slice(1,r[2].length-1):t["xmlns:"===r[1].slice(0,6)?"xmlns"+r[1].slice(6):r[1].slice(a+1)]=r[2].slice(1,r[2].length-1);return t}function lc(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=Et(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Ve(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[mt(t)]=n}function fc(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||oa[e.v]:"General"===t?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v):e.w=(a=t||"General",n=e.v,"General"===(s=nc[a]||mt(a))?ee(n):we(s,n))}catch(o){if(r.WTF)throw o}var a,n,s;try{var i=nc[t]||t||"General";if(r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&ve(i)){var c=X(e.v);c&&(e.t="d",e.v=new Date(c.y,c.m-1,c.d,c.H,c.M,c.S,c.u))}}catch(o){if(r.WTF)throw o}}}function hc(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=is[a.Pattern]||a.Pattern)}e[t.ID]=t}function uc(e,t,r,a,n,s,i,c,o,l){var f="General",h=a.StyleID,u={};l=l||{};var d=[],p=0;for(void 0===h&&c&&(h=c.StyleID),void 0===h&&i&&(h=i.StyleID);void 0!==s[h]&&(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),s[h].Parent);)h=s[h].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=Et(e);break;case"String":a.t="s",a.r=Tt(mt(e)),a.v=e.indexOf("<")>-1?mt(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(Ve(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=mt(e):a.v<60&&(a.v=a.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=la[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=Tt(t||e))}if(fc(a,f,l),!1!==l.cellFormula)if(a.Formula){var m=mt(a.Formula);61==m.charCodeAt(0)&&(m=m.slice(1)),a.f=ys(m,n),delete a.Formula,"RC"==a.ArrayRange?a.F=ys("RC:RC",n):a.ArrayRange&&(a.F=ys(a.ArrayRange,n),o.push([Dr(a.F),a.F]))}else for(p=0;p<o.length;++p)n.r>=o[p][0].s.r&&n.r<=o[p][0].e.r&&n.c>=o[p][0].s.c&&n.c<=o[p][0].e.c&&(a.F=o[p][1]);l.cellStyles&&(d.forEach((function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)})),a.s=u),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function dc(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function pc(e,t){var r=t||{};ke();var a=v(Pt(e));"binary"!=r.type&&"array"!=r.type&&"base64"!=r.type||(a=yt(a));var n,s=a.slice(0,1024).toLowerCase(),i=!1;if((1023&(s=s.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&s.indexOf(","),1023&s.indexOf(";"))){var c=We(r);return c.type="string",An.to_workbook(a,c)}if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){s.indexOf("<"+e)>=0&&(i=!0)})),i)return function(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||0==r.length)throw new Error("Invalid HTML: could not find <table>");if(1==r.length)return Lr(_c(r[0],t),t);var a={SheetNames:[],Sheets:{}};return r.forEach((function(e,r){bo(a,_c(e,t),"Sheet"+(r+1))})),a}(a,r);nc={"General Number":"General","General Date":W[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":W[15],"Short Date":W[14],"Long Time":W[19],"Medium Time":W[18],"Short Time":W[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:W[2],Standard:W[4],Percent:W[10],Scientific:W[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var o,l,f=[],h={},u=[],d=r.dense?[]:{},p="",m={},g={},b=cc('<Data ss:Type="String">'),T=0,E=0,w=0,S={s:{r:2e6,c:2e6},e:{r:0,c:0}},k={},A={},y="",x=0,C=[],_={},N={},O=0,R=[],I=[],D={},F=[],P=!1,L=[],M=[],B={},U=0,V=0,H={Sheets:[],WBProps:{date1904:!1}},z={};Lt.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");for(var G="";n=Lt.exec(a);)switch(n[3]=(G=n[3]).toLowerCase()){case"data":if("data"==G){if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break}if(f[f.length-1][1])break;"/"===n[1]?uc(a.slice(T,n.index),y,b,"comment"==f[f.length-1][0]?D:m,{c:E,r:w},k,F[E],g,L,r):(y="",b=cc(n[0]),T=n.index+n[0].length);break;case"cell":if("/"===n[1])if(I.length>0&&(m.c=I),(!r.sheetRows||r.sheetRows>w)&&void 0!==m.v&&(r.dense?(d[w]||(d[w]=[]),d[w][E]=m):d[_r(E)+xr(w)]=m),m.HRef&&(m.l={Target:mt(m.HRef)},m.HRefScreenTip&&(m.l.Tooltip=m.HRefScreenTip),delete m.HRef,delete m.HRefScreenTip),(m.MergeAcross||m.MergeDown)&&(U=E+(0|parseInt(m.MergeAcross,10)),V=w+(0|parseInt(m.MergeDown,10)),C.push({s:{c:E,r:w},e:{c:U,r:V}})),r.sheetStubs)if(m.MergeAcross||m.MergeDown){for(var $=E;$<=U;++$)for(var j=w;j<=V;++j)($>E||j>w)&&(r.dense?(d[j]||(d[j]=[]),d[j][$]={t:"z"}):d[_r($)+xr(j)]={t:"z"});E=U+1}else++E;else m.MergeAcross?E=U+1:++E;else(m=oc(n[0])).Index&&(E=+m.Index-1),E<S.s.c&&(S.s.c=E),E>S.e.c&&(S.e.c=E),"/>"===n[0].slice(-2)&&++E,I=[];break;case"row":"/"===n[1]||"/>"===n[0].slice(-2)?(w<S.s.r&&(S.s.r=w),w>S.e.r&&(S.e.r=w),"/>"===n[0].slice(-2)&&(g=cc(n[0])).Index&&(w=+g.Index-1),E=0,++w):((g=cc(n[0])).Index&&(w=+g.Index-1),B={},("0"==g.AutoFitHeight||g.Height)&&(B.hpx=parseInt(g.Height,10),B.hpt=ns(B.hpx),M[w]=B),"1"==g.Hidden&&(B.hidden=!0,M[w]=B));break;case"worksheet":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));u.push(p),S.s.r<=S.e.r&&S.s.c<=S.e.c&&(d["!ref"]=Ir(S),r.sheetRows&&r.sheetRows<=S.e.r&&(d["!fullref"]=d["!ref"],S.e.r=r.sheetRows-1,d["!ref"]=Ir(S))),C.length&&(d["!merges"]=C),F.length>0&&(d["!cols"]=F),M.length>0&&(d["!rows"]=M),h[p]=d}else S={s:{r:2e6,c:2e6},e:{r:0,c:0}},w=E=0,f.push([n[3],!1]),o=cc(n[0]),p=mt(o.Name),d=r.dense?[]:{},C=[],L=[],M=[],z={name:p,Hidden:0},H.Sheets.push(z);break;case"table":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else{if("/>"==n[0].slice(-2))break;f.push([n[3],!1]),F=[],P=!1}break;case"style":"/"===n[1]?hc(k,A,r):A=cc(n[0]);break;case"numberformat":A.nf=mt(cc(n[0]).Format||"General"),nc[A.nf]&&(A.nf=nc[A.nf]);for(var X=0;392!=X&&W[X]!=A.nf;++X);if(392==X)for(X=57;392!=X;++X)if(null==W[X]){Se(A.nf,X);break}break;case"column":if("table"!==f[f.length-1][0])break;if((l=cc(n[0])).Hidden&&(l.hidden=!0,delete l.Hidden),l.Width&&(l.wpx=parseInt(l.Width,10)),!P&&l.wpx>10){P=!0,qn=6;for(var Y=0;Y<F.length;++Y)F[Y]&&as(F[Y])}P&&as(l),F[l.Index-1||F.length]=l;for(var K=0;K<+l.Span;++K)F[F.length]=We(l);break;case"namedrange":if("/"===n[1])break;H.Names||(H.Names=[]);var J=ht(n[0]),q={Name:J.Name,Ref:ys(J.RefersTo.slice(1),{r:0,c:0})};H.Sheets.length>0&&(q.Sheet=H.Sheets.length-1),H.Names.push(q);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":if("/>"===n[0].slice(-2))break;"/"===n[1]?y+=a.slice(x,n.index):x=n.index+n[0].length;break;case"interior":if(!r.cellStyles)break;A.Interior=cc(n[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===n[0].slice(-2))break;"/"===n[1]?Sa(_,G,a.slice(O,n.index)):O=n.index+n[0].length;break;case"styles":case"workbook":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else f.push([n[3],!1]);break;case"comment":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));dc(D),I.push(D)}else f.push([n[3],!1]),D={a:(o=cc(n[0])).Author};break;case"autofilter":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else if("/"!==n[0].charAt(n[0].length-2)){var Z=cc(n[0]);d["!autofilter"]={ref:ys(Z.Range).replace(/\$/g,"")},f.push([n[3],!0])}break;case"datavalidation":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===n[1]){if((o=f.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&f.push([n[3],!0]);break;default:if(0==f.length&&"document"==n[3])return Lc(a,r);if(0==f.length&&"uof"==n[3])return Lc(a,r);var Q=!0;switch(f[f.length-1][0]){case"officedocumentsettings":switch(n[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:Q=!1}break;case"componentoptions":switch(n[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:Q=!1}break;case"excelworkbook":switch(n[3]){case"date1904":H.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:Q=!1}break;case"workbookoptions":switch(n[3]){case"owcversion":case"height":case"width":break;default:Q=!1}break;case"worksheetoptions":switch(n[3]){case"visible":if("/>"===n[0].slice(-2));else if("/"===n[1])switch(a.slice(O,n.index)){case"SheetHidden":z.Hidden=1;break;case"SheetVeryHidden":z.Hidden=2}else O=n.index+n[0].length;break;case"header":d["!margins"]||bi(d["!margins"]={},"xlml"),isNaN(+ht(n[0]).Margin)||(d["!margins"].header=+ht(n[0]).Margin);break;case"footer":d["!margins"]||bi(d["!margins"]={},"xlml"),isNaN(+ht(n[0]).Margin)||(d["!margins"].footer=+ht(n[0]).Margin);break;case"pagemargins":var ee=ht(n[0]);d["!margins"]||bi(d["!margins"]={},"xlml"),isNaN(+ee.Top)||(d["!margins"].top=+ee.Top),isNaN(+ee.Left)||(d["!margins"].left=+ee.Left),isNaN(+ee.Right)||(d["!margins"].right=+ee.Right),isNaN(+ee.Bottom)||(d["!margins"].bottom=+ee.Bottom);break;case"displayrighttoleft":H.Views||(H.Views=[]),H.Views[0]||(H.Views[0]={}),H.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].left=!0;break;default:Q=!1}break;case"pivottable":case"pivotcache":switch(n[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:Q=!1}break;case"pagebreaks":switch(n[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:Q=!1}break;case"autofilter":switch(n[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:Q=!1}break;case"querytable":switch(n[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:Q=!1}break;case"datavalidation":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:Q=!1}break;case"sorting":case"conditionalformatting":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:Q=!1}break;case"mapinfo":case"schema":case"data":switch(n[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:Q=!1}break;case"smarttags":break;default:Q=!1}if(Q)break;if(n[3].match(/!\[CDATA/))break;if(!f[f.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+f.join("|");if("customdocumentproperties"===f[f.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?lc(N,G,R,a.slice(O,n.index)):(R=n,O=n.index+n[0].length);break}if(r.WTF)throw"Unrecognized tag: "+n[3]+"|"+f.join("|")}var te={};return r.bookSheets||r.bookProps||(te.Sheets=h),te.SheetNames=u,te.Workbook=H,te.SSF=We(W),te.Props=_,te.Custprops=N,te}function mc(e,t){switch(eo(t=t||{}),t.type||"base64"){case"base64":return pc(S(e),t);case"binary":case"buffer":case"file":return pc(e,t);case"array":return pc(_(e),t)}}function gc(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=function(e){return Qr(e,1)}(r),r.length-r.l<=4)return t;var a=r.read_shift(4);return 0==a||a>40?t:(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4||1907505652!==(a=r.read_shift(4))?t:(t.UnicodeClipboardFormat=function(e){return Qr(e,2)}(r),0==(a=r.read_shift(4))||a>40?t:(r.l-=4,void(t.Reserved2=r.read_shift(0,"lpwstr")))))}var vc=[60,1084,2066,2165,2175];function bc(e,t,r,a,n){var s=a,i=[],c=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(c)}i.push(c),r.l+=s;for(var o=cr(r,r.l),l=xc[o],f=0;null!=l&&vc.indexOf(o)>-1;)s=cr(r,r.l+2),f=r.l+4,2066==o?f+=4:2165!=o&&2175!=o||(f+=12),c=r.slice(f,r.l+4+s),i.push(c),r.l+=4+s,l=xc[o=cr(r,r.l)];var h=O(i);gr(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function Tc(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=W[a])}catch(s){if(t.WTF)throw s}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||oa[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Q(e.v):e.w=ee(e.v):e.w=we(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&a&&"n"==e.t&&ve(W[a]||String(a))){var n=X(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function Ec(e,t,r){return{v:e,ixfe:t,t:r}}function wc(e,t){var r,a,s,i,c,o,l,f,h={opts:{}},d={},p=t.dense?[]:{},m={},g={},v=null,b=[],T="",E={},w="",S={},k=[],A=[],y=[],x={Sheets:[],WBProps:{date1904:!1},Views:[{}]},C={},_=function(e){return e<8?ca[e]:e<64&&y[e-8]||ca[e]},N=function(e,t,a){if(!(U>1||a.sheetRows&&e.r>=a.sheetRows)){if(a.cellStyles&&t.XF&&t.XF.data&&function(e,t,r){var a,n=t.XF.data;n&&n.patternType&&r&&r.cellStyles&&(t.s={},t.s.patternType=n.patternType,(a=Kn(_(n.icvFore)))&&(t.s.fgColor={rgb:a}),(a=Kn(_(n.icvBack)))&&(t.s.bgColor={rgb:a}))}(0,t,a),delete t.ixfe,delete t.XF,r=e,w=Or(e),g&&g.s&&g.e||(g={s:{r:0,c:0},e:{r:0,c:0}}),e.r<g.s.r&&(g.s.r=e.r),e.c<g.s.c&&(g.s.c=e.c),e.r+1>g.e.r&&(g.e.r=e.r+1),e.c+1>g.e.c&&(g.e.c=e.c+1),a.cellFormula&&t.f)for(var n=0;n<k.length;++n)if(!(k[n][0].s.c>e.c||k[n][0].s.r>e.r||k[n][0].e.c<e.c||k[n][0].e.r<e.r)){t.F=Ir(k[n][0]),k[n][0].s.c==e.c&&k[n][0].s.r==e.r||delete t.f,t.f&&(t.f=""+ri(k[n][1],0,e,L,O));break}a.dense?(p[e.r]||(p[e.r]=[]),p[e.r][e.c]=t):p[w]=t}},O={enc:!1,sbcch:0,snames:[],sharedf:S,arrayf:k,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(O.password=t.password);var R=[],I=[],D=[],F=[],P=!1,L=[];L.SheetNames=O.snames,L.sharedf=O.sharedf,L.arrayf=O.arrayf,L.names=[],L.XTI=[];var M,B=0,U=0,V=0,H=[],z=[];O.codepage=1200,u(1200);for(var G=!1;e.l<e.length-1;){var $=e.l,j=e.read_shift(2);if(0===j&&10===B)break;var X=e.l===e.length?0:e.read_shift(2),Y=xc[j];if(Y&&Y.f){if(t.bookSheets&&133===B&&133!==j)break;if(B=j,2===Y.r||12==Y.r){var K=e.read_shift(2);if(X-=2,!O.enc&&K!==j&&((255&K)<<8|K>>8)!==j)throw new Error("rt mismatch: "+K+"!="+j);12==Y.r&&(e.l+=10,X-=10)}var J={};if(J=10===j?Y.f(e,X,O):bc(j,Y,e,X,O),0==U&&-1===[9,521,1033,2057].indexOf(B))continue;switch(j){case 34:h.opts.Date1904=x.WBProps.date1904=J;break;case 134:h.opts.WriteProtect=!0;break;case 47:if(O.enc||(e.l=0),O.enc=J,!t.password)throw new Error("File is password-protected");if(null==J.valid)throw new Error("Encryption scheme unsupported");if(!J.valid)throw new Error("Password is incorrect");break;case 92:O.lastuser=J;break;case 66:var q=Number(J);switch(q){case 21010:q=1200;break;case 32768:q=1e4;break;case 32769:q=1252}u(O.codepage=q),G=!0;break;case 317:O.rrtabid=J;break;case 25:O.winlocked=J;break;case 439:h.opts.RefreshAll=J;break;case 12:h.opts.CalcCount=J;break;case 16:h.opts.CalcDelta=J;break;case 17:h.opts.CalcIter=J;break;case 13:h.opts.CalcMode=J;break;case 14:h.opts.CalcPrecision=J;break;case 95:h.opts.CalcSaveRecalc=J;break;case 15:O.CalcRefMode=J;break;case 2211:h.opts.FullCalc=J;break;case 129:J.fDialog&&(p["!type"]="dialog"),J.fBelow||((p["!outline"]||(p["!outline"]={})).above=!0),J.fRight||((p["!outline"]||(p["!outline"]={})).left=!0);break;case 224:A.push(J);break;case 430:L.push([J]),L[L.length-1].XTI=[];break;case 35:case 547:L[L.length-1].push(J);break;case 24:case 536:M={Name:J.Name,Ref:ri(J.rgce,0,null,L,O)},J.itab>0&&(M.Sheet=J.itab-1),L.names.push(M),L[0]||(L[0]=[],L[0].XTI=[]),L[L.length-1].push(J),"_xlnm._FilterDatabase"==J.Name&&J.itab>0&&J.rgce&&J.rgce[0]&&J.rgce[0][0]&&"PtgArea3d"==J.rgce[0][0][0]&&(z[J.itab-1]={ref:Ir(J.rgce[0][0][1][2])});break;case 22:O.ExternCount=J;break;case 23:0==L.length&&(L[0]=[],L[0].XTI=[]),L[L.length-1].XTI=L[L.length-1].XTI.concat(J),L.XTI=L.XTI.concat(J);break;case 2196:if(O.biff<8)break;null!=M&&(M.Comment=J[1]);break;case 18:p["!protect"]=J;break;case 19:0!==J&&O.WTF&&n("error","at node_modules/xlsx/xlsx.mjs:18748","Password verifier: "+J);break;case 133:m[J.pos]=J,O.snames.push(J.name);break;case 10:if(--U)break;if(g.e){if(g.e.r>0&&g.e.c>0){if(g.e.r--,g.e.c--,p["!ref"]=Ir(g),t.sheetRows&&t.sheetRows<=g.e.r){var Z=g.e.r;g.e.r=t.sheetRows-1,p["!fullref"]=p["!ref"],p["!ref"]=Ir(g),g.e.r=Z}g.e.r++,g.e.c++}R.length>0&&(p["!merges"]=R),I.length>0&&(p["!objects"]=I),D.length>0&&(p["!cols"]=D),F.length>0&&(p["!rows"]=F),x.Sheets.push(C)}""===T?E=p:d[T]=p,p=t.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===O.biff&&(O.biff={9:2,521:3,1033:4}[j]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[J.BIFFVer]||8),O.biffguess=0==J.BIFFVer,0==J.BIFFVer&&4096==J.dt&&(O.biff=5,G=!0,u(O.codepage=28591)),8==O.biff&&0==J.BIFFVer&&16==J.dt&&(O.biff=2),U++)break;if(p=t.dense?[]:{},O.biff<8&&!G&&(G=!0,u(O.codepage=t.codepage||1252)),O.biff<5||0==J.BIFFVer&&4096==J.dt){""===T&&(T="Sheet1"),g={s:{r:0,c:0},e:{r:0,c:0}};var Q={pos:e.l-X,name:T};m[Q.pos]=Q,O.snames.push(T)}else T=(m[$]||{name:""}).name;32==J.dt&&(p["!type"]="chart"),64==J.dt&&(p["!type"]="macro"),R=[],I=[],O.arrayf=k=[],D=[],F=[],P=!1,C={Hidden:(m[$]||{hs:0}).hs,name:T};break;case 515:case 3:case 2:"chart"==p["!type"]&&(t.dense?(p[J.r]||[])[J.c]:p[Or({c:J.c,r:J.r})])&&++J.c,o={ixfe:J.ixfe,XF:A[J.ixfe]||{},v:J.val,t:"n"},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t);break;case 5:case 517:o={ixfe:J.ixfe,XF:A[J.ixfe],v:J.val,t:J.t},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t);break;case 638:o={ixfe:J.ixfe,XF:A[J.ixfe],v:J.rknum,t:"n"},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t);break;case 189:for(var ee=J.c;ee<=J.C;++ee){var te=J.rkrec[ee-J.c][0];o={ixfe:te,XF:A[te],v:J.rkrec[ee-J.c][1],t:"n"},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:ee,r:J.r},o,t)}break;case 6:case 518:case 1030:if("String"==J.val){v=J;break}if((o=Ec(J.val,J.cell.ixfe,J.tt)).XF=A[o.ixfe],t.cellFormula){var re=J.formula;if(re&&re[0]&&re[0][0]&&"PtgExp"==re[0][0][0]){var ae=re[0][0][1][0],ne=re[0][0][1][1],se=Or({r:ae,c:ne});S[se]?o.f=""+ri(J.formula,0,J.cell,L,O):o.F=((t.dense?(p[ae]||[])[ne]:p[se])||{}).F}else o.f=""+ri(J.formula,0,J.cell,L,O)}V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N(J.cell,o,t),v=J;break;case 7:case 519:if(!v)throw new Error("String record expects Formula");v.val=J,(o=Ec(J,v.cell.ixfe,"s")).XF=A[o.ixfe],t.cellFormula&&(o.f=""+ri(v.formula,0,v.cell,L,O)),V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N(v.cell,o,t),v=null;break;case 33:case 545:k.push(J);var ie=Or(J[0].s);if(a=t.dense?(p[J[0].s.r]||[])[J[0].s.c]:p[ie],t.cellFormula&&a){if(!v)break;if(!ie||!a)break;a.f=""+ri(J[1],0,J[0],L,O),a.F=Ir(J[0])}break;case 1212:if(!t.cellFormula)break;if(w){if(!v)break;S[Or(v.cell)]=J[0],((a=t.dense?(p[v.cell.r]||[])[v.cell.c]:p[Or(v.cell)])||{}).f=""+ri(J[0],0,r,L,O)}break;case 253:o=Ec(b[J.isst].t,J.ixfe,"s"),b[J.isst].h&&(o.h=b[J.isst].h),o.XF=A[o.ixfe],V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t);break;case 513:t.sheetStubs&&(o={ixfe:J.ixfe,XF:A[J.ixfe],t:"z"},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t));break;case 190:if(t.sheetStubs)for(var ce=J.c;ce<=J.C;++ce){var oe=J.ixfe[ce-J.c];o={ixfe:oe,XF:A[oe],t:"z"},V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:ce,r:J.r},o,t)}break;case 214:case 516:case 4:(o=Ec(J.val,J.ixfe,"s")).XF=A[o.ixfe],V>0&&(o.z=H[o.ixfe>>8&63]),Tc(o,t,h.opts.Date1904),N({c:J.c,r:J.r},o,t);break;case 0:case 512:1===U&&(g=J);break;case 252:b=J;break;case 1054:if(4==O.biff){H[V++]=J[1];for(var le=0;le<V+163&&W[le]!=J[1];++le);le>=163&&Se(J[1],V+163)}else Se(J[1],J[0]);break;case 30:H[V++]=J;for(var fe=0;fe<V+163&&W[fe]!=J;++fe);fe>=163&&Se(J,V+163);break;case 229:R=R.concat(J);break;case 93:I[J.cmo[0]]=O.lastobj=J;break;case 438:O.lastobj.TxO=J;break;case 127:O.lastobj.ImData=J;break;case 440:for(c=J[0].s.r;c<=J[0].e.r;++c)for(i=J[0].s.c;i<=J[0].e.c;++i)(a=t.dense?(p[c]||[])[i]:p[Or({c:i,r:c})])&&(a.l=J[1]);break;case 2048:for(c=J[0].s.r;c<=J[0].e.r;++c)for(i=J[0].s.c;i<=J[0].e.c;++i)(a=t.dense?(p[c]||[])[i]:p[Or({c:i,r:c})])&&a.l&&(a.l.Tooltip=J[1]);break;case 28:if(O.biff<=5&&O.biff>=2)break;a=t.dense?(p[J[0].r]||[])[J[0].c]:p[Or(J[0])];var he=I[J[2]];a||(t.dense?(p[J[0].r]||(p[J[0].r]=[]),a=p[J[0].r][J[0].c]={t:"z"}):a=p[Or(J[0])]={t:"z"},g.e.r=Math.max(g.e.r,J[0].r),g.s.r=Math.min(g.s.r,J[0].r),g.e.c=Math.max(g.e.c,J[0].c),g.s.c=Math.min(g.s.c,J[0].c)),a.c||(a.c=[]),s={a:J[1],t:he.TxO.t},a.c.push(s);break;case 2173:A[J.ixfe],J.ext.forEach((function(e){e[0]}));break;case 125:if(!O.cellStyles)break;for(;J.e>=J.s;)D[J.e--]={width:J.w/256,level:J.level||0,hidden:!!(1&J.flags)},P||(P=!0,rs(J.w/256)),as(D[J.e+1]);break;case 520:var ue={};null!=J.level&&(F[J.r]=ue,ue.level=J.level),J.hidden&&(F[J.r]=ue,ue.hidden=!0),J.hpt&&(F[J.r]=ue,ue.hpt=J.hpt,ue.hpx=ss(J.hpt));break;case 38:case 39:case 40:case 41:p["!margins"]||bi(p["!margins"]={}),p["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[j]]=J;break;case 161:p["!margins"]||bi(p["!margins"]={}),p["!margins"].header=J.header,p["!margins"].footer=J.footer;break;case 574:J.RTL&&(x.Views[0].RTL=!0);break;case 146:y=J;break;case 2198:f=J;break;case 140:l=J;break;case 442:T?C.CodeName=J||C.name:x.WBProps.CodeName=J||"ThisWorkbook"}}else Y||n("error","at node_modules/xlsx/xlsx.mjs:19034","Missing Info for XLS Record 0x"+j.toString(16)),e.l+=X}return h.SheetNames=_e(m).sort((function(e,t){return Number(e)-Number(t)})).map((function(e){return m[e].name})),t.bookSheets||(h.Sheets=d),!h.SheetNames.length&&E["!ref"]?(h.SheetNames.push("Sheet1"),h.Sheets&&(h.Sheets.Sheet1=E)):h.Preamble=E,h.Sheets&&z.forEach((function(e,t){h.Sheets[h.SheetNames[t]]["!autofilter"]=e})),h.Strings=b,h.SSF=We(W),O.enc&&(h.Encryption=O.enc),f&&(h.Themes=f),h.Metadata={},void 0!==l&&(h.Metadata.Country=l),L.names.length>0&&(x.Names=L.names),h.Workbook=x,h}var Sc="e0859ff2f94f6810ab9108002b27b3d9",kc="02d5cdd59c2e1b10939708002b2cf9ae";function Ac(e,t){var r,a,n,s;if(t||(t={}),eo(t),d(),t.codepage&&h(t.codepage),e.FullPaths){if(Ce.find(e,"/encryption"))throw new Error("File is password-protected");r=Ce.find(e,"!CompObj"),a=Ce.find(e,"/Workbook")||Ce.find(e,"/Book")}else{switch(t.type){case"base64":e=C(S(e));break;case"binary":e=C(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}gr(e,0),a={content:e}}if(r&&gc(r),t.bookProps&&!t.bookSheets)n={};else{var i=k?"buffer":"array";if(a&&a.content)n=wc(a.content,t);else if((s=Ce.find(e,"PerfectOffice_MAIN"))&&s.content)n=yn.to_workbook(s.content,(t.type=i,t));else{if(!(s=Ce.find(e,"NativeContent_MAIN"))||!s.content)throw(s=Ce.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");n=yn.to_workbook(s.content,(t.type=i,t))}t.bookVBA&&e.FullPaths&&Ce.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=function(e){var t=Ce.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(r,a){if("/"!==r.slice(-1)&&r.match(/_VBA_PROJECT_CUR/)){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Ce.utils.cfb_add(t,n,e.FileIndex[a].content)}})),Ce.write(t)}(e))}var c={};return e.FullPaths&&function(e,t,r){var a=Ce.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Da(a,ra,kc);for(var s in n)t[s]=n[s]}catch(l){if(r.WTF)throw l}var i=Ce.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=Da(i,aa,Sc);for(var o in c)null==t[o]&&(t[o]=c[o])}catch(l){if(r.WTF)throw l}t.HeadingPairs&&t.TitlesOfParts&&(ba(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,c,t),n.Props=n.Custprops=c,t.bookFiles&&(n.cfb=e),n}var yc={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[zr(e)]}},2:{f:function(e){return[zr(e),Kr(e),"n"]}},3:{f:function(e){return[zr(e),e.read_shift(1),"e"]}},4:{f:function(e){return[zr(e),e.read_shift(1),"b"]}},5:{f:function(e){return[zr(e),Zr(e),"n"]}},6:{f:function(e){return[zr(e),Ur(e),"str"]}},7:{f:function(e){return[zr(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=zr(e);n.r=r["!row"];var s=[n,Ur(e),"str"];if(r.cellFormula){e.l+=2;var i=oi(e,a-e.l,r);s[3]=ri(i,0,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=zr(e);n.r=r["!row"];var s=[n,Zr(e),"n"];if(r.cellFormula){e.l+=2;var i=oi(e,a-e.l,r);s[3]=ri(i,0,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=zr(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=oi(e,a-e.l,r);s[3]=ri(i,0,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=zr(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=oi(e,a-e.l,r);s[3]=ri(i,0,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[Gr(e)]}},13:{f:function(e){return[Gr(e),Kr(e),"n"]}},14:{f:function(e){return[Gr(e),e.read_shift(1),"e"]}},15:{f:function(e){return[Gr(e),e.read_shift(1),"b"]}},16:{f:Pi},17:{f:function(e){return[Gr(e),Ur(e),"str"]}},18:{f:function(e){return[Gr(e),e.read_shift(4),"s"]}},19:{f:Hr},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=Xr(e),i=li(e,0,r),c=jr(e);e.l=a;var o={Name:s,Ptg:i};return n<268435455&&(o.Sheet=n),c&&(o.Comment=c),o}},40:{},42:{},43:{f:function(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var i=e.read_shift(1);i>0&&(a.family=i);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=function(e){var t={},r=e.read_shift(1)>>>1,a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=a;var o=ca[a];o&&(t.rgb=Kn(o));break;case 2:t.rgb=Kn([s,i,c]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=Ur(e),a}},44:{f:function(e,t){return[e.read_shift(2),Ur(e)]}},45:{f:fs},46:{f:hs},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:mn},62:{f:function(e){return[zr(e),Hr(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Or(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:vr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=$r(e),r}},148:{f:Fi,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?Ur(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Yr(e),r.name=Ur(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:qr},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Li},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ur(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Yr},357:{},358:{},359:{},360:{T:1},361:{},362:{f:un},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=Jr(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var c=ci(e,a-e.l,r);i[1]=c}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[qr(e)];if(r.cellFormula){var s=fi(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Mi.forEach((function(r){t[r]=Zr(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=qr(e),n=jr(e),s=Ur(e),i=Ur(e),c=Ur(e);e.l=r;var o={rfx:a,relId:n,loc:s,display:c};return i&&(o.Tooltip=i),o}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Yr},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:As},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=qr(e);return t.rfx=r.s,t.ref=Or(r.s),e.l+=16,t}},636:{T:-1},637:{f:Wr},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:Ur(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},xc={6:{f:si},10:{f:Fa},12:{f:La},13:{f:La},14:{f:Pa},15:{f:Pa},16:{f:Zr},17:{f:Pa},18:{f:Pa},19:{f:La},20:{f:on},21:{f:on},23:{f:un},24:{f:hn},25:{f:Pa},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=Wa(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},c,i,s]}}(e,0,r)}},29:{},34:{f:Pa},35:{f:ln},38:{f:Zr},39:{f:Zr},40:{f:Zr},41:{f:Zr},42:{f:Pa},43:{f:Pa},47:{f:function(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?Xn(e,t-2,a):jn(e,r.biff,r,a),a}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=Ba(e,0,r),a}},51:{f:La},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:Pa},65:{f:function(){}},66:{f:La},77:{},80:{},81:{},82:{},85:{f:La},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=Wa(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8)return function(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((pn[a]||vr)(e,t,r)),{cmo:[n,a,s],ft:i}}(e,t,r);var a=Qa(e),n=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(tn[n](e,r-e.l))}catch(s){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,a[1]);return{cmo:a,ft:n}}},94:{},95:{f:Pa},96:{},97:{},99:{f:Pa},125:{f:mn},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:La},131:{f:Pa},132:{f:Pa},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=Ba(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=na[t]||t,t=e.read_shift(2),r[1]=na[t]||t,r}},141:{f:La},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ja(e));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:La},157:{},158:{},160:{f:vn},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=Zr(e),r.footer=Zr(e),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(Ka(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:Fa},197:{},198:{},199:{},200:{},201:{},202:{f:Pa},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:La},220:{},221:{f:Pa},222:{},224:{f:function(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,6,a.data=function(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),o=e.read_shift(2);return n.patternType=sa[c>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&c,n.icvBottom=c>>7&127,n.icvDiag=c>>14&127,n.dgDiag=c>>21&15,n.icvFore=127&o,n.icvBack=o>>7&127,n.fsxButton=o>>14&1,n):n}(e,0,a.fStyle,r),a}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:Fa},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(Ja(e));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(Ua(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=Xa(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Ma},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Pa},353:{f:Fa},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Va(e,s),c=[];a>e.l;)c.push(Ha(e));return[s,n,i,c]}},431:{f:Pa},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:function(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}(e);var i=e.read_shift(2);e.read_shift(2),La(e);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw new Error("TxO: bad continue record");var l=e[e.l];if((n+=Va(e,e.lens[o+1]-e.lens[o]-1)).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+t,{t:n}}catch(f){return e.l=a+t,{t:n}}}},439:{f:Pa},440:{f:function(e,t){var r=Ja(e);e.l+=16;var a=function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,c,o,l,f,h="";16&n&&(s=Ga(e,e.l)),128&n&&(i=Ga(e,e.l)),257==(257&n)&&(c=Ga(e,e.l)),1==(257&n)&&(o=za(e,e.l)),8&n&&(h=Ga(e,e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=ka(e)),e.l=r;var u=i||c||o||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24);return[r,a]}},441:{},442:{f:Ha},443:{},444:{f:La},445:{},446:{},448:{f:Fa},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:Fa},512:{f:sn},513:{f:gn},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=Xa(e),n=Zr(e);return a.val=n,a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5),e.l;var a=Xa(e);2==r.biff&&e.l++;var n=Ha(e,e.l,r);return a.val=n,a}},517:{f:cn},519:{f:bn},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:dn},549:{f:an},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=Ka(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),Wa(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=qa(e);e.l++;var n=e.read_shift(1);return[ni(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=Ja(e),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(R,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:rn},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Fa},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){e.l,e.l+=2;var r=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),n=[];a-- >0;)n.push(Ss(e,e.l));return{ixfe:r,ext:n}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Pa,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2);return[Va(e,a,r),Va(e,n,r)]}e.l+=t},r:12},2197:{},2198:{f:function(e,t,r){var a=e.l+t;if(124226!==e.read_shift(4))if(r.cellStyles){var n,s=e.slice(e.l);e.l=a;try{n=at(s,{type:"array"})}catch(c){return}var i=Qe(n,"theme/theme/theme1.xml",!0);if(i)return Es(i,r)}else e.l=a},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:Fa},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t=function(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}(e);if(2211!=t.type)throw new Error("Invalid Future Record "+t.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:La},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ja(e));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:sn},1:{},2:{f:function(e){var t=Xa(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=Xa(e);++e.l;var r=Zr(e);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=Xa(e);++e.l;var n=Wa(e,0,r);return a.t="str",a.val=n,a}},5:{f:cn},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:rn},11:{},22:{f:La},30:{f:nn},31:{},32:{},33:{f:dn},36:{},37:{f:an},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:La},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=Xa(e),s=e.read_shift(2),i=Va(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:si},521:{f:rn},536:{f:hn},547:{f:ln},561:{},579:{},1030:{f:si},1033:{f:rn},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function Cc(e,t,r,a){var n=t;if(!isNaN(n)){var s=a||(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&sr(r)&&e.push(r)}}function _c(e,t){var r=t||{},a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,c=s&&s.index||e.length,o=Xe(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<o.length;++i){var m=o[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"!=g){if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(c=0;c<v.length;++c){var b=v[c].trim();if(b.match(/<t[dh]/i)){for(var T=b,E=0;"<"==T.charAt(0)&&(E=T.indexOf(">"))>-1;)T=T.slice(E+1);for(var w=0;w<p.length;++w){var S=p[w];S.s.c==f&&S.s.r<l&&l<=S.e.r&&(f=S.e.c+1,w=-1)}var k=ht(b.slice(0,b.indexOf(">")));u=k.colspan?+k.colspan:1,((h=+k.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var A=k.t||k["data-t"]||"";if(T.length)if(T=_t(T),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),T.length){var y={t:"s",v:T};r.raw||!T.trim().length||"s"==A||("TRUE"===T?y={t:"b",v:!0}:"FALSE"===T?y={t:"b",v:!1}:isNaN(Ge(T))?isNaN(je(T).getDate())||(y={t:"d",v:Ve(T)},r.cellDates||(y={t:"n",v:Re(y.v)}),y.z=r.dateNF||W[14]):y={t:"n",v:Ge(T)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=y):a[Or({r:l,c:f})]=y,f+=u}else f+=u;else f+=u}}}}else{if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0}}return a["!ref"]=Ir(d),p.length&&(a["!merges"]=p),a}function Nc(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var c=0,o=0,l=0;l<n.length;++l)if(!(n[l].s.r>r||n[l].s.c>i||n[l].e.r<r||n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){c=-1;break}c=n[l].e.r-n[l].s.r+1,o=n[l].e.c-n[l].s.c+1;break}if(!(c<0)){var f=Or({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||bt(h.w||(Pr(h),h.w)||""))||"",d={};c>1&&(d.rowspan=c),o>1&&(d.colspan=o),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(Ft("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}function Oc(e,t,r){var a=r||{},n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?Nr(a.origin):a.origin;n=i.r,s=i.c}var c=t.getElementsByTagName("tr"),o=Math.min(a.sheetRows||1e7,c.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=Rr(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,T=0;for(e["!cols"]||(e["!cols"]=[]);p<c.length&&m<o;++p){var E=c[p];if(Ic(E)){if(a.display)continue;d[m]={hidden:!0}}var w=E.children;for(g=v=0;g<w.length;++g){var S=w[g];if(!a.display||!Ic(S)){var k=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):_t(S.innerHTML),A=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var y=h[u];y.s.c==v+s&&y.s.r<m+n&&m+n<=y.e.r&&(v=y.e.c+1-s,u=-1)}T=+S.getAttribute("colspan")||1,((b=+S.getAttribute("rowspan")||1)>1||T>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(b||1)-1,c:v+s+(T||1)-1}});var x={t:"s",v:k},C=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=k&&(0==k.length?x.t=C||"z":a.raw||0==k.trim().length||"s"==C||("TRUE"===k?x={t:"b",v:!0}:"FALSE"===k?x={t:"b",v:!1}:isNaN(Ge(k))?isNaN(je(k).getDate())||(x={t:"d",v:Ve(k)},a.cellDates||(x={t:"n",v:Re(x.v)}),x.z=a.dateNF||W[14]):x={t:"n",v:Ge(k)})),void 0===x.z&&null!=A&&(x.z=A);var _="",N=S.getElementsByTagName("A");if(N&&N.length)for(var O=0;O<N.length&&(!N[O].hasAttribute("href")||"#"==(_=N[O].getAttribute("href")).charAt(0));++O);_&&"#"!=_.charAt(0)&&(x.l={Target:_}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=x):e[Or({c:v+s,r:m+n})]=x,l.e.c<v+s&&(l.e.c=v+s),v+=T}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=Ir(l),m>=o&&(e["!fullref"]=Ir((l.e.r=c.length-p+m-1+n,l))),e}function Rc(e,t){return Oc((t||{}).dense?[]:{},e,t)}function Ic(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}var Dc={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Fc(e,t){var r,a,n,s,i,c,o,l,f=t||{},h=Pt(e),u=[],d={name:""},p="",m=0,g={},v=[],b=f.dense?[]:{},T={value:""},E="",w=0,S=[],k=-1,A=-1,y={s:{r:1e6,c:1e7},e:{r:0,c:0}},x=0,C={},_=[],N={},O=[],R=1,I=1,D=[],F={Names:[]},P={},L=["",""],M=[],B={},U="",V=0,H=!1,W=!1,z=0;for(Lt.lastIndex=0,h=h.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");i=Lt.exec(h);)switch(i[3]=i[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===i[1]?(y.e.c>=y.s.c&&y.e.r>=y.s.r?b["!ref"]=Ir(y):b["!ref"]="A1:A1",f.sheetRows>0&&f.sheetRows<=y.e.r&&(b["!fullref"]=b["!ref"],y.e.r=f.sheetRows-1,b["!ref"]=Ir(y)),_.length&&(b["!merges"]=_),O.length&&(b["!rows"]=O),n.name=n["名称"]||n.name,"undefined"!=typeof JSON&&JSON.stringify(n),v.push(n.name),g[n.name]=b,W=!1):"/"!==i[0].charAt(i[0].length-2)&&(n=ht(i[0],!1),k=A=-1,y.s.r=y.s.c=1e7,y.e.r=y.e.c=0,b=f.dense?[]:{},_=[],O=[],W=!0);break;case"table-row-group":"/"===i[1]?--x:++x;break;case"table-row":case"行":if("/"===i[1]){k+=R,R=1;break}if((s=ht(i[0],!1))["行号"]?k=s["行号"]-1:-1==k&&(k=0),(R=+s["number-rows-repeated"]||1)<10)for(z=0;z<R;++z)x>0&&(O[k+z]={level:x});A=-1;break;case"covered-table-cell":"/"!==i[1]&&++A,f.sheetStubs&&(f.dense?(b[k]||(b[k]=[]),b[k][A]={t:"z"}):b[Or({r:k,c:A})]={t:"z"}),E="",S=[];break;case"table-cell":case"数据":if("/"===i[0].charAt(i[0].length-2))++A,T=ht(i[0],!1),I=parseInt(T["number-columns-repeated"]||"1",10),c={t:"z",v:null},T.formula&&0!=f.cellFormula&&(c.f=pi(mt(T.formula))),"string"==(T["数据类型"]||T["value-type"])&&(c.t="s",c.v=mt(T["string-value"]||""),f.dense?(b[k]||(b[k]=[]),b[k][A]=c):b[Or({r:k,c:A})]=c),A+=I-1;else if("/"!==i[1]){E="",w=0,S=[],I=1;var G=R?k+R-1:k;if(++A>y.e.c&&(y.e.c=A),A<y.s.c&&(y.s.c=A),k<y.s.r&&(y.s.r=k),G>y.e.r&&(y.e.r=G),M=[],B={},c={t:(T=ht(i[0],!1))["数据类型"]||T["value-type"],v:null},f.cellFormula)if(T.formula&&(T.formula=mt(T.formula)),T["number-matrix-columns-spanned"]&&T["number-matrix-rows-spanned"]&&(N={s:{r:k,c:A},e:{r:k+(parseInt(T["number-matrix-rows-spanned"],10)||0)-1,c:A+(parseInt(T["number-matrix-columns-spanned"],10)||0)-1}},c.F=Ir(N),D.push([N,c.F])),T.formula)c.f=pi(T.formula);else for(z=0;z<D.length;++z)k>=D[z][0].s.r&&k<=D[z][0].e.r&&A>=D[z][0].s.c&&A<=D[z][0].e.c&&(c.F=D[z][1]);switch((T["number-columns-spanned"]||T["number-rows-spanned"])&&(N={s:{r:k,c:A},e:{r:k+(parseInt(T["number-rows-spanned"],10)||0)-1,c:A+(parseInt(T["number-columns-spanned"],10)||0)-1}},_.push(N)),T["number-columns-repeated"]&&(I=parseInt(T["number-columns-repeated"],10)),c.t){case"boolean":c.t="b",c.v=Et(T["boolean-value"]);break;case"float":case"percentage":case"currency":c.t="n",c.v=parseFloat(T.value);break;case"date":c.t="d",c.v=Ve(T["date-value"]),f.cellDates||(c.t="n",c.v=Re(c.v)),c.z="m/d/yy";break;case"time":c.t="n",c.v=Le(T["time-value"])/86400,f.cellDates&&(c.t="d",c.v=Pe(c.v)),c.z="HH:MM:SS";break;case"number":c.t="n",c.v=parseFloat(T["数据数值"]);break;default:if("string"!==c.t&&"text"!==c.t&&c.t)throw new Error("Unsupported value type "+c.t);c.t="s",null!=T["string-value"]&&(E=mt(T["string-value"]),S=[])}}else{if(H=!1,"s"===c.t&&(c.v=E||"",S.length&&(c.R=S),H=0==w),P.Target&&(c.l=P),M.length>0&&(c.c=M,M=[]),E&&!1!==f.cellText&&(c.w=E),H&&(c.t="z",delete c.v),(!H||f.sheetStubs)&&!(f.sheetRows&&f.sheetRows<=k))for(var $=0;$<R;++$){if(I=parseInt(T["number-columns-repeated"]||"1",10),f.dense)for(b[k+$]||(b[k+$]=[]),b[k+$][A]=0==$?c:We(c);--I>0;)b[k+$][A+I]=We(c);else for(b[Or({r:k+$,c:A})]=c;--I>0;)b[Or({r:k+$,c:A+I})]=We(c);y.e.c<=A&&(y.e.c=A)}A+=(I=parseInt(T["number-columns-repeated"]||"1",10))-1,I=0,c={},E="",S=[]}P={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!0]);break;case"annotation":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r;B.t=E,S.length&&(B.R=S),B.a=U,M.push(B)}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);U="",V=0,E="",w=0,S=[];break;case"creator":"/"===i[1]?U=h.slice(V,i.index):V=i.index+i[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===i[1]){if((r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&u.push([i[3],!1]);E="",w=0,S=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===i[1]){if(C[d.name]=p,(r=u.pop())[0]!==i[3])throw"Bad state: "+r}else"/"!==i[0].charAt(i[0].length-2)&&(p="",d=ht(i[0],!1),u.push([i[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(u[u.length-1][0]){case"time-style":case"date-style":a=ht(i[0],!1),p+=Dc[i[3]]["long"===a.style?1:0]}break;case"text":if("/>"===i[0].slice(-2))break;if("/"===i[1])switch(u[u.length-1][0]){case"number-style":case"date-style":case"time-style":p+=h.slice(m,i.index)}else m=i.index+i[0].length;break;case"named-range":L=mi((a=ht(i[0],!1))["cell-range-address"]);var j={Name:a.name,Ref:L[0]+"!"+L[1]};W&&(j.Sheet=v.length),F.Names.push(j);break;case"p":case"文本串":if(["master-styles"].indexOf(u[u.length-1][0])>-1)break;if("/"!==i[1]||T&&T["string-value"])ht(i[0],!1),w=i.index+i[0].length;else{var X=(o=h.slice(w,i.index),l=void 0,l=o.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,t){return Array(parseInt(t,10)+1).join(" ")})).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n"),[mt(l.replace(/<[^>]*>/g,""))]);E=(E.length>0?E+"\n":"")+X[0]}break;case"database-range":if("/"===i[1])break;try{g[(L=mi(ht(i[0])["target-range-address"]))[0]]["!autofilter"]={ref:L[1]}}catch(K){}break;case"a":if("/"!==i[1]){if(!(P=ht(i[0],!1)).href)break;P.Target=mt(P.href),delete P.href,"#"==P.Target.charAt(0)&&P.Target.indexOf(".")>-1?(L=mi(P.Target.slice(1)),P.Target="#"+L[0]+"!"+L[1]):P.Target.match(/^\.\.[\\\/]/)&&(P.Target=P.Target.slice(3))}break;default:switch(i[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(f.WTF)throw new Error(i)}}var Y={Sheets:g,SheetNames:v,Workbook:F};return f.bookSheets&&delete Y.Sheets,Y}function Pc(e,t){t=t||{},Je(e,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=Pt(e);r=Lt.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=ht(r[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(Ze(e,"META-INF/manifest.xml"),t);var r=Qe(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=Fc(yt(r),t);return Je(e,"meta.xml")&&(a.Props=ga(Ze(e,"meta.xml"))),a}function Lc(e,t){return Fc(e,t)}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Mc(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Bc(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):yt(_(e))}function Uc(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),a=0;return e.forEach((function(e){r.set(e,a),a+=e.length})),r}function Vc(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>>24}function Hc(e,t){var r=t?t[0]:0,a=127&e[r];e:if(e[r++]>=128){if(a|=(127&e[r])<<7,e[r++]<128)break e;if(a|=(127&e[r])<<14,e[r++]<128)break e;if(a|=(127&e[r])<<21,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),a}function Wc(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function zc(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=Hc(e,r),i=7&s,c=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var o=r[0];e[r[0]++]>=128;);a=e.slice(o,r[0]);break;case 5:c=4,a=e.slice(r[0],r[0]+c),r[0]+=c;break;case 1:c=8,a=e.slice(r[0],r[0]+c),r[0]+=c;break;case 2:c=Hc(e,r),a=e.slice(r[0],r[0]+c),r[0]+=c;break;default:throw new Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]?t[s]=[l]:t[s].push(l)}return t}function Gc(e,t){return(null==e?void 0:e.map((function(e){return t(e.data)})))||[]}function $c(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=Hc(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0!=s){var i=0,c=0;if(1==s?(c=4+(t[r[0]]>>2&7),i=(224&t[r[0]++])<<3,i|=t[r[0]++]):(c=1+(t[r[0]++]>>2),2==s?(i=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(i=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[Uc(n)],0==i)throw new Error("Invalid offset 0");if(i>n[0].length)throw new Error("Invalid offset beyond length");if(c>=i)for(n.push(n[0].slice(-i)),c-=i;c>=n[n.length-1].length;)n.push(n[n.length-1]),c-=n[n.length-1].length;n.push(n[0].slice(-i,-i+c))}else{var o=t[r[0]++]>>2;if(o<60)++o;else{var l=o-59;o=t[r[0]],l>1&&(o|=t[r[0]+1]<<8),l>2&&(o|=t[r[0]+2]<<16),l>3&&(o|=t[r[0]+3]<<24),o>>>=0,o++,r[0]+=l}n.push(t.slice(r[0],r[0]+o)),r[0]+=o}}var f=Uc(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function jc(e,t,r){var a,n=Mc(e),s=n.getUint32(8,!0),i=12,c=-1,o=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(o=n.getUint32(i,!0),i+=4),16&s&&(c=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:t[o]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(!(c>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));a={t:"s",v:r[c]};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}function Xc(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,t,r,a){var n,s=Mc(e),i=s.getUint32(4,!0),c=(a>1?12:8)+4*Vc(i&(a>1?3470:398)),o=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(o=s.getUint32(c,!0),c+=4),c+=4*Vc(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(c,!0),c+=4),32&i&&(f=s.getFloat64(c,!0),c+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(c,!0)),c+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:t[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(o>-1)n={t:"s",v:r[o]};else if(l>-1)n={t:"s",v:t[l]};else{if(isNaN(f))throw new Error("Unsupported cell type ".concat(e.slice(0,4)));n={t:"n",v:f}}break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,t,r,e[0]);case 5:return jc(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Yc(e){return Hc(zc(e)[1][0].data)}function Kc(e,t){var r=zc(t.data),a=Wc(r[1][0].data),n=r[3],s=[];return(n||[]).forEach((function(t){var r=zc(t.data),n=Wc(r[1][0].data)>>>0;switch(a){case 1:s[n]=Bc(r[3][0].data);break;case 8:var i=zc(e[Yc(r[9][0].data)][0].data),c=e[Yc(i[1][0].data)][0],o=Wc(c.meta[1][0].data);if(2001!=o)throw new Error("2000 unexpected reference to ".concat(o));var l=zc(c.data);s[n]=l[3].map((function(e){return Bc(e.data)})).join("")}})),s}function Jc(e,t){var r,a=zc(t.data),n=(null==(r=null==a?void 0:a[7])?void 0:r[0])?Wc(a[7][0].data)>>>0>0?1:0:-1,s=Gc(a[5],(function(e){return function(e,t){var r,a,n,s,i,c,o,l,f,h,u,d,p,m,g,v,b=zc(e),T=Wc(b[1][0].data)>>>0,E=Wc(b[2][0].data)>>>0,w=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&Wc(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)g=null==(c=null==(i=b[7])?void 0:i[0])?void 0:c.data,v=null==(l=null==(o=b[6])?void 0:o[0])?void 0:l.data;else{if(!(null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)||1==t)throw"NUMBERS Tile missing ".concat(t," cell storage");g=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data}for(var S=w?4:1,k=Mc(g),A=[],y=0;y<g.length/2;++y){var x=k.getUint16(2*y,!0);x<65535&&A.push([y,x])}if(A.length!=E)throw"Expected ".concat(E," cells, found ").concat(A.length);var C=[];for(y=0;y<A.length-1;++y)C[A[y][0]]=v.subarray(A[y][1]*S,A[y+1][1]*S);return A.length>=1&&(C[A[A.length-1][0]]=v.subarray(A[A.length-1][1]*S)),{R:T,cells:C}}(e,n)}));return{nrows:Wc(a[4][0].data)>>>0,data:s.reduce((function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach((function(r,a){if(e[t.R][a])throw new Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r})),e}),[])}}function qc(e,t){var r={"!ref":"A1"},a=e[Yc(zc(t.data)[2][0].data)],n=Wc(a[0].meta[1][0].data);if(6001!=n)throw new Error("6000 unexpected reference to ".concat(n));return function(e,t,r){var a,n=zc(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Wc(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(Wc(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=Ir(s);var i=zc(n[4][0].data),c=Kc(e,e[Yc(i[4][0].data)][0]),o=(null==(a=i[17])?void 0:a[0])?Kc(e,e[Yc(i[17][0].data)][0]):[],l=zc(i[3][0].data),f=0;l[1].forEach((function(t){var a=zc(t.data),n=e[Yc(a[2][0].data)][0],s=Wc(n.meta[1][0].data);if(6002!=s)throw new Error("6001 unexpected reference to ".concat(s));var i=Jc(0,n);i.data.forEach((function(e,t){e.forEach((function(e,a){var n=Or({r:f+t,c:a}),s=Xc(e,c,o);s&&(r[n]=s)}))})),f+=i.nrows}))}(e,a[0],r),r}function Zc(e,t){var r={SheetNames:[],Sheets:{}};if(Gc(zc(t.data)[1],Yc).forEach((function(t){e[t].forEach((function(t){if(2==Wc(t.meta[1][0].data)){var a=function(e,t){var r,a=zc(t.data),n={name:(null==(r=a[1])?void 0:r[0])?Bc(a[1][0].data):"",sheets:[]};return Gc(a[2],Yc).forEach((function(t){e[t].forEach((function(t){6e3==Wc(t.meta[1][0].data)&&n.sheets.push(qc(e,t))}))})),n}(e,t);a.sheets.forEach((function(e,t){bo(r,e,0==t?a.name:a.name+"_"+t,!0)}))}}))})),0==r.SheetNames.length)throw new Error("Empty NUMBERS file");return r}function Qc(e){var t,r,a,s,i={},c=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)){var t,r;try{t=function(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push($c(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Uc(t)}(e.content)}catch(a){return n("log","at node_modules/xlsx/xlsx.mjs:22746","?? "+e.content.length+" "+(a.message||a))}try{r=function(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=Hc(e,a),s=zc(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:Wc(s[1][0].data),messages:[]};s[2].forEach((function(t){var r=zc(t.data),n=Wc(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n})),(null==(t=s[3])?void 0:t[0])&&(i.merge=Wc(s[3][0].data)>>>0>0),r.push(i)}return r}(t)}catch(a){return n("log","at node_modules/xlsx/xlsx.mjs:22752","## "+(a.message||a))}r.forEach((function(e){i[e.id]=e.messages,c.push(e.id)}))}})),!c.length)throw new Error("File has no messages");var o=(null==(s=null==(a=null==(r=null==(t=null==i?void 0:i[1])?void 0:t[0])?void 0:r.meta)?void 0:a[1])?void 0:s[0].data)&&1==Wc(i[1][0].meta[1][0].data)&&i[1][0];if(o||c.forEach((function(e){i[e].forEach((function(e){if(1==Wc(e.meta[1][0].data)>>>0){if(o)throw new Error("Document has multiple roots");o=e}}))})),!o)throw new Error("Cannot find Document root");return Zc(i,o)}function eo(e){var t;(t=[["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]],function(e){for(var r=0;r!=t.length;++r){var a=t[r];void 0===e[a[0]]&&(e[a[0]]=a[1]),"n"===a[2]&&(e[a[0]]=Number(e[a[0]]))}})(e)}function to(e,t,r,a,n,s,i,c,o,l,f,h){try{s[a]=da(Qe(e,r,!0),t);var u,d=Ze(e,t);switch(c){case"sheet":u=Ji(d,t,n,o,s[a],l,f,h);break;case"chart":if(!(u=qi(d,t,n,o,s[a],l))||!u["!drawel"])break;var p=nt(u["!drawel"].Target,t),m=ua(p),g=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}(Qe(e,p,!0),da(Qe(e,m,!0),p)),v=nt(g,p),b=ua(v);u=Bi(Qe(e,v,!0),0,0,da(Qe(e,b,!0),v),0,u);break;case"macro":E=t,s[a],E.slice(-4),u={"!type":"macro"};break;case"dialog":u=function(e,t,r,a,n,s,i,c){return t.slice(-4),{"!type":"dialog"}}(0,t,0,0,s[a]);break;default:throw new Error("Unrecognized sheet type "+c)}i[a]=u;var T=[];s&&s[a]&&_e(s[a]).forEach((function(r){var n="";if(s[a][r].Type==ha.CMNT){n=nt(s[a][r].Target,t);var i=ec(Ze(e,n,!0),n,o);if(!i||!i.length)return;ks(u,i,!1)}s[a][r].Type==ha.TCMNT&&(n=nt(s[a][r].Target,t),T=T.concat(function(e,t){var r=[],a=!1,n={},s=0;return e.replace(ot,(function(i,c){var o=ht(i);switch(ut(o[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":n={author:o.personId,guid:o.id,ref:o.ref,T:1};break;case"</threadedComment>":null!=n.t&&r.push(n);break;case"<text>":case"<text":s=c+i.length;break;case"</text>":n.t=e.slice(s,c).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":a=!0;break;case"</mentions>":case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+o[0]+" in threaded comments")}return i})),r}(Ze(e,n,!0),o)))})),T&&T.length&&ks(u,T,!0,o.people||[])}catch(w){if(o.WTF)throw w}var E}function ro(e){return"/"==e.charAt(0)?e.slice(1):e}function ao(e,t){if(ke(),eo(t=t||{}),Je(e,"META-INF/manifest.xml"))return Pc(e,t);if(Je(e,"objectdata.xml"))return Pc(e,t);if(Je(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return Qc(e);var r=Ce.utils.cfb_new();return tt(e).forEach((function(t){rt(r,t,et(e,t))})),Qc(r)}if(!Je(e,"[Content_Types].xml")){if(Je(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Je(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a,s,i=tt(e),c=function(e){var t={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return t;var r={};if((e.match(ot)||[]).forEach((function(e){var a=ht(e);switch(a[0].replace(lt,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":void 0!==t[fa[a.ContentType]]&&t[fa[a.ContentType]].push(a.PartName)}})),t.xmlns!==Mt)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(Qe(e,"[Content_Types].xml")),o=!1;if(0===c.workbooks.length&&Ze(e,s="xl/workbook.xml",!0)&&c.workbooks.push(s),0===c.workbooks.length){if(!Ze(e,s="xl/workbook.bin",!0))throw new Error("Could not find workbook");c.workbooks.push(s),o=!0}"bin"==c.workbooks[0].slice(-3)&&(o=!0);var l={},f={};if(!t.bookSheets&&!t.bookProps){if(gi=[],c.sst)try{gi=Qi(Ze(e,ro(c.sst)),c.sst,t)}catch(R){if(t.WTF)throw R}t.cellStyles&&c.themes.length&&(l=function(e,t,r){return Es(e,r)}(Qe(e,c.themes[0].replace(/^\//,""),!0)||"",c.themes[0],t)),c.style&&(f=Zi(Ze(e,ro(c.style)),c.style,l,t))}c.links.map((function(r){try{da(Qe(e,ua(ro(r))),r);return rc(Ze(e,ro(r)),0,r,t)}catch(R){}}));var h=Ki(Ze(e,ro(c.workbooks[0])),c.workbooks[0],t),u={},d="";c.coreprops.length&&((d=Ze(e,ro(c.coreprops[0]),!0))&&(u=ga(d)),0!==c.extprops.length&&(d=Ze(e,ro(c.extprops[0]),!0))&&function(e,t,r){var a={};t||(t={}),e=yt(e),va.forEach((function(r){var n=(e.match(Ct(r[0]))||[])[1];switch(r[2]){case"string":n&&(t[r[1]]=mt(n));break;case"bool":t[r[1]]="true"===n;break;case"raw":var s=e.match(new RegExp("<"+r[0]+"[^>]*>([\\s\\S]*?)</"+r[0]+">"));s&&s.length>0&&(a[r[1]]=s[1])}})),a.HeadingPairs&&a.TitlesOfParts&&ba(a.HeadingPairs,a.TitlesOfParts,t,r)}(d,u,t));var p={};t.bookSheets&&!t.bookProps||0!==c.custprops.length&&(d=Qe(e,ro(c.custprops[0]),!0))&&(p=function(e,t){var r={},a="",s=e.match(Ta);if(s)for(var i=0;i!=s.length;++i){var c=s[i],o=ht(c);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":a=mt(o.name);break;case"</property>":a=null;break;default:if(0===c.indexOf("<vt:")){var l=c.split(">"),f=l[0].slice(4),h=l[1];switch(f){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=mt(h);break;case"bool":r[a]=Et(h);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(h,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(h);break;case"filetime":case"date":r[a]=Ve(h);break;default:if("/"==f.slice(-1))break;t.WTF&&"undefined"!=typeof console&&n("warn","at node_modules/xlsx/xlsx.mjs:5669","Unexpected",c,f,l)}}else if("</"===c.slice(0,2));else if(t.WTF)throw new Error(c)}}return r}(d,t));var m={};if((t.bookSheets||t.bookProps)&&(h.Sheets?a=h.Sheets.map((function(e){return e.name})):u.Worksheets&&u.SheetNames.length>0&&(a=u.SheetNames),t.bookProps&&(m.Props=u,m.Custprops=p),t.bookSheets&&void 0!==a&&(m.SheetNames=a),t.bookSheets?m.SheetNames:t.bookProps))return m;a={};var g={};t.bookDeps&&c.calcchain&&(g=tc(Ze(e,ro(c.calcchain)),c.calcchain));var v,b,T=0,E={},w=h.Sheets;u.Worksheets=w.length,u.SheetNames=[];for(var S=0;S!=w.length;++S)u.SheetNames[S]=w[S].name;var k=o?"bin":"xml",A=c.workbooks[0].lastIndexOf("/"),y=(c.workbooks[0].slice(0,A+1)+"_rels/"+c.workbooks[0].slice(A+1)+".rels").replace(/^\//,"");Je(e,y)||(y="xl/_rels/workbook."+k+".rels");var x=da(Qe(e,y,!0),y.replace(/_rels.*/,"s5s"));(c.metadata||[]).length>=1&&(t.xlmeta=ac(Ze(e,ro(c.metadata[0])),c.metadata[0],t)),(c.people||[]).length>=1&&(t.people=function(e,t){var r=[],a=!1;return e.replace(ot,(function(e){var n=ht(e);switch(ut(n[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":r.push({name:n.displayname,id:n.id});break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),r}(Ze(e,ro(c.people[0])),t)),x&&(x=function(e,t){if(!e)return 0;try{e=t.map((function(t){return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,ha.WS.indexOf(r)>-1?"sheet":r==ha.CS?"chart":r==ha.DS?"dialog":r==ha.MS?"macro":r&&r.length?r:"sheet")];var r}))}catch(R){return null}return e&&0!==e.length?e:null}(x,h.Sheets));var C=Ze(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(T=0;T!=u.Worksheets;++T){var _="sheet";if(x&&x[T]?(v="xl/"+x[T][1].replace(/[\/]?xl\//,""),Je(e,v)||(v=x[T][1]),Je(e,v)||(v=y.replace(/_rels\/.*$/,"")+x[T][1]),_=x[T][2]):v=(v="xl/worksheets/sheet"+(T+1-C)+"."+k).replace(/sheet0\./,"sheet."),b=v.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(T!=t.sheets)continue e;break;case"string":if(u.SheetNames[T].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var N=!1,O=0;O!=t.sheets.length;++O)"number"==typeof t.sheets[O]&&t.sheets[O]==T&&(N=1),"string"==typeof t.sheets[O]&&t.sheets[O].toLowerCase()==u.SheetNames[T].toLowerCase()&&(N=1);if(!N)continue e}}to(e,v,b,u.SheetNames[T],T,E,a,_,t,h,l,f)}return m={Directory:c,Workbook:h,Props:u,Custprops:p,Deps:g,Sheets:a,SheetNames:u.SheetNames,Strings:gi,Styles:f,Themes:l,SSF:We(W)},t&&t.bookFiles&&(e.files?(m.keys=i,m.files=e.files):(m.keys=[],m.files={},e.FullPaths.forEach((function(t,r){t=t.replace(/^Root Entry[\/]/,""),m.keys.push(t),m.files[t]=e.FileIndex[r]})))),t&&t.bookVBA&&(c.vba.length>0?m.vbaraw=Ze(e,ro(c.vba[0]),!0):c.defaults&&"application/vnd.ms-office.vbaProject"===c.defaults.bin&&(m.vbaraw=Ze(e,"xl/vbaProject.bin",!0))),m}function no(e,t){var r,a,n=t||{},s="Workbook",i=Ce.find(e,s);try{if(s="/!DataSpaces/Version",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(r=i.content,(a={}).id=r.read_shift(0,"lpp4"),a.R=Ln(r,4),a.U=Ln(r,4),a.W=Ln(r,4),s="/!DataSpaces/DataSpaceMap",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var c=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(Mn(e));return t}(i.content);if(1!==c.length||1!==c[0].comps.length||0!==c[0].comps[0].t||"StrongEncryptionDataSpace"!==c[0].name||"EncryptedPackage"!==c[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(i.content);if(1!=o.length||"StrongEncryptionTransform"!=o[0])throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);Bn(i.content)}catch(f){}if(s="/EncryptionInfo",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var l=function(e){var t=Ln(e);switch(t.Minor){case 2:return[t.Minor,Hn(e)];case 3:return[t.Minor,Wn()];case 4:return[t.Minor,zn(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}(i.content);if(s="/EncryptedPackage",!(i=Ce.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],i.content,n.password||"",n);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],i.content,n.password||"",n);throw new Error("File is password-protected")}function so(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=S(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function io(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return mc(e.slice(r),t);default:break e}return An.to_workbook(e,t)}function co(e,t,r,a){return a?(r.type="string",An.to_workbook(e,r)):An.to_workbook(t,r)}function oo(e,t){d();var r=t||{};if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return oo(new Uint8Array(e),((r=We(r)).type="array",r));"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&!r.type&&(r.type="undefined"!=typeof Deno?"buffer":"array");var a,n=e,s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),vi={},r.dateNF&&(vi.dateNF=r.dateNF),r.type||(r.type=k&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==r.type&&(r.type=k?"buffer":"binary",n=function(e){if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}(e),"undefined"==typeof Uint8Array||k||(r.type="array")),"string"==r.type&&(s=!0,r.type="binary",r.codepage=65001,n=function(e){return e.match(/[^\x00-\x7F]/)?xt(e):e}(e)),"array"==r.type&&"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return(r=We(r)).type="array",oo(N(n),r)}switch((a=so(n,r))[0]){case 208:if(207===a[1]&&17===a[2]&&224===a[3]&&161===a[4]&&177===a[5]&&26===a[6]&&225===a[7])return function(e,t){return Ce.find(e,"EncryptedPackage")?no(e,t):Ac(e,t)}(Ce.read(n,r),r);break;case 9:if(a[1]<=8)return Ac(n,r);break;case 60:return mc(n,r);case 73:if(73===a[1]&&42===a[2]&&0===a[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===a[1])return function(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=wn.to_workbook(e,r);return r.WTF=a,n}catch(s){if(r.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return An.to_workbook(e,t)}}(n,r);break;case 84:if(65===a[1]&&66===a[2]&&76===a[3])return Sn.to_workbook(n,r);break;case 80:return 75===a[1]&&a[2]<9&&a[3]<9?function(e,t){var r=e,a=t||{};return a.type||(a.type=k&&Buffer.isBuffer(e)?"buffer":"base64"),ao(at(r,a),a)}(n,r):co(e,n,r,s);case 239:return 60===a[3]?mc(n,r):co(e,n,r,s);case 255:if(254===a[1])return function(e,t){var r=e;return"base64"==t.type&&(r=S(r)),r=g.utils.decode(1200,r.slice(2),"str"),t.type="binary",io(r,t)}(n,r);if(0===a[1]&&2===a[2]&&0===a[3])return yn.to_workbook(n,r);break;case 0:if(0===a[1]){if(a[2]>=2&&0===a[3])return yn.to_workbook(n,r);if(0===a[2]&&(8===a[3]||9===a[3]))return yn.to_workbook(n,r)}break;case 3:case 131:case 139:case 140:return En.to_workbook(n,r);case 123:if(92===a[1]&&114===a[2]&&116===a[3])return Yn.to_workbook(n,r);break;case 10:case 13:case 32:return function(e,t){var r="",a=so(e,t);switch(t.type){case"base64":r=S(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=He(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(r=yt(r)),t.type="binary",io(r,t)}(n,r);case 137:if(80===a[1]&&78===a[2]&&71===a[3])throw new Error("PNG Image File is not a spreadsheet")}return Tn.indexOf(a[0])>-1&&a[2]<=12&&a[3]<=31?En.to_workbook(n,r):co(e,n,r,s)}function lo(e,t,r,a,n,s,i,c){var o=xr(r),l=c.defval,f=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(g){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+o];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==c.rawNumbers)?m:Pr(p,m,c);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l)}}return{row:u,isempty:h}}function fo(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":o=Dr(f);break;case"number":(o=Dr(e["!ref"])).s.r=f;break;default:o=f}a>0&&(n=0);var h=xr(o.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=o.s.r,b=0,T={};g&&!e[v]&&(e[v]=[]);var E=l.skipHidden&&e["!cols"]||[],w=l.skipHidden&&e["!rows"]||[];for(b=o.s.c;b<=o.e.c;++b)if(!(E[b]||{}).hidden)switch(u[b]=_r(b),r=g?e[v][b]:e[u[b]+h],a){case 1:s[b]=b-o.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-o.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),c=i=Pr(r,null,l),m=T[i]||0){do{c=i+"_"+m++}while(T[c]);T[i]=m,T[c]=1}else T[i]=1;s[b]=c}for(v=o.s.r+n;v<=o.e.r;++v)if(!(w[v]||{}).hidden){var S=lo(e,o,v,u,a,s,g,l);(!1===S.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var ho=/"/g;function uo(e,t,r,a,n,s,i,c){for(var o=!0,l=[],f="",h=xr(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=c.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){o=!1,f=""+(c.rawNumbers&&"n"==d.t?d.v:Pr(d,null,c));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||c.forceQuotes){f='"'+f.replace(ho,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(o=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(ho,'""')+'"'));l.push(f)}return!1===c.blankrows&&o?null:l.join(i)}function po(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=Dr(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),c=void 0!==a.RS?a.RS:"\n",o=c.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=_r(p));for(var m=0,g=n.s.r;g<=n.e.r;++g)(d[g]||{}).hidden||null!=(f=uo(e,n,g,h,i,o,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&r.push((m++?c:"")+f));return delete a.dense,r.join("")}function mo(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},c=0,o=0;if(i&&null!=n.origin)if("number"==typeof n.origin)c=n.origin;else{var l="string"==typeof n.origin?Nr(n.origin):n.origin;c=l.r,o=l.c}var f={s:{c:0,r:0},e:{c:o,r:c+t.length-1+s}};if(i["!ref"]){var h=Dr(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==c&&(c=h.e.r+1,f.e.r=c+t.length-1+s)}else-1==c&&(c=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach((function(e,t){_e(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",p=Or({c:o+d,r:c+t+s});a=go(i,p),!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=Re(l)),h=n.dateNF||W[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l}))})),f.e.c=Math.max(f.e.c,o+u.length-1);var p=xr(c);if(s)for(d=0;d<u.length;++d)i[_r(d+o)+p]={t:"s",v:u[d]};return i["!ref"]=Ir(f),i}function go(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=Nr(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return go(e,Or("number"!=typeof t?t:{r:t,c:r||0}))}function vo(){return{SheetNames:[],Sheets:{}}}function bo(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(function(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;ji.forEach((function(a){if(-1!=e.indexOf(a)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}))}(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function To(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var Eo={encode_col:_r,encode_row:xr,encode_cell:Or,encode_range:Ir,decode_col:Cr,decode_row:yr,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:Nr,decode_range:Rr,format_cell:Pr,sheet_add_aoa:Mr,sheet_add_json:mo,sheet_add_dom:Oc,aoa_to_sheet:Br,json_to_sheet:function(e,t){return mo(null,e,t)},table_to_sheet:Rc,table_to_book:function(e,t){return Lr(Rc(e,t),t)},sheet_to_csv:po,sheet_to_txt:function(e,t){return t||(t={}),t.FS="\t",t.RS="\n",po(e,t)},sheet_to_json:fo,sheet_to_html:function(e,t){var r=t||{},a=null!=r.header?r.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',n=null!=r.footer?r.footer:"</body></html>",s=[a],i=Rr(e["!ref"]);r.dense=Array.isArray(e),s.push(function(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}(0,0,r));for(var c=i.s.r;c<=i.e.r;++c)s.push(Nc(e,i,c,r));return s.push("</table>"+n),s.join("")},sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=Dr(e["!ref"]),i="",c=[],o=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)c[n]=_r(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=xr(f),n=s.s.c;n<=s.e.c;++n)if(r=c[n]+i,a="",void 0!==(t=l?(e[f]||[])[n]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}}o[o.length]=r+"="+a}return o},sheet_to_row_object_array:fo,sheet_get_cell:go,book_new:vo,book_append_sheet:bo,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:To,cell_set_internal_link:function(e,t,r){return To(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:Dr(t),s="string"==typeof t?t:Ir(t),i=n.s.r;i<=n.e.r;++i)for(var c=n.s.c;c<=n.e.c;++c){var o=go(e,i,c);o.t="n",o.F=s,delete o.v,i==n.s.r&&c==n.s.c&&(o.f=r,a&&(o.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const wo=t({data:()=>({schools:[],schoolNames:[],selectedIndex:null,selectedSchool:"",recipes:[]}),onLoad(){const e=uni.getStorageSync("schools");Array.isArray(e)&&(this.schools=e,this.schoolNames=e.map((e=>e.schoolName||"")),uni.showToast({title:"加载学校列表成功",icon:"success"}))},computed:{selectedIndexValid(){return null!==this.selectedIndex}},methods:{onSchoolChange(e){this.selectedIndex=e.detail.value,this.selectedSchool=this.schoolNames[this.selectedIndex]},confirmSelection(){uni.showToast({title:`已选择 ${this.selectedSchool}`,icon:"none"})},uploadFile(){if(this.selectedIndexValid)if(uni.chooseMessageFile)uni.chooseMessageFile({count:1,type:"file",extension:["xlsx"],success:e=>this.handleFile(e.tempFiles[0].path),fail:()=>uni.showToast({title:"选择失败",icon:"error"})});else{a("lemonjk-FileSelect").showPicker({mimeType:"*/*",utisType:["public.data"],filterConfig:{fileExtension:["xlsx","xls"]}},(e=>this.parseXLSX(e.files[0].filePath).then(this.saveRecipes).catch((()=>uni.showToast({title:"解析失败",icon:"error"})))))}},handleFile(e){uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>{const t=wx.base64ToArrayBuffer(e.data);this.parseData(t)},fail:()=>uni.showToast({title:"读取失败",icon:"error"})})},parseData(e){const t=oo(e,{type:"array"}),r=t.Sheets[t.SheetNames[0]],a=Eo.sheet_to_json(r,{header:["foodName"],range:1}).map((e=>({foodName:String(e.foodName||"")})));this.saveRecipes(a)},readFile:e=>new Promise(((t,r)=>{plus.io.resolveLocalFileSystemURL(e,(e=>e.file((e=>{const a=new plus.io.FileReader;a.readAsDataURL(e),a.onloadend=e=>t(e.target.result),a.onerror=e=>r(e)}),(e=>r(e)))),(e=>r(e)))})),async parseXLSX(e){const t=oo((await this.readFile(e)).replace(/^.*base64,/,""),{type:"base64"}),r=t.Sheets[t.SheetNames[0]];return Eo.sheet_to_json(r,{header:["foodName"],range:1}).map((e=>({foodName:String(e.foodName||"")})))},saveRecipes(e){this.recipes=e;const t=uni.getStorageSync("schoolRecipes")||{};t[this.selectedSchool]=e,uni.setStorageSync("schoolRecipes",t),n("log","at pages/food_updata/food_updata.vue:152",t),uni.showToast({title:"食谱上传并保存成功",icon:"success"})}}},[["render",function(t,r,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("text",{class:"title"},"学校食谱上传")]),e.createElementVNode("view",{class:"selector"},[e.createElementVNode("picker",{mode:"selector",range:s.schoolNames,onChange:r[0]||(r[0]=(...e)=>i.onSchoolChange&&i.onSchoolChange(...e))},[e.createElementVNode("view",{class:"picker-inner"},[e.createElementVNode("text",null,e.toDisplayString(s.selectedSchool||"请选择学校"),1)])],40,["range"]),e.createElementVNode("button",{class:"btn confirm",disabled:!i.selectedIndexValid,onClick:r[1]||(r[1]=(...e)=>i.confirmSelection&&i.confirmSelection(...e))},"确定",8,["disabled"])]),s.recipes.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"card"},[e.createElementVNode("scroll-view",{"scroll-x":"",class:"table-wrapper"},[e.createElementVNode("view",{class:"table-row header"},[e.createElementVNode("view",{class:"cell"},"序号"),e.createElementVNode("view",{class:"cell"},"食品名称")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.recipes,((t,r)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["table-row",{"odd-row":r%2==0,"even-row":r%2==1}]),key:r},[e.createElementVNode("view",{class:"cell"},e.toDisplayString(r+1),1),e.createElementVNode("view",{class:"cell"},e.toDisplayString(t.foodName),1)],2)))),128))])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"upload-section"},[e.createElementVNode("button",{class:"btn upload",disabled:!i.selectedIndexValid,onClick:r[2]||(r[2]=(...e)=>i.uploadFile&&i.uploadFile(...e))},"上传食谱文件",8,["disabled"])])])}],["__scopeId","data-v-0630ac79"]]),So=a("lemonjk-FileSelect");const ko=t({data:()=>({schoolNames:[]}),onLoad(){const e=uni.getStorageSync("schools")||[];let t=[];e.length&&"object"==typeof e[0]&&e[0].schoolName?t=e.map((e=>e.schoolName)):Array.isArray(e)&&(t=e),t.length&&(this.schoolNames=t,uni.showToast({title:"加载本地数据成功",icon:"success"}))},methods:{refreshPage(){this.$forceUpdate(),uni.showToast({title:"界面已刷新",icon:"success"})},uploadFile(){const e=uni.chooseMessageFile||uni.chooseFile;e?this.nativePick(e):So.isHaveSecurityScopeGrant({},(e=>{"yes"===e.result?this.pluginPick():So.reqCustomPickerPermission({},(e=>{3002===e.code?this.pluginPick():uni.showModal({content:"需要文件访问权限",showCancel:!1})}))}))},nativePick(e){e({count:1,type:"file",extension:["xlsx","xls"],success:e=>{const t=e.tempFiles||e.files||[];if(!t.length)return uni.showToast({title:"未选择文件",icon:"error"});const r=t[0];if(r.file&&"undefined"!=typeof FileReader){const e=new FileReader;e.onload=e=>this.handleBase64(e.target.result),e.onerror=()=>uni.showToast({title:"读取失败",icon:"error"}),e.readAsDataURL(r.file)}else{const e=r.path||r.filePath;this.readAndParse(e)}},fail:()=>uni.showToast({title:"选择失败",icon:"error"})})},pluginPick(){So.showPicker({pathScope:"/Download",mimeType:"*/*",utisType:["public.data"],filterConfig:{fileExtension:["xlsx","xls"]}},(e=>{if(0!==e.code||!e.files||!e.files.length)return uni.showToast({title:"未获取文件",icon:"error"});this.readAndParse(e.files[0].filePath)}))},readAndParse(e){n("log","at pages/school_updata/school_updata.vue:111","读取文件路径:",e),"undefined"!=typeof plus&&plus.io?plus.io.resolveLocalFileSystemURL(e,(e=>{e.file((e=>{const t=new plus.io.FileReader;t.onloadend=e=>this.handleBase64(e.target.result),t.readAsDataURL(e)}))}),(()=>uni.showToast({title:"读取失败",icon:"error"}))):uni.getFileSystemManager?uni.getFileSystemManager().readFile({filePath:e,encoding:"base64",success:e=>this.handleBase64("data:;base64,"+e.data),fail:()=>uni.showToast({title:"读取失败",icon:"error"})}):uni.showToast({title:"环境不支持",icon:"error"})},handleBase64(e){n("log","at pages/school_updata/school_updata.vue:130","解析到 Base64 长度:",e.length);const t=e.split(",")[1],r=oo(this.base64ToArrayBuffer(t),{type:"array"}),a=r.Sheets[r.SheetNames[0]],s=Eo.sheet_to_json(a);n("log","at pages/school_updata/school_updata.vue:136","sheet_to_json 数据:",s);const i=s.map((e=>e["学校名称"])).filter((e=>e)).map((e=>String(e).trim()));n("log","at pages/school_updata/school_updata.vue:141","提取到的学校名称:",i),this.saveData(i)},base64ToArrayBuffer(e){const t=atob(e),r=t.length,a=new Uint8Array(r);for(let n=0;n<r;n++)a[n]=t.charCodeAt(n);return a.buffer},saveData(e){this.schoolNames=e;const t=e.map((e=>({schoolName:e})));uni.setStorageSync("schools",t),uni.showToast({title:"保存成功",icon:"success"}),setTimeout((()=>{uni.navigateBack()}),500)}}},[["render",function(t,r,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("text",{class:"title"},"学校名称上传"),e.createElementVNode("view",{class:"actions"},[e.createElementVNode("button",{class:"btn upload",onClick:r[0]||(r[0]=(...e)=>i.uploadFile&&i.uploadFile(...e))},"文件上传"),e.createElementVNode("button",{class:"btn refresh",onClick:r[1]||(r[1]=(...e)=>i.refreshPage&&i.refreshPage(...e))},"刷新")])]),e.createElementVNode("view",{class:"card"},[e.createElementVNode("scroll-view",{"scroll-x":"",class:"table-wrapper"},[e.createElementVNode("view",{class:"table-row header"},[e.createElementVNode("view",{class:"cell"},"序号"),e.createElementVNode("view",{class:"cell"},"学校名称")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.schoolNames,((t,r)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["table-row",{"odd-row":r%2==0,"even-row":r%2==1}]),key:r},[e.createElementVNode("view",{class:"cell"},e.toDisplayString(r+1),1),e.createElementVNode("view",{class:"cell"},e.toDisplayString(t),1)],2)))),128))]),0===s.schoolNames.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-tip"},"暂无数据，请先上传文件。")):e.createCommentVNode("",!0)])])}],["__scopeId","data-v-049489e1"]]),Ao=uni.requireUTSPlugin("uni_modules/kaka-KPrinter"),yo=a("Fvv-UniSerialPort");const xo=t({data:()=>({schoolNames:[],selectedSchool:null,showSchoolList:!1,recipeList:[],selectedFood:null,cardnumber:"1",capturedImage1:"",vendorId:463,camerasOpened:!1,flag:"",printData:null,printerConnected:!1,currentTime:""}),methods:{toggleSchoolList(){this.showSchoolList=!this.showSchoolList},onSchoolSelect(e){this.selectedSchool=this.schoolNames[e],this.showSchoolList=!1},fetchRecipes(){if(!this.selectedSchool)return void uni.showToast({title:"请选择学校",icon:"none"});let e=uni.getStorageSync("schoolRecipes"),t={};try{t="string"==typeof e?JSON.parse(e):e||{}}catch(r){n("error","at pages/Module/Module.vue:145","解析 schoolRecipes 失败",r)}this.recipeList=t[this.selectedSchool]||[],this.selectedFood=null,uni.showToast({title:"食谱已加载",icon:"success"})},selectOnlyFood(e){this.selectedFood=this.selectedFood===e?null:e},openCamera(){s.closedUvcCamera(this.vendorId,(()=>{uni.getSystemInfo({success:e=>{uni.createSelectorQuery().select(".camera-view").boundingClientRect((t=>{const r=t.width/e.screenWidth,a=t.height/e.screenHeight,n=t.top/e.screenHeight,i=t.left/e.screenWidth;s.openUvcCamera({vendorId:this.vendorId,widthView:r,heightView:a,topMargin:n,leftMargin:i,quirkFixBandwidth:!0,textData:[],imageData:[]},(e=>{}))})).exec()}})}))},takePhoto(){s.getUvcCameraImg({vendorId:this.vendorId,someQuality:40,isBase64:!1,keepAlive:!0},(e=>{const t=JSON.parse(e);"ok"===t.msg?this.capturedImage1=t.file:uni.showToast({title:"摄像头拍照失败",icon:"none"})}))},onMessage(e){const t=this.hexToText(e).trim().match(/^[sw][gn](-?\d+\.\d+)[ks][gj]$/i);let r=t?t[0].slice(-2).toLowerCase():null;this.flag=r,t&&(this.cardnumber=parseFloat(t[1]))},hexToText:e=>(e.replace(/\s+/g,"").match(/.{1,2}/g)||[]).map((e=>String.fromCharCode(parseInt(e,16)))).join(""),formatNumber:e=>Number(e).toFixed(3),onPeel(){yo.sendBytes([84])},onZero(){yo.sendBytes([90])},printRecord(e){this.printerConnected?(Ao.tscSize({width:76,height:42}),Ao.tscCls(),Ao.tscDensity(8),Ao.tscDirection({directionReverse:!1,isMirror:!1}),Ao.tscText({x:200,y:50,xScal:1,yScal:1,rotation:0,font:"TSS24.BF2",content:`食谱: ${e.food||""}`}),Ao.tscText({x:200,y:100,xScal:1,yScal:1,rotation:0,font:"TSS24.BF2",content:`时间: ${e.time||""}`}),Ao.tscText({x:200,y:150,xScal:1,yScal:1,rotation:0,font:"TSS24.BF2",content:`重量: ${e.weight||""} 公斤`}),Ao.tscText({x:200,y:200,xScal:1,yScal:1,rotation:0,font:"TSS24.BF2",content:"操作人: 刘晓宇"}),Ao.tscPrint(1),Ao.writeDataUSB()):uni.showToast({title:"打印机未连接",icon:"none"})},onSubmit(){const e=[];if(this.selectedFood||e.push("食谱"),this.capturedImage1||e.push("照片"),e.length)return void uni.showToast({title:`缺少${e.join("、")}`,icon:"none"});const t=new Date,r=`${t.getFullYear()}-${t.getMonth()+1}-${t.getDate()} ${t.getHours()}:${t.getMinutes()}:${t.getSeconds()}`,a={food:this.selectedFood,time:r,weight:this.formatNumber(this.cardnumber),imagePath1:this.capturedImage1},n=uni.getStorageSync("submissionRecords")||[];n.push(a),uni.setStorageSync("submissionRecords",n),this.printData={food:this.selectedFood,time:r,weight:this.formatNumber(this.cardnumber)},uni.showToast({title:"提交成功",icon:"success"}),this.printRecord(a),this.selectedFood=null,this.cardnumber=null,this.capturedImage1=""},get_data(){s.closedUvcCamera(this.vendorId,(()=>{})),this.camerasOpened=!1,uni.navigateTo({url:"/pages/get_data/get_data"})},school(){s.closedUvcCamera(this.vendorId,(()=>{})),this.camerasOpened=!1,uni.navigateTo({url:"/pages/school_updata/school_updata"})},food(){s.closedUvcCamera(this.vendorId,(()=>{})),this.camerasOpened=!1,uni.navigateTo({url:"/pages/food_updata/food_updata"})},updateTime(){const e=new Date,t=e=>e<10?"0"+e:e;this.currentTime=`${e.getFullYear()}-${t(e.getMonth()+1)}-${t(e.getDate())} ${t(e.getHours())}:${t(e.getMinutes())}:${t(e.getSeconds())}`},connectFixedPrinter(){Ao.getUsbDeviceList((e=>{let t=[];if(Array.isArray(e))t=e.map((e=>e.devicePath||e.path||e.deviceName)).filter((e=>e&&e.startsWith("/dev/bus/usb/005")));else if(e&&(e.devicePath||e.path||e.deviceName)){const r=e.devicePath||e.path||e.deviceName;r&&r.startsWith("/dev/bus/usb/005")&&(t=[r])}if(0===t.length)return void(this.printerConnected=!1);let r=!1,a=0;const n=()=>{r||a>=t.length||Ao.connectUSB(t[a])};Ao.onUSBConnectStateChange({onSuccess:()=>{this.printerConnected=!0,r=!0},onDisconnect:()=>{this.printerConnected=!1},onFail:()=>{a++,n()}}),n()}))}},onShow(){this.camerasOpened||this.openCamera(),this.updateTime(),this._timer=setInterval(this.updateTime,1e3),this.connectFixedPrinter()},onHide(){s.closedUvcCamera(this.vendorId,(()=>{})),this.camerasOpened=!1,clearInterval(this._timer)},onUnload(){s.closedUvcCamera(this.vendorId,(()=>{})),this.camerasOpened=!1,clearInterval(this._timer)},mounted(){const e=uni.getStorageSync("schools")||[];this.schoolNames=Array.isArray(e)?e.map((e=>e.schoolName||"")):[],yo.setPath("/dev/ttyS1"),yo.setBaudRate(9600),yo.open((e=>{e.status&&yo.onMessageHex((e=>this.onMessage(e)))}))}},[["render",function(t,r,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"main-bg"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("view",{class:"nav-left"},[e.createElementVNode("image",{src:"/static/logo.png",class:"logo",mode:"aspectFit"}),e.createElementVNode("text",{class:"sys-title"},"智能称重采集系统")]),e.createElementVNode("view",{class:"nav-center"},[e.createElementVNode("text",{class:"nav-time"},e.toDisplayString(s.currentTime),1)]),e.createElementVNode("view",{class:"nav-right"},[e.createElementVNode("view",{class:e.normalizeClass(["printer-status-card",s.printerConnected?"connected":"disconnected"])},[e.createElementVNode("text",{class:"printer-status-text"}," 打印机"+e.toDisplayString(s.printerConnected?"已连接":"未连接"),1)],2)])]),e.createElementVNode("view",{class:"main-content"},[e.createElementVNode("view",{class:"side-card left-card"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("text",{class:"section-title"},"学校选择")]),e.createElementVNode("view",{class:"school-select-container"},[e.createElementVNode("view",{class:"school-select-box",onClick:r[0]||(r[0]=(...e)=>i.toggleSchoolList&&i.toggleSchoolList(...e))},[e.createElementVNode("text",{class:"select-text"},e.toDisplayString(s.selectedSchool||"请选择学校"),1),e.createElementVNode("text",{class:"arrow"},"▼")]),s.showSchoolList?(e.openBlock(),e.createElementBlock("view",{key:0,class:"dropdown-school"},[e.createElementVNode("scroll-view",{"scroll-y":"",class:"dropdown-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.schoolNames,((t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"dropdown-item",onClick:e=>i.onSchoolSelect(r)},[e.createElementVNode("text",null,e.toDisplayString(t),1)],8,["onClick"])))),128))])])):e.createCommentVNode("",!0)]),e.createElementVNode("button",{class:"btn primary-btn fetch-btn",onClick:r[1]||(r[1]=(...e)=>i.fetchRecipes&&i.fetchRecipes(...e))},"获取食谱"),e.createElementVNode("view",{class:"card-header",style:{"margin-top":"2rem"}},[e.createElementVNode("text",{class:"section-title"},"食谱选择")]),e.createElementVNode("scroll-view",{"scroll-y":"",class:"recipe-list-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.recipeList,((t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:e.normalizeClass(["recipe-card",{selected:s.selectedFood===t.foodName}]),onClick:e=>i.selectOnlyFood(t.foodName)},[e.createElementVNode("text",{class:"recipe-text"},e.toDisplayString(t.foodName),1)],10,["onClick"])))),128))])]),e.createElementVNode("view",{class:"center-card"},[e.createElementVNode("view",{class:"camera-section"},[e.createElementVNode("view",{class:"camera-preview"},[e.createElementVNode("view",{class:"camera-view"})]),e.createElementVNode("view",{class:"photo-preview-container"},[s.capturedImage1?(e.openBlock(),e.createElementBlock("image",{key:0,src:s.capturedImage1,mode:"aspectFill",class:"photo-preview-img"},null,8,["src"])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"photo-placeholder"},[e.createElementVNode("text",{class:"placeholder-text"},"拍照预览区")]))])]),e.createElementVNode("button",{class:"btn primary-btn photo-btn",onClick:r[2]||(r[2]=(...e)=>i.takePhoto&&i.takePhoto(...e))},"拍照")]),e.createElementVNode("view",{class:"side-card right-card"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("text",{class:"section-title"},"当前重量")]),e.createElementVNode("view",{class:"weight-display-container"},[e.createElementVNode("view",{class:"weight-display"},[e.createElementVNode("text",{class:"weight-num"},e.toDisplayString(null!==s.cardnumber?i.formatNumber(s.cardnumber):"--"),1),e.createElementVNode("text",{class:"weight-unit"},e.toDisplayString("kg"===s.flag?"公斤":"sj"===s.flag?"斤":""),1)])]),e.createElementVNode("view",{class:"weight-controls"},[e.createElementVNode("button",{class:"btn accent-btn control-btn",onClick:r[3]||(r[3]=(...e)=>i.onPeel&&i.onPeel(...e))},"去皮"),e.createElementVNode("button",{class:"btn accent-btn control-btn",onClick:r[4]||(r[4]=(...e)=>i.onZero&&i.onZero(...e))},"置零")])])]),e.createElementVNode("view",{class:"footer-bar"},[e.createElementVNode("button",{class:"btn primary-btn footer-btn",onClick:r[5]||(r[5]=(...e)=>i.onSubmit&&i.onSubmit(...e))},"提交数据"),e.createElementVNode("button",{class:"btn neutral-btn footer-btn",onClick:r[6]||(r[6]=(...e)=>i.get_data&&i.get_data(...e))},"查看数据"),e.createElementVNode("button",{class:"btn secondary-btn footer-btn",onClick:r[7]||(r[7]=(...e)=>i.school&&i.school(...e))},"上传学校"),e.createElementVNode("button",{class:"btn secondary-btn footer-btn",onClick:r[8]||(r[8]=(...e)=>i.food&&i.food(...e))},"上传食谱")])])}],["__scopeId","data-v-8d01b0b1"]]);const Co=t({data:()=>({records:[],displayed:[],filters:{recipe:"",date:"",dateLabel:""},expandedIndices:{}}),methods:{loadData(){let e=uni.getStorageSync("submissionRecords")||[];try{e="string"==typeof e?JSON.parse(e):e}catch{e=[]}this.records=Array.isArray(e)?e:[],this.displayed=this.records,this.expandedIndices={},uni.showToast({title:"数据已加载",icon:"success"})},onDateChange(e){const t=e.detail.value;this.filters.date=t;const[r,a,n]=t.split("-");this.filters.dateLabel=`${r}-${Number(a)}-${Number(n)}`},applyFilters(){this.displayed=this.records.filter((e=>{const t=""===this.filters.recipe||e.food.includes(this.filters.recipe);let r=!0;if(""!==this.filters.date){r=e.time.split(" ")[0]===this.filters.dateLabel}return t&&r})),this.expandedIndices={}},toggleDetails(e){this.$set(this.expandedIndices,e,!this.expandedIndices[e])},isExpanded(e){return!!this.expandedIndices[e]}},onLoad(){this.loadData()}},[["render",function(t,r,a,n,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"nav-bar"},[e.createElementVNode("text",{class:"title"},"数据查询"),e.createElementVNode("view",{class:"actions"},[e.createElementVNode("button",{class:"btn refresh",onClick:r[0]||(r[0]=(...e)=>i.loadData&&i.loadData(...e))},"刷新")])]),e.createElementVNode("view",{class:"filter-bar"},[e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":r[1]||(r[1]=e=>s.filters.recipe=e),class:"filter-input",placeholder:"配方名"},null,512),[[e.vModelText,s.filters.recipe]]),e.createElementVNode("picker",{mode:"date",value:s.filters.date,onChange:r[2]||(r[2]=(...e)=>i.onDateChange&&i.onDateChange(...e))},[e.createElementVNode("view",{class:"date-picker"},e.toDisplayString(s.filters.dateLabel||"提交日期"),1)],40,["value"]),e.createElementVNode("button",{class:"btn query",onClick:r[3]||(r[3]=(...e)=>i.applyFilters&&i.applyFilters(...e))},"查询")]),e.createElementVNode("scroll-view",{class:"card-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.displayed,((t,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"card"},[e.createElementVNode("view",{class:"card-header",onClick:e=>i.toggleDetails(r)},[e.createElementVNode("text",null,"配方："+e.toDisplayString(t.food),1),e.createElementVNode("text",null,"重量："+e.toDisplayString(t.weight)+" Kg",1),e.createElementVNode("text",null,"提交时间："+e.toDisplayString(t.time),1),e.createElementVNode("text",{class:"toggle-icon"},e.toDisplayString(i.isExpanded(r)?"收起 ▲":"展开 ▼"),1)],8,["onClick"]),i.isExpanded(r)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-wrapper"},[e.createElementVNode("text",{class:"detail-label"},"照片1："),t.imagePath1?(e.openBlock(),e.createElementBlock("image",{key:0,src:t.imagePath1,mode:"aspectFill",class:"detail-image"},null,8,["src"])):(e.openBlock(),e.createElementBlock("text",{key:1,class:"no-image"},"无照片1")),e.createElementVNode("text",{class:"detail-label"},"照片2："),t.imagePath2?(e.openBlock(),e.createElementBlock("image",{key:2,src:t.imagePath2,mode:"aspectFill",class:"detail-image"},null,8,["src"])):(e.openBlock(),e.createElementBlock("text",{key:3,class:"no-image"},"无照片2"))])):e.createCommentVNode("",!0)])))),128))]),0===s.displayed.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"empty-state"},[e.createElementVNode("text",{class:"empty-text"},"暂无符合条件的数据")])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-b3088934"]]);__definePage("pages/login/login",r),__definePage("pages/text/test",i),__definePage("pages/index/index",c),__definePage("pages/food_updata/food_updata",wo),__definePage("pages/school_updata/school_updata",ko),__definePage("pages/Module/Module",xo),__definePage("pages/get_data/get_data",Co);const _o={onLaunch:function(){n("log","at App.vue:4","App Launch")},onShow:function(){n("log","at App.vue:7","App Show")},onHide:function(){n("log","at App.vue:10","App Hide")}};const{app:No,Vuex:Oo,Pinia:Ro}={app:e.createVueApp(_o)};uni.Vuex=Oo,uni.Pinia=Ro,No.provide("__globalStyles",__uniConfig.styles),No._component.mpType="app",No._component.render=()=>{},No.mount("#app")}(Vue);
