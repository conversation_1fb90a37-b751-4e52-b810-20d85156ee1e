# kaka-KPrinter

欢迎使用 KPrinter UTS插件, 此插件适用于蓝牙热敏打印机，以下是说明及使用教程：

- 适用厂商：`佳博`、 `启锐`、`汉印` 等主流打印机
- 支持的指令类型：`TSPL/TSC`、 `CPCL`、`ESC`
- 支持平台：`安卓`、 `iOS`、`鸿蒙-开发中`
- 单位换算：`200dip：1mm = 8dot`、`300dip：1mm = 12dot`
- **uni-app x  / uvue demo** 请见 [kprinter-uvue demo](https://gitee.com/kaka568/kprinter-uvue)

### 导入

```vue
import * as KPrinter from '@/uni_modules/kaka-KPrinter';
```


### 蓝牙使用说明

---

#### 扫描设备

```ts
KPrinter.startScan((device) => {
    let d = this.deviceList.find((item) => device.deviceId == item.deviceId)
    if (d == undefined) {
            console.log(`添加设备: ${JSON.stringify(device)}`);
            this.deviceList.push(device);
    }
});
```

#### 停止扫描

```ts
KPrinter.stopScan();
```

#### 连接设备

```ts
KPrinter.connect(deviceId);
```

#### 断开设备

```ts
KPrinter.disconnect();
```

#### 写入数据

```ts
/*
需要先构造指令，写入才有效
    如：
        KPrinter.tscSelfTest()
        KPrinter.writeData();
*/ 
KPrinter.writeData();
```

#### 蓝牙事件监听

##### 蓝牙开关关闭监听

```ts
KPrinter.onBlueStateChange((isOn) => {
    console.log("蓝牙 " + (isOn ? "开启" : "关闭"), isOn);
})
```

##### 蓝牙连接状态监听

```ts
KPrinter.onConnectStateChange({
    onSuccess: (device) => {
        console.log("连接成功", device);
    },
    onDisconnect: (device) => {
        console.log("断开连接", device);
    },
    onFail: (msg) => {
        console.log("连接失败", msg);
    }
});
```

##### 蓝牙数据回传监听

```ts
KPrinter.onDataReceive((data) => {
    console.log("收到蓝牙数据: ", data);
})
```

##### 数据写入是否完成监听

```ts
KPrinter.onWriteComplete((isComplete) => {
	console.log("写入 " + (isComplete ? "成功" : "失败"), isComplete);
});
```

### TSPL/TSC指令使用说明

---

#### tscSize指令

- 作用：**设置标签尺寸的宽和高**
- 参数类型：`SizeParam`
- 示例：

```ts
// 单位毫米
KPrinter.tscSize({
      width: 76,    // 标签宽度
      height: 130,  // 标签高度
});
```

#### tscCls指令

- 作用：**清除打印缓冲区， 必须放在size后面，通常是跟在size后面**
- 示例：

```ts
KPrinter.tscCls();
```

#### tscBar指令

- 作用：**在标签上绘制黑块，画线**
- 参数类型：`RectParam`
- 示例：

```ts
// 单位点
KPrinter.tscBar({
    x: 100,     // x 起始横坐标
    y: 50,      // y 起始纵坐标
    width: 500,    // width 线宽，以点(dot)表示
    height: 5      // height 线高，以点(dot)表示
})
```

#### tscBarCode指令

- 作用：**在标签上绘制一维条码**
- 参数类型：`BarCodeParam`
- 示例：

```ts
// 单位点
KPrinter.tscBarCode({
    x: 10,                     // x 起始横坐标
    y: 20,                     // y 起始纵坐标
    codeType: "128",          // 条码类型()
    height: 100,              // 条码高度
    readable: true,           // 是否可识别
    content: "1234567890"     // 条码内容
})
```

#### tscQRCode指令

- 作用：**在标签上绘制QRCode二维码**
- 参数类型：`QRCodeParam`
- 示例：

```ts
// 单位点
KPrinter.tscQRCode({
    x: 30,                     // x 起始横坐标
    y: 200,                    // y 起始纵坐标
    cellWidth: 6,              // cellWidth 二维码宽度1~10
    rotation: 0,               // rotation 旋转角度，顺时钟方向，0不旋转，90: 90度，180:180度，270: 270度
    content: "809088",         //content   条码内容
})
```

#### tscText指令

- 作用：**在标签上绘制QRCode二维码**
- 参数类型：`TextParam`
- 示例：

```ts
// 单位点
KPrinter.tscText({
    x: 30,                     // x 起始横坐标
    y: 400,                    //  y 起始纵坐标
    xScal: 1,                  //  xScal  横向放大
    yScal: 1,                  //  yScal  纵向放大
    rotation: 0,               //  rotation  旋转角度 0 90 180 270
    font: "TSS24.BF2",         //  font  字体类型
    content: "古道西风瘦马",     //  content  文字字符串
})
```


#### tscImage指令

- 作用：**在标签上绘制QRCode二维码**
- 参数类型：`TextParam`
- 示例：

```ts
// 单位点
KPrinter.tscImage({
		x: 30, 			// x 起始横坐标
		y: 500, 		//  y 起始纵坐标
		base64Str: this.base64	// 图片base64
})
```



