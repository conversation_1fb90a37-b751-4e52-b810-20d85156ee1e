//
//  KKEthernet.h
//  KSocket
//
//  Created by kaka on 2/14/25.
//

#import <Foundation/Foundation.h>

@protocol KEthernetDelegate <NSObject>

@optional
- (void)connectStateDidChange:(NSDictionary *_Nonnull)dict;
- (void)onReceive:(NSData *_Nullable)data;
- (void)onWriteComplete:(BOOL)isComplete;

@end

@interface KEthernet : NSObject

@property (nonatomic, weak) id<KEthernetDelegate> delegate;

- (void)configure:(NSString *_Nullable)str;

- (void)connect:(NSString *_Nullable)str;

- (void)disconnect;

- (void)writeCmd;

- (void)pelar;

- (BOOL)isConnect;

@end

