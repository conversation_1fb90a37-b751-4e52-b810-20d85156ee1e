if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>er,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}__definePage("pages/index/index",((e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n})({data:()=>({photoPath:""}),onLoad(){var e;this.camera=(e="uni-camera-plugin",weex.requireModule(e))},methods:{async onOpenCamera(){try{await openCamera(),uni.showToast({title:"摄像头已打开",icon:"success"})}catch(e){uni.showModal({title:"错误",content:e.toString(),showCancel:!1})}},async onCapturePhoto(){try{const e=await capturePhoto();this.photoPath=e,uni.showToast({title:"拍照成功",icon:"success"})}catch(e){uni.showModal({title:"拍照失败",content:e.toString(),showCancel:!1})}}}},[["render",function(t,n,o,r,a,i){const c=e.resolveComponent("surface-view"),l=e.resolveComponent("native-view");return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createVNode(l,{class:"preview-box"},{default:e.withCtx((()=>[e.createVNode(c,{id:"cameraView",style:{width:"100%",height:"100%"}})])),_:1}),e.createElementVNode("view",{class:"btn-group"},[e.createElementVNode("button",{class:"btn",onClick:n[0]||(n[0]=(...e)=>i.onOpenCamera&&i.onOpenCamera(...e))},"打开摄像头"),e.createElementVNode("button",{class:"btn",onClick:n[1]||(n[1]=(...e)=>i.onCapturePhoto&&i.onCapturePhoto(...e))},"拍 照")]),a.photoPath?(e.openBlock(),e.createElementBlock("image",{key:0,src:a.photoPath,class:"photo-preview",mode:"aspectFill"},null,8,["src"])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-376a79bb"]]));const n={onLaunch:function(){t("log","at App.vue:4","App Launch")},onShow:function(){t("log","at App.vue:7","App Show")},onHide:function(){t("log","at App.vue:10","App Hide")}};const{app:o,Vuex:r,Pinia:a}={app:e.createVueApp(n)};uni.Vuex=r,uni.Pinia=a,o.provide("__globalStyles",__uniConfig.styles),o._component.mpType="app",o._component.render=()=>{},o.mount("#app")}(Vue);
