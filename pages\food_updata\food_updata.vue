<template>
  <view class="container">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <text class="title">学校食谱上传</text>
    </view>

    <!-- 学校选择 -->
    <view class="selector">
      <picker mode="selector" :range="schoolNames" @change="onSchoolChange">
        <view class="picker-inner">
          <text>{{ selectedSchool || '请选择学校' }}</text>
        </view>
      </picker>
      <button class="btn confirm" :disabled="!selectedIndexValid" @click="confirmSelection">确定</button>
    </view>

    <!-- 食谱表格 -->
    <view class="card" v-if="recipes.length">
      <scroll-view scroll-x class="table-wrapper">
        <view class="table-row header">
          <view class="cell">序号</view>
          <view class="cell">食品名称</view>
        </view>
        <view
          class="table-row"
          v-for="(item, idx) in recipes"
          :key="idx"
          :class="{ 'odd-row': idx % 2 === 0, 'even-row': idx % 2 === 1 }"
        >
          <view class="cell">{{ idx + 1 }}</view>
          <view class="cell">{{ item.foodName }}</view>
        </view>
      </scroll-view>
    </view>

    <!-- 上传按钮 -->
    <view class="upload-section">
      <button class="btn upload" :disabled="!selectedIndexValid" @click="uploadFile">上传食谱文件</button>
    </view>
  </view>
</template>

<script>
import * as XLSX from 'xlsx';
export default {
  data() {
    return {
      schools: [],           // 本地缓存的学校对象列表
      schoolNames: [],       // 仅名称数组，用于 picker
      selectedIndex: null,   // 选中学校的索引
      selectedSchool: '',    // 选中学校名称
      recipes: []            // 上传解析后的食谱列表
    };
  },
  onLoad() {
    // 加载本地缓存的学校
    const cached = uni.getStorageSync('schools');
    if (Array.isArray(cached)) {
      this.schools = cached;
      this.schoolNames = cached.map(s => s.schoolName || '');
      uni.showToast({ title: '加载学校列表成功', icon: 'success' });
    }
  },
  computed: {
    selectedIndexValid() {
      return this.selectedIndex !== null;
    }
  },
  methods: {
    onSchoolChange(e) {
      this.selectedIndex = e.detail.value;
      this.selectedSchool = this.schoolNames[this.selectedIndex];
    },
    confirmSelection() {
      uni.showToast({ title: `已选择 ${this.selectedSchool}`, icon: 'none' });
    },
    uploadFile() {
      if (!this.selectedIndexValid) return;
      if (uni.chooseMessageFile) {
        uni.chooseMessageFile({
          count: 1,
          type: 'file',
          extension: ['xlsx'],
          success: res => this.handleFile(res.tempFiles[0].path),
          fail: () => uni.showToast({ title: '选择失败', icon: 'error' })
        });
      } else {
        const plugin = uni.requireNativePlugin('lemonjk-FileSelect');
        plugin.showPicker(
          {
            mimeType: '*/*',
            utisType: ['public.data'],
            filterConfig: { fileExtension: ['xlsx', 'xls'] }
          },
          result => this.parseXLSX(result.files[0].filePath)
                           .then(this.saveRecipes)
                           .catch(() => uni.showToast({ title: '解析失败', icon: 'error' }))
        );
      }
    },
    handleFile(path) {
      uni.getFileSystemManager().readFile({
        filePath: path,
        encoding: 'base64',
        success: readRes => {
          const buffer = wx.base64ToArrayBuffer(readRes.data);
          this.parseData(buffer);
        },
        fail: () => uni.showToast({ title: '读取失败', icon: 'error' })
      });
    },
    parseData(buffer) {
      const wb = XLSX.read(buffer, { type: 'array' });
      const ws = wb.Sheets[wb.SheetNames[0]];
      // 从第二行开始读取，第一列为食品名称
      const raw = XLSX.utils.sheet_to_json(ws, { header: ['foodName'], range: 1 });
      const list = raw.map(r => ({ foodName: String(r.foodName || '') }));
      this.saveRecipes(list);
    },
    readFile(path) {
      return new Promise((resolve, reject) => {
        plus.io.resolveLocalFileSystemURL(
          path,
          entry => entry.file(
            file => {
              const reader = new plus.io.FileReader();
              reader.readAsDataURL(file);
              reader.onloadend = e => resolve(e.target.result);
              reader.onerror = err => reject(err);
            },
            err => reject(err)
          ),
          err => reject(err)
        );
      });
    },
    async parseXLSX(fp) {
      const b64 = await this.readFile(fp);
      const rawData = b64.replace(/^.*base64,/, '');
      const wb = XLSX.read(rawData, { type: 'base64' });
      const ws = wb.Sheets[wb.SheetNames[0]];
      const raw = XLSX.utils.sheet_to_json(ws, { header: ['foodName'], range: 1 });
      return raw.map(r => ({ foodName: String(r.foodName || '') }));
    },
    saveRecipes(list) {
      this.recipes = list;
      // 获取已有的食谱映射
      const all = uni.getStorageSync('schoolRecipes') || {};
      all[this.selectedSchool] = list;
      uni.setStorageSync('schoolRecipes', all);
	  console.log(all)
      uni.showToast({ title: '食谱上传并保存成功', icon: 'success' });
    }
  }
};
</script>

<style scoped>
.container {
  flex: 1;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}
.nav-bar {
  padding: 12px 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}
.title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}
.selector {
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
}
.picker-inner {
  flex: 1;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.btn {
  margin-left: 12px;
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.btn.confirm {
  background: #50E3C2;
  color: #fff;
}
.upload-section {
  margin-top: 20px;
  align-items: center;
}
.btn.upload {
  background: #4A90E2;
  color: #fff;
}
.card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0,0,0,0.05);
}
.table-wrapper {
  width: 100%;
}
.table-row {
  display: flex;
  padding: 12px 0;
}
.table-row.header {
  background: #f0f4f8;
  font-weight: 600;
}
.odd-row {
  background: #fff;
}
.even-row {
  background: #f9fbfd;
}
.cell {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #555;
}
</style>
