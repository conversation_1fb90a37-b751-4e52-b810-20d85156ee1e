
const { registerUTSInterface, initUTSProxyClass, initUTSProxyFunction, initUTSPackageName, initUTSIndexClassName, initUTSClassName } = uni
const name = 'kakaKPrinter'
const moduleName = '蓝牙 USB Wi-Fi 打印机UTS插件.支持安卓 iOS 支持TSPL CPCL ESC'
const moduleType = ''
const errMsg = ``
const is_uni_modules = true
const pkg = /*#__PURE__*/ initUTSPackageName(name, is_uni_modules)
const cls = /*#__PURE__*/ initUTSIndexClassName(name, is_uni_modules)

const exports = { __esModule: true }



exports.escStringCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escStringCommandByJs', keepAlive: false, params: [{"name":"command","type":"string"}], return: ""})
exports.escBytesCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escBytesCommandByJs', keepAlive: false, params: [{"name":"command","type":"number"}], return: ""})
exports.escInitializePrinter = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escInitializePrinterByJs', keepAlive: false, params: [], return: ""})
exports.escPrintingAreaWidth = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escPrintingAreaWidthByJs', keepAlive: false, params: [{"name":"width","type":"number"}], return: ""})
exports.escLineSpacing = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escLineSpacingByJs', keepAlive: false, params: [{"name":"space","type":"number"}], return: ""})
exports.escNewLine = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escNewLineByJs', keepAlive: false, params: [], return: ""})
exports.escPrintAndFeedLines = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escPrintAndFeedLinesByJs', keepAlive: false, params: [{"name":"feedLines","type":"number"}], return: ""})
exports.escCutPaper = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escCutPaperByJs', keepAlive: false, params: [], return: ""})
exports.escSound = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escSoundByJs', keepAlive: false, params: [{"name":"m","type":"number"},{"name":"time","type":"number"}], return: ""})
exports.escImage = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escImageByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterEscImageParamJSONObject"}], return: ""})
exports.escQRCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escQRCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterEscQRCodeParamJSONObject"}], return: ""})
exports.escTwoText58 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escTwoText58ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterTwoTextJSONObject"}], return: ""})
exports.escThreeText58 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escThreeText58ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterThreeTextJSONObject"}], return: ""})
exports.escFourText58 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escFourText58ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterFourTextJSONObject"}], return: ""})
exports.escTwoText80 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escTwoText80ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterTwoTextJSONObject"}], return: ""})
exports.escThreeText80 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escThreeText80ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterThreeTextJSONObject"}], return: ""})
exports.escFourText80 = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escFourText80ByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterFourTextJSONObject"}], return: ""})
exports.escSetCharcterSize = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escSetCharcterSizeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterCharcterSizeTpyeJSONObject"}], return: ""})
exports.escTurnEmphasizedMode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escTurnEmphasizedModeByJs', keepAlive: false, params: [{"name":"on","type":"boolean"}], return: ""})
exports.escJustification = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escJustificationByJs', keepAlive: false, params: [{"name":"just","type":"Position"}], return: ""})
exports.escText = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escTextByJs', keepAlive: false, params: [{"name":"text","type":"string"}], return: ""})
exports.escBarCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'escBarCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterEscBarCodeParamJSONObject"}], return: ""})
exports.cpclStringCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclStringCommandByJs', keepAlive: false, params: [{"name":"command","type":"string"}], return: ""})
exports.cpclBytesCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclBytesCommandByJs', keepAlive: false, params: [{"name":"command","type":"number"}], return: ""})
exports.cpclSize = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclSizeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterSizeParamJSONObject"}], return: ""})
exports.cpclForm = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclFormByJs', keepAlive: false, params: [], return: ""})
exports.cpclPrint = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclPrintByJs', keepAlive: false, params: [], return: ""})
exports.cpclJustification = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclJustificationByJs', keepAlive: false, params: [{"name":"param","type":"Position"}], return: ""})
exports.cpclBox = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclBoxByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterBoxParamJSONObject"}], return: ""})
exports.cpclImage = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclImageByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterImageParamJSONObject"}], return: ""})
exports.cpclText = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclTextByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterCpclTextParamJSONObject"}], return: ""})
exports.cpclQRCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclQRCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterCpclQRCodeParamJSONObject"}], return: ""})
exports.cpclBarCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclBarCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterCpclBarCodeParamJSONObject"}], return: ""})
exports.cpclLine = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'cpclLineByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterBoxParamJSONObject"}], return: ""})
exports.tscStringCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscStringCommandByJs', keepAlive: false, params: [{"name":"command","type":"string"}], return: ""})
exports.tscBytesCommand = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscBytesCommandByJs', keepAlive: false, params: [{"name":"command","type":"number"}], return: ""})
exports.tscSelfTest = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscSelfTestByJs', keepAlive: false, params: [], return: ""})
exports.tscSpeed = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscSpeedByJs', keepAlive: false, params: [{"name":"speed","type":"number"}], return: ""})
exports.tscDensity = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscDensityByJs', keepAlive: false, params: [{"name":"density","type":"number"}], return: ""})
exports.tscGap = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscGapByJs', keepAlive: false, params: [{"name":"gap","type":"number"}], return: ""})
exports.tscSize = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscSizeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterSizeParamJSONObject"}], return: ""})
exports.tscCls = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscClsByJs', keepAlive: false, params: [], return: ""})
exports.tscPrint = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscPrintByJs', keepAlive: false, params: [{"name":"copies","type":"number"}], return: ""})
exports.tscDirection = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscDirectionByJs', keepAlive: false, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterDirectionParamJSONObject"}], return: ""})
exports.tscImage = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscImageByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterImageParamJSONObject"}], return: ""})
exports.tscBox = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscBoxByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterBoxParamJSONObject"}], return: ""})
exports.tscBar = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscBarByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterRectParamJSONObject"}], return: ""})
exports.tscBarCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscBarCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterBarCodeParamJSONObject"}], return: ""})
exports.tscQRCode = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscQRCodeByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterQRCodeParamJSONObject"}], return: ""})
exports.tscText = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscTextByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterTextParamJSONObject"}], return: ""})
exports.tscEnableGap = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscEnableGapByJs', keepAlive: false, params: [{"name":"enable","type":"boolean"}], return: ""})
exports.tscBlock = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscBlockByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterBlockParamJSONObject"}], return: ""})
exports.tscReverse = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'tscReverseByJs', keepAlive: false, params: [{"name":"param","type":"UTSSDKModulesKakaKPrinterRectParamJSONObject"}], return: ""})
exports.onBlueStateChange = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onBlueStateChangeByJs', keepAlive: true, params: [{"name":"callback","type":"UTSCallback"}], return: ""})
exports.onConnectStateChange = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onConnectStateChangeByJs', keepAlive: true, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterConnectOptionsJSONObject"}], return: ""})
exports.onDataReceive = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onDataReceiveByJs', keepAlive: true, params: [{"name":"callback","type":"UTSCallback"}], return: ""})
exports.onWriteComplete = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onWriteCompleteByJs', keepAlive: true, params: [{"name":"callBack","type":"UTSCallback"}], return: ""})
exports.startScan = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'startScanByJs', keepAlive: true, params: [{"name":"callback","type":"UTSCallback"}], return: ""})
exports.stopScan = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'stopScanByJs', keepAlive: false, params: [], return: ""})
exports.connect = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'connectByJs', keepAlive: false, params: [{"name":"deviceId","type":"string"}], return: ""})
exports.disconnect = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'disconnectByJs', keepAlive: false, params: [], return: ""})
exports.writeData = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'writeDataByJs', keepAlive: false, params: [], return: ""})
exports.isConnect = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'isConnectByJs', keepAlive: false, params: [], return: ""})
exports.onEthernetConnectStateChange = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onEthernetConnectStateChangeByJs', keepAlive: true, params: [{"name":"options","type":"UTSSDKModulesKakaKPrinterEthernetConnectOptionsJSONObject"}], return: ""})
exports.onEthernetDataReceive = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onEthernetDataReceiveByJs', keepAlive: true, params: [{"name":"callback","type":"UTSCallback"}], return: ""})
exports.connectEthernet = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'connectEthernetByJs', keepAlive: true, params: [{"name":"ip","type":"string"},{"name":"port","type":"string"}], return: ""})
exports.disConnectEthernet = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'disConnectEthernetByJs', keepAlive: false, params: [], return: ""})
exports.writeDataEthernet = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'writeDataEthernetByJs', keepAlive: true, params: [], return: ""})
exports.iOSPackageSize = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'iOSPackageSizeByJs', keepAlive: false, params: [{"name":"size","type":"number"}], return: ""})
exports.getUsbDeviceList = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'getUsbDeviceListByJs', keepAlive: true, params: [{"name":"callBack","type":""}], return: ""})
exports.onUSBConnectStateChange = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'onUSBConnectStateChangeByJs', keepAlive: true, params: [{"name":"options","type":""}], return: ""})
exports.connectUSB = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'connectUSBByJs', keepAlive: true, params: [{"name":"name","type":"string"}], return: ""})
exports.disConnectUSB = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'disConnectUSBByJs', keepAlive: false, params: [], return: ""})
exports.writeDataUSB = /*#__PURE__*/ initUTSProxyFunction(false, { moduleName, moduleType, errMsg, main: true, package: pkg, class: cls, name: 'writeDataUSBByJs', keepAlive: true, params: [], return: ""})
uni.registerUTSPlugin('uni_modules/kaka-KPrinter', exports)
