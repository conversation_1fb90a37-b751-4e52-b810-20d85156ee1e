{"id": "kaka-KPrinter", "displayName": "蓝牙 USB Wi-Fi 打印机UTS插件.支持安卓 iOS 支持TSPL CPCL ESC", "version": "1.3.5", "description": "蓝牙 USB Wi-Fi 打印机插件，支持安卓 iOS TSC CPCL ESC，适用于佳博、启锐、芯烨、汉印、得力、容大、匠辛、快麦、印向未来...支持小票&标签打印", "keywords": ["蓝牙打印", "热敏打印", "标签", "小票", "打印机"], "repository": "", "engines": {"HBuilderX": "^4.45"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "299.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "932102886"}, "declaration": {"ads": "无", "data": "无", "permissions": "安卓：\n    <uses-permission android:name=\"android.permission.BLUETOOTH\"/>\n    <uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\" />\n    <uses-permission android:name=\"android.permission.BLUETOOTH_CONNECT\"/>\n    <uses-permission android:name=\"android.permission.BLUETOOTH_SCAN\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>\n    <uses-permission android:name=\"android.permission.BLUETOOTH_SCAN\" />\n\n\niOS：\n\t<key>NSBluetoothAlwaysUsageDescription</key>\n\t<string>开启蓝牙</string>\n\t<key>NSBluetoothPeripheralUsageDescription</key>\n\t<string>蓝牙</string>\n\t<key>UIBackgroundModes</key>\n\t<array>\n\t\t<string>bluetooth-central</string>\n\t</array>"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": {"minVersion": "21"}, "app-ios": {}, "app-harmony": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}