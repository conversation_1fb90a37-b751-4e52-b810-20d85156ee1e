
.container[data-v-6d1e169e] {
  flex: 1;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}
.nav-bar[data-v-6d1e169e] {
  padding: 12px 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}
.title[data-v-6d1e169e] {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}
.selector[data-v-6d1e169e] {
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
}
.picker-inner[data-v-6d1e169e] {
  flex: 1;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.btn[data-v-6d1e169e] {
  margin-left: 12px;
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.btn.confirm[data-v-6d1e169e] {
  background: #50E3C2;
  color: #fff;
}
.upload-section[data-v-6d1e169e] {
  margin-top: 20px;
  align-items: center;
}
.btn.upload[data-v-6d1e169e] {
  background: #4A90E2;
  color: #fff;
}
.card[data-v-6d1e169e] {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0,0,0,0.05);
}
.table-wrapper[data-v-6d1e169e] {
  width: 100%;
}
.table-row[data-v-6d1e169e] {
  display: flex;
  padding: 12px 0;
}
.table-row.header[data-v-6d1e169e] {
  background: #f0f4f8;
  font-weight: 600;
}
.odd-row[data-v-6d1e169e] {
  background: #fff;
}
.even-row[data-v-6d1e169e] {
  background: #f9fbfd;
}
.cell[data-v-6d1e169e] {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #555;
}
