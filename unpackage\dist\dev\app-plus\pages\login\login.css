@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 导入字体 */
/* 全局样式 */
*[data-v-e4e4508d] {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', system-ui, sans-serif;
}
/* 主容器 */
.login-container[data-v-e4e4508d] {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #D4C8BE 0%, #E0D6CC 25%, #E8E2DB 50%, #F0EDE8 75%, #F5F3F0 100%);
  overflow: hidden;
}
/* 背景渐变 - 增强对比度 */
.background-gradient[data-v-e4e4508d] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(168, 230, 207, 0.35) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 184, 163, 0.35) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(136, 201, 153, 0.25) 0%, transparent 70%),
    radial-gradient(circle at 10% 10%, rgba(119, 179, 153, 0.2) 0%, transparent 50%);
  z-index: 1;
}
/* 登录卡片 - 增大尺寸 */
.login-card[data-v-e4e4508d] {
  position: relative;
  z-index: 2;
  width: 95%;
  max-width: 600px;
  min-height: 650px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 2rem;
  padding: 3.5rem 3rem;
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}
.login-card[data-v-e4e4508d]:hover {
  transform: translateY(-6px);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.25),
    0 12px 32px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.9);
}
/* 卡片头部 - 增大尺寸 */
.card-header[data-v-e4e4508d] {
  text-align: center;
  margin-bottom: 3rem;
}
.logo-area[data-v-e4e4508d] {
  margin-bottom: 1.5rem;
}
.logo-icon[data-v-e4e4508d] {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8px 32px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
  position: relative;
}
.logo-icon[data-v-e4e4508d]::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border-radius: 50%;
}
.logo-text[data-v-e4e4508d] {
  font-size: 2.25rem;
  font-weight: 700;
  color: #271930;
  z-index: 1;
  position: relative;
}
.app-title[data-v-e4e4508d] {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #271930;
  margin-bottom: 0.75rem;
}
.app-subtitle[data-v-e4e4508d] {
  display: block;
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 400;
}
/* 表单区域 - 增大间距 */
.form-section[data-v-e4e4508d] {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}
.input-group[data-v-e4e4508d] {
  position: relative;
}
.input-field[data-v-e4e4508d] {
  position: relative;
  background: rgba(245, 243, 240, 0.9);
  border-radius: 1rem;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 70px;
}
.input-field[data-v-e4e4508d]:focus-within {
  border-color: #A8E6CF;
  box-shadow: 0 0 0 6px rgba(168, 230, 207, 0.15);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.95);
}
.input-icon[data-v-e4e4508d] {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}
.icon[data-v-e4e4508d] {
  font-size: 1.5rem;
  opacity: 0.7;
}
.form-input[data-v-e4e4508d] {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 4rem;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1.25rem;
  color: #271930;
  font-weight: 500;
  min-height: 70px;
}
.form-input[data-v-e4e4508d]::-webkit-input-placeholder {
  color: #7FB3B3;
  font-weight: 400;
  font-size: 1.125rem;
}
.form-input[data-v-e4e4508d]::placeholder {
  color: #7FB3B3;
  font-weight: 400;
  font-size: 1.125rem;
}
/* 选项行 - 增大尺寸 */
.options-row[data-v-e4e4508d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0 2rem 0;
  padding: 0 0.5rem;
}
.checkbox-group[data-v-e4e4508d] {
  display: flex;
  align-items: center;
}
.checkbox-label[data-v-e4e4508d] {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 0.75rem;
}
.checkbox-input[data-v-e4e4508d] {
  display: none;
}
.checkbox-custom[data-v-e4e4508d] {
  width: 24px;
  height: 24px;
  border: 3px solid #A8E6CF;
  border-radius: 0.5rem;
  background: rgba(168, 230, 207, 0.15);
  position: relative;
  transition: all 0.2s ease;
}
.checkbox-input:checked + .checkbox-custom[data-v-e4e4508d] {
  background: #A8E6CF;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.15);
}
.checkbox-input:checked + .checkbox-custom[data-v-e4e4508d]::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #271930;
  font-size: 1rem;
  font-weight: 700;
}
.checkbox-text[data-v-e4e4508d] {
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 500;
}
.forgot-link[data-v-e4e4508d] {
  font-size: 1.125rem;
  color: #FF9B85;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
  padding: 0.5rem;
}
.forgot-link[data-v-e4e4508d]:active {
  color: #FFB8A3;
}
/* 登录按钮 - 增大尺寸 */
.login-button[data-v-e4e4508d] {
  background: linear-gradient(135deg, #A8E6CF 0%, #88C999 100%);
  border: none;
  border-radius: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.375rem;
  font-weight: 700;
  color: #271930;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 
    0 8px 32px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  margin-top: 1rem;
}
.login-button[data-v-e4e4508d]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}
.login-button[data-v-e4e4508d]:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 48px rgba(168, 230, 207, 0.5),
    inset 0 4px 8px rgba(255, 255, 255, 0.5);
}
.login-button[data-v-e4e4508d]:hover::before {
  left: 100%;
}
.login-button[data-v-e4e4508d]:active {
  transform: translateY(0);
  box-shadow: 
    0 4px 16px rgba(168, 230, 207, 0.4),
    inset 0 4px 8px rgba(0, 0, 0, 0.15);
}
.button-text[data-v-e4e4508d] {
  position: relative;
  z-index: 1;
}
/* 注册区域 - 增大尺寸 */
.register-section[data-v-e4e4508d] {
  text-align: center;
  margin-top: 2rem;
}
.register-text[data-v-e4e4508d] {
  font-size: 1.125rem;
  color: #7FB3B3;
  font-weight: 500;
}
.register-link[data-v-e4e4508d] {
  font-size: 1.125rem;
  color: #FF9B85;
  font-weight: 600;
  text-decoration: none;
  margin-left: 0.5rem;
  transition: color 0.2s ease;
  padding: 0.5rem;
}
.register-link[data-v-e4e4508d]:active {
  color: #FFB8A3;
}
/* 平板优化 */
@media (min-width: 768px) and (max-width: 1024px) {
.login-card[data-v-e4e4508d] {
    width: 90%;
    max-width: 700px;
    min-height: 700px;
    padding: 4rem 3.5rem;
}
.logo-icon[data-v-e4e4508d] {
    width: 140px;
    height: 140px;
}
.logo-text[data-v-e4e4508d] {
    font-size: 2.5rem;
}
.app-title[data-v-e4e4508d] {
    font-size: 2.75rem;
}
.app-subtitle[data-v-e4e4508d] {
    font-size: 1.25rem;
}
.form-input[data-v-e4e4508d] {
    font-size: 1.375rem;
    padding: 1.5rem 1.5rem 1.5rem 4.5rem;
    min-height: 80px;
}
.form-input[data-v-e4e4508d]::-webkit-input-placeholder {
    font-size: 1.25rem;
}
.form-input[data-v-e4e4508d]::placeholder {
    font-size: 1.25rem;
}
.icon[data-v-e4e4508d] {
    font-size: 1.75rem;
}
.login-button[data-v-e4e4508d] {
    padding: 1.75rem 3.5rem;
    font-size: 1.5rem;
    min-height: 90px;
}
.checkbox-custom[data-v-e4e4508d] {
    width: 28px;
    height: 28px;
}
.checkbox-text[data-v-e4e4508d],
  .forgot-link[data-v-e4e4508d],
  .register-text[data-v-e4e4508d],
  .register-link[data-v-e4e4508d] {
    font-size: 1.25rem;
}
}
/* 大屏平板优化 */
@media (min-width: 1024px) {
.login-card[data-v-e4e4508d] {
    width: 85%;
    max-width: 800px;
    min-height: 750px;
    padding: 4.5rem 4rem;
}
.logo-icon[data-v-e4e4508d] {
    width: 160px;
    height: 160px;
}
.logo-text[data-v-e4e4508d] {
    font-size: 3rem;
}
.app-title[data-v-e4e4508d] {
    font-size: 3rem;
}
.app-subtitle[data-v-e4e4508d] {
    font-size: 1.375rem;
}
.form-input[data-v-e4e4508d] {
    font-size: 1.5rem;
    padding: 1.75rem 1.75rem 1.75rem 5rem;
    min-height: 90px;
}
.form-input[data-v-e4e4508d]::-webkit-input-placeholder {
    font-size: 1.375rem;
}
.form-input[data-v-e4e4508d]::placeholder {
    font-size: 1.375rem;
}
.icon[data-v-e4e4508d] {
    font-size: 2rem;
}
.login-button[data-v-e4e4508d] {
    padding: 2rem 4rem;
    font-size: 1.625rem;
    min-height: 100px;
}
.checkbox-custom[data-v-e4e4508d] {
    width: 32px;
    height: 32px;
}
.checkbox-text[data-v-e4e4508d],
  .forgot-link[data-v-e4e4508d],
  .register-text[data-v-e4e4508d],
  .register-link[data-v-e4e4508d] {
    font-size: 1.375rem;
}
}
