{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__E860387", "name": "sample_1", "version": {"name": "1.0.0", "code": "100"}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"camera": {"__plugin_info__": {"name": "camera", "description": "原生 UVC 摄像头拍照插件", "platforms": "Android", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}, "package": "com.example.camera", "class": "CameraModule"}}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nativePlugins": {"Fvv-UniSerialPort": {"__plugin_info__": {"name": "UniSerialPort", "description": "安卓串口通讯", "platforms": "Android", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}, "lemonjk-FileSelect": {"appid_android": "", "__plugin_info__": {"name": "FileSelect", "description": "文件选取插件", "platforms": "Android,iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {"appid_android": {"des": "请填写你当前应用的包名，必需填写，否则可能无法使用。（在菜单中->发行->原生App-云打包中可以查看包名信息）", "key": "", "value": ""}}}}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.66", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}}, "launch_path": "__uniappview.html"}