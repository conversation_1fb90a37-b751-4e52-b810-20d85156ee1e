
/* 蓝牙是否开启函数定义 */
export type BlueIsOn = (isOn : boolean) => void
/* 设备函数定义 */
export type DeviceBack = (device : BlueDevice) => void
/* 消息函数定义 */
export type MessageBack = (msg : string) => void
/* 设备数据返回函数定义 */
export type DataBack = (dataStr : Array<number>) => void
/* 写入是否完成函数定义 */
export type WriteComplete = (isComplete : boolean) => void

export type Position = "left" | "center" | "right";
export type Rotation = 0 | 90 | 180 | 270;

// 蓝牙设备信息
export type BlueDevice = {
	name : string
	deviceId : string
	isConnect : boolean,
	paired ?: boolean		// 是否配对过，仅用于安卓设备扫描callback
}

// 连接参数
export type ConnectOptions = {
	onSuccess : DeviceBack
	onDisconnect : DeviceBack
	onFail : MessageBack
}

/* ----------- 公共 Param Type -----------*/
export type SizeParam = {
	width : number
	height : number
	copies ?: number // cpcl 中使用
}

/* ----------- tspl Param Type -----------*/
export type ImageParam = {
	x : number
	y : number
	base64Str : string
	width ?: number		 // 图片打印的宽度(缩放)
	compress ?: boolean  // 是否压缩，需打印机支持，一般不传这参数
	dithering ?: boolean // 是否添加图片抖动效果
}

export type RectParam = {
	x : number
	y : number
	width : number
	height : number
}

export type BarCodeParam = {
	x : number
	y : number
	height : number
	content : string
	codeType : string
	readable : boolean
}

export type QRCodeParam = {
	x : number
	y : number
	cellWidth : number
	rotation : number
	content : string
}

export type TextParam = {
	x : number
	y : number
	rotation : number
	xScal : number
	yScal : number
	font : string
	content : string
}

export type BlockParam = {
	x : number
	y : number
	width : number
	height : number
	font : string
	rotation : Rotation
	xScal : number
	yScal : number
	content : string
}

export type BoxParam = {
	x : number
	y : number
	xend : number
	yend : number
	thickness : number
}

export type DirectionParam = {
	directionReverse : boolean,	// 打印方向是否反向, false: 不反向, true: 反向
	isMirror : boolean			// 设置镜像，false: 不镜像, true: 镜像
}

/* ----------- Cpcl Param Type -----------*/
export type CpclTextParam = {
	x : number
	y : number
	font : string
	xScal : number
	yScal : number
	content : string

	bold ?: boolean
	rotation ?: number
}

export type CpclBarCodeParam = {
	x : number
	y : number
	width : number
	height : number
	vertical : boolean
	codeType : string
	content : string
}

export type CpclQRCodeParam = {
	x : number
	y : number
	cellWidth : number
	content : string
}

/ ************* BLUE指令接口 ************* /
/**
 * 蓝牙开启关闭监听
 * @param callback 蓝牙状态回调
 */
export declare function onBlueStateChange(callback : BlueIsOn) : void;

/**
 * 蓝牙连接状态监听
 * @param options 见：ConnectOptions，连接、断开、失败回调
 */
export declare function onConnectStateChange(options : ConnectOptions) : void;
/**
 * 蓝牙数据回传监听
 * @param callback 数据回传回调
 */
export declare function onDataReceive(callback : DataBack) : void;

/**
 * 数据写入是否完成监听
 * @param callback 写入是否完成回调
 */
export declare function onWriteComplete(callBack : WriteComplete) : void;

/**
 * 扫描设备
 * @param callback 发现设备回调
 */
export declare function startScan(callback : DeviceBack) : void;

// 停止扫描设备
export declare function stopScan() : void;

/**
 * 连接设备
 * @param deviceId 设备 id
 */
export declare function connect(deviceId : string) : void;

// 断开设备
export declare function disconnect() : void;

// 写入数据
export declare function writeData() : void;

// 设置iOS数据分包大小, 默认 130 字节一包
export declare function iOSPackageSize(size : number) : void;

/ ************* USB 接口 ************* /
// USB设备信息
export type USBDevice = {
	deviceName : string
	deviceId : string
	productName : string
	productId : string	// pid
	vendorId : string	// vid
	manufacturerName : string
	isConnect : boolean
}

/* 设备函数定义 */
export type USBDeviceBack = (device : USBDevice) => void;
export type VoidBack = () => void;

// 连接参数
export type USBConnectOptions = {
	usbDeviceAttached : USBDeviceBack
	onSuccess : USBDeviceBack
	onDisconnect : VoidBack
	onFail : MessageBack
}

export declare function getUsbDeviceList(callBack : USBDeviceBack) : void

export declare function onUSBConnectStateChange(options : USBConnectOptions) : void;

export declare function onUSBDataReceive(callback : DataBack) : void;

export declare function connectUSB(name : string) : void;

export declare function disConnectUSB() : void;

export declare function writeDataUSB() : void;

/ ************* 以太网 接口 ************* /
// 连接参数
export type EthernetConnectOptions = {
	onSuccess : VoidBack
	onDisconnect : VoidBack
	onFail : MessageBack
}

export declare function onEthernetConnectStateChange(options : EthernetConnectOptions) : void;

export declare function onEthernetDataReceive(callback : DataBack) : void;

export declare function connectEthernet(ip : string, port : string) : void;

export declare function disConnectEthernet() : void;

export declare function writeDataEthernet() : void;


/ ************* TSPL指令接口 ************* /
// tsc传字符串指令
export declare function tscStringCommand(command : string) : void;
// tsc传字节数组指令
export declare function tscBytesCommand(command : number[]) : void;

export declare function tscSize(param : SizeParam) : void;

export declare function tscCls() : void;

export declare function tscPrint(copies : number) : void;

export declare function tscDirection(param : DirectionParam) : void;

export declare function tscSelfTest() : void;

export declare function tscSpeed(speed : number) : void;

export declare function tscDensity(density : number) : void;

export declare function tscEnableGap(enable : boolean) : void;

export declare function tscGap(gap : number) : void;

export declare function tscImage(param : ImageParam) : void;

export declare function tscBox(param : BoxParam) : void;

export declare function tscBar(param : RectParam) : void;

export declare function tscBarCode(param : BarCodeParam) : void;

export declare function tscQRCode(param : QRCodeParam) : void;
// 打印一个文本
export declare function tscText(param : TextParam) : void;
// 打印一个段落
export declare function tscBlock(options : BlockParam) : void;
// 反白指定的区域
export declare function tscReverse(options : RectParam) : void;

/ ************* CPCL指令接口 ************* /
// cpcl传字符串指令
export declare function cpclStringCommand(command : string) : void;
// cpcl传字节数组指令
export declare function cpclBytesCommand(command : number[]) : void;

export declare function cpclSize(param : SizeParam) : void;

export declare function cpclForm() : void;

export declare function cpclPrint() : void;

export declare function cpclJustification(param : Position) : void;

export declare function cpclBox(param : BoxParam) : void;

export declare function cpclImage(param : ImageParam) : void;

export declare function cpclText(param : CpclTextParam) : void;

export declare function cpclQRCode(param : CpclQRCodeParam) : void;

// 以指定的宽和高横向或纵向打印条码
export declare function cpclBarCode(param : CpclBarCodeParam) : void;

// 用于绘制任何长度、宽度和方向的线条
export declare function cpclLine(param : BoxParam) : void;

/ ************* ESC指令接口 ************* /
export type EscImageParam = {
	base64Str : string
	dithering ?: boolean // 是否添加图片抖动效果
}

export type EscQRCodeParam = {
	size : number 		// 1 ≤ size ≤ 16
	content : string
}

export type EscBarCodeParam = {
	width : number		// 1 ≤ width ≤ 6
	height : number		// 1 ≤ height ≤ 255
	hri : number  		// 选择HRI字符的打印位置 可识别字符位置，0 不打印 , 1 条码上方, 2 条码下方, 3 条码上、下方都打印
	codeType : string 	// 128 39 93 ITF UPCA UPCE CODABAR EAN8 EAN13
	content : string
}

export type TwoText = {
	left : string
	right : string
}

export type ThreeText = {
	left : string
	middle : string
	right : string
}

export type FourText = {
	one : string
	two : string
	three : string
	four : string
}

export type CharcterSizeTpye = {
	xScal : number 	// 宽度缩放比例
	yScal : number 	// 高度缩放比例
}

// esc传字符串指令
export declare function escStringCommand(command : string) : void;
// esc传字节数组指令
export declare function escBytesCommand(command : number[]) : void;
// 打印机初始化，必须是第一个打印命令
export declare function escInitializePrinter() : void;
// 设置打印宽度
export declare function escPrintingAreaWidth(width : number) : void;
// 设置行间距
export declare function escLineSpacing(space : number) : void;
// 换行
export declare function escNewLine() : void;
// 打印并且走纸多少行，打印完内容后发送
export declare function escPrintAndFeedLines(feedLines : number) : void;
// 切纸 
export declare function escCutPaper() : void;
// 蜂鸣器  m  报警灯和鸣叫次数  time  时间
export declare function escSound(m : number, time : number) : void;
// 设置对齐方式,  just "left" | "center" | "right"
export declare function escJustification(just : Position) : void;
// 设置字符放大
export declare function escSetCharcterSize(param : CharcterSizeTpye) : void;
// 切换强调模式/文字是否加粗
export declare function escTurnEmphasizedMode(on : boolean) : void;
// 插入文字
export declare function escText(text : string) : void;
// 打印光栅位图  base64Str 图片base64数据
export declare function escImage(param : EscImageParam) : void;
// 二维码
export declare function escQRCode(param : EscQRCodeParam) : void;
// code 128
export declare function escBarCode(param : EscBarCodeParam) : void;
// 菜单文本生成接口
export declare function escTwoText58(options : TwoText) : string;
export declare function escThreeText58(options : ThreeText) : string;
export declare function escFourText58(options : FourText) : string;
export declare function escTwoText80(options : TwoText) : string;
export declare function escThreeText80(options : ThreeText) : string;
export declare function escFourText80(options : FourText) : string;

/ ************* 辅助功能 ************* /