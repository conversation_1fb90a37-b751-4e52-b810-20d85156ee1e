//
//  TscCmd.h
//  KPrinter
//
//  Created by kaka on 12/15/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TscCmd : NSObject

/// 测试
//- (void)testCommand;

- (void)addCommand:(NSString *)command;

/// 打印自检页，打印测试页
- (void)selfTest;

/// 清除打印缓冲区
- (void)cls;

/// 设置标签间隙尺寸 单位mm
- (void)gap:(NSString *)str;

/// 设置打印速度
- (void)speed:(NSString *)str;

/// 设置打印浓度
- (void)density:(NSString *)str;

/// 设置标签尺寸的宽和高
- (void)size:(NSString *)str;

/// 执行打印
- (void)print:(NSString *)str;

/// 设置打印方向和镜像打印
- (void)direction:(NSString *)str;

/// 图片
- (void)image:(NSString *)str;

/// 文本
- (void)text:(NSString *)str;

/// 条码
- (void)barCode:(NSString *)str;

/// 二维码
- (void)qrCode:(NSString *)str;

/// 在标签上绘制黑块，画线
- (void)bar:(NSString *)str;

/// 绘制矩形
- (void)box:(NSString *)str;

// 连续纸模式
- (void)enableGap:(BOOL)enable;

/// 段落
- (void)block:(NSString *)str;

/// 反白指定的区域
- (void)reverse:(NSString *)str;

@end

NS_ASSUME_NONNULL_END
